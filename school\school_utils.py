from django.db.models.aggregates import Sum
from django.db.models import Q
from . import models
from main import utils as main_utils

# Set all columns to -1 and make sure cols values are greater 
STUDENTS_IMPORT_COLS = {
    'matricule': -1, 'nom': -1, 'prenoms': -1, 'sexe': -1, 'niveau': -1,
    'jour': -1, 'mois': -1, 'annee': -1, 
    'localite': -1, 'contact': -1, 'pere': -1, 
    'contact_pere': -1, 'mere': -1, 'contact_mere': -1,
    'statut': -1, 'qualite': -1, 'lv2': -1, 
    'nom_complet_ar': -1, 'niveau_ar': -1, 'localite_ar': -1,
}

REQUIRED_COLS = {
    'matricule': -1, 'nom': -1, 'prenoms': -1, 'sexe': -1, 'niveau': -1,
}

def get_current_year():
    """ Returns the year with active=True"""
    return models.Year.objects.filter(active=True).first()

def get_next_level(level):
    qs = models.GenericLevel.objects.filter(order__gt=level.order).order_by('order')
    if qs.exists():
        return qs.first()
    return level

def get_highest_level(level1, level2):
    return level1 if level1.order > level2.order else level2

def get_highest_level_as_dict(level1, level2):
    return { 'level':level1, 'index':1 } if level1.order > level2.order \
        else {'level':level2, 'index':2}

def get_lowest_level(level1, level2):
    return level1 if level1.order < level2.order else level2

def get_lowest_level_as_dict(level1, level2):
    return { 'level':level1, 'index':1 } if level1.order < level2.order \
        else {'level':level2, 'index':2}

def __get_level_pricing(user, level_id, status=None, education=None):
    queryset = models.GenericLevel.objects.filter(id=level_id)
    generic_level = queryset.first()

    queryset = models.LevelPricing.objects \
                .for_school(user, education=education) \
                .filter(generic_level=generic_level)
    if education == main_utils.EDUCATION_FRENCH and status \
        and generic_level.cycle == generic_level.CYCLE_SECONDARY:
        queryset = queryset.filter(Q(student_status=status) | ~Q(student_status=status))
    return queryset.first()

def get_fees(user, level_fr, level_ar=None, status=None, with_annexe=False):
    school = user.school
    pricing = None
    if school.pricing_option == school.PRICING_BY_FRENCH:
        education = main_utils.EDUCATION_FRENCH
        pricing = __get_level_pricing(user, level_fr, status, education)
        result = {
            'inscription': pricing.inscription if pricing else 0, 
            'scolarite': pricing.scolarite if pricing else 0,
            'annexe': pricing.annexe if pricing else 0,
        }
    
    elif school.pricing_option == school.PRICING_BY_ARABIC:
        education = main_utils.EDUCATION_ARABIC
        pricing = __get_level_pricing(user, level_ar, education=education)
        result = {
            'inscription': pricing.inscription if pricing else 0, 
            'scolarite': pricing.scolarite if pricing else 0,
            'annexe': pricing.annexe if pricing else 0
        }
    elif school.pricing_option == school.PRICING_BY_BOTH:
        pricing_fr = __get_level_pricing(user, level_fr, status, 
                     education=main_utils.EDUCATION_FRENCH)
        pricing_ar = None

        if level_ar:
            pricing_ar = __get_level_pricing(user,level_ar,
                        education=main_utils.EDUCATION_ARABIC)
        inscription = 0
        scolarite = 0
        annexe = 0
        if pricing_fr:
            inscription = pricing_fr.inscription
            scolarite = pricing_fr.scolarite
            if with_annexe:
                annexe = pricing_fr.annexe

        if pricing_ar:
            inscription += pricing_ar.inscription
            scolarite += pricing_ar.scolarite
            if with_annexe:
                annexe += pricing_ar.annexe

        return {
            'inscription': inscription, 
            'scolarite': scolarite,
            'annexe': annexe
        }
    elif school.pricing_option == school.PRICING_BY_HIGHEST_LEVEL:
        # Get levels and generic_levels
        generic_level_fr = models.GenericLevel.objects \
                           .filter(id=level_fr).first()
        generic_level_ar = models.GenericLevel.objects \
                           .filter(id=level_ar).first()

        # Get highest level and its index
        highest_dict = get_highest_level_as_dict(
            generic_level_fr, generic_level_ar)
        highest = highest_dict['level']

        education = None
        if highest_dict['index'] == 1:
            education = main_utils.EDUCATION_FRENCH
        else:
            education = main_utils.EDUCATION_ARABIC

        pricing = models.LevelPricing.objects \
                .for_school(user, education=education, status=status) \
                .filter(generic_level=highest) \
                .first()
        result = {
            'inscription': pricing.inscription if pricing else 0,
            'scolarite': pricing.scolarite if pricing else 0,
            'annexe': pricing.annexe if pricing else 0
        }
    elif school.pricing_option == school.PRICING_BY_LOWEST_LEVEL:
        # Get levels and generic_levels
        generic_level_fr = models.GenericLevel.objects \
                           .filter(id=level_fr).first()
        generic_level_ar = models.GenericLevel.objects \
                           .filter(id=level_ar).first()

        # Get lowest level and its index
        lowest_dict = get_lowest_level_as_dict(
            generic_level_fr, generic_level_ar)
        lowest = lowest_dict['level']

        education = None
        if lowest_dict['index'] == 1:
            education = main_utils.EDUCATION_FRENCH
        else:
            education = main_utils.EDUCATION_ARABIC

        pricing = models.LevelPricing.objects \
                .for_school(user, education=education, status=status) \
                .filter(generic_level=lowest) \
                .first()
        result = {
            'inscription': pricing.inscription if pricing else 0,
            'scolarite': pricing.scolarite if pricing else 0,
            'annexe': pricing.annexe if pricing else 0
        }
    # if with_annexe:
    #     if pricing and pricing.levelextraprice_set.exists():
    #         result['annexe'] = pricing.levelextraprice_set\
    #             .aggregate(total=Sum('price'))['total']
    #     else:
    #         result['annexe'] = 0
    return result


def get_session_year_or_current_year(request):
    session_year = main_utils.get_session_year_name(request)
    queryset = models.Year.objects.filter(name=session_year)
    if queryset.exists():
        return queryset.first()
    return get_current_year()