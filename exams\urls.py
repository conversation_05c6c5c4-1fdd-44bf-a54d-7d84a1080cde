from django.urls import path
from . import views

app_name = 'exams'
urlpatterns = [
    path('periodes/', views.SchoolTermsListView.as_view(), name='terms'),
    path('periodes/<int:pk>/editer/', views.SchoolTermUpdateView.as_view(), name='term_edit'),
    path('periodes/verrouillage_notes/', views.MarksLockView.as_view(), name='lock_marks'),
    path('periodes/<int:pk>/changer-statut/',
         views.SchoolTermStatusToggleView.as_view(), name='term-status'),
]

subjects_url = 'matieres'
urlpatterns += [
    path(subjects_url, views.SubjectsListView.as_view(), name='subjects'),
    path(f'{subjects_url}/ajouter/', views.SubjectCreateView.as_view(), name='subject_add'),
    path(f'{subjects_url}/par-niveau/', views.LevelSubjectsListView.as_view(), name='level_subjects'),
    path(f'{subjects_url}/par-niveau/<str:level>/<str:lang>/ajouter/', views.LevelSubjectCreateView.as_view(), name='level_subject_add'),
    path(f'{subjects_url}/par-niveau/<int:pk>/editer/', views.LevelSubjectsUpdateView.as_view(), name='level_subject_edit'),
    path(f'{subjects_url}/par-niveau/reorganiser/', views.level_subject_order_change_view, name='level_subject_reorder'),
    path(f'{subjects_url}/par-niveau/<int:pk>/changer-statut/', views.LevelSubjectsStatusChangeView.as_view(), name='level_subject_status'),
]

grades_url = 'notes'
urlpatterns += [
    path(f'{grades_url}/', views.GradesNavView.as_view(), name='grades'),
    path(f'{grades_url}/par-matiere/', views.GradesBySubjectView.as_view(),
         name='grade_by_subject'),
    path(f'{grades_url}/par-matiere/editer/', views.grade_edit_view, name='grade_edit'),
    path(f'{grades_url}/par-matiere/exporter/', views.grade_by_subject_export,
         name='subject_grade_export'),
    path(f'{grades_url}/par-matiere/importer/', views.grade_by_subject_import_view,
         name='subject_grade_import'),
    path(f'{grades_url}/tout/', views.AllGradesView.as_view(), name='all_grades'),
    path(f'{grades_url}/importer/', views.all_grades_import_view2, name='all_grades_import'),
    path(f'{grades_url}/actions/', views.grade_actions, name='grade_action'),
    path(f'{grades_url}/exporter/', views.grade_export, name='grade_export'),
    path(f'{grades_url}/edition-multiple/', views.grade_batch_edit_view, name='grade_batch_edit'),
    path(f'{grades_url}/fiche-de-notation-personnalisees/', views.customized_marking_sheet, name='custom_marking_sheet'),
]

urlpatterns += [
    path('resultats/', views.ResultsNavView.as_view(), name='results'),
    path('resultats/par-periode/', views.period_results, name='period_results'),
    path('resultats/statistiques/', views.results_by_cycle_view, name='results_by_cycle'),
    path('resultats/calcul-moyennes/',
         views.compute_period_results_view,
         name='compute_term_results'),
    path('results/calculs/', views.level_resuls_update,
         name='results_update'),
     path('resultats_pdf/', views.results_pdf_view, name='period_results_pdf'),
     path('resultats/annuels/', views.annual_results_pdf, name='annual_results'),
     path('resultats/dfa/', views.DFATemplateView.as_view(), name='dfa'),
     path('resultats/dfa/show/', views.DFAListView.as_view(), name='dfa_list'),
     path('resultats/combines/', views.combined_results_ar, name='combined_results_ar'),
]


urlpatterns += [
    path('bulletin/', views.student_report_view, name='student_report'),
]

urlpatterns += [
    path('classes/', views.LevelActionListView.as_view(),
         name='level_action'),
    path('classes/documents/', views.level_document_pdf,
         name='level_documents'),
    path('classes-niveau/', views.sublevels_view, name='sublevels'),
]

urlpatterns += [
    path('cycles/periodes/', views.distinct_cycle_terms_view, name='cycle_terms'),
]


urlpatterns += [
    path('deccherifla/dossiers/', views.CheriflaStudentsList.as_view(), name='cherifla_students'),
    path('deccherifla/dossiers/ouvrir/', views.cherifla_create_student, name='cherifla_create'),
]

# urlpatterns += [
#     path('xhtml2pdf/', views.convert_to_pdf, name='convert_pdf')
# ]


urlpatterns += [
    path('generer-fiches-de-table/', views.generate_fiche_table_view, name='generate_fiche_table'),
    path('generer-bulletins/', views.generate_reports_view, name='generate_reports'),
    path('generer-bulletins/par-classe/', views.generate_all_reports_for_level_view, name='generate_reports_for_level'),
]

urlpatterns += [
    path('fichier/<int:id>/', views.file_download_view, name='file_download'),
    path('fichier-genere/', views.get_generated_file_link, name='get_generated_file_link')
]

urlpatterns += [
    path('diplomes/', views.CertificateListView.as_view(), name='certificates'),
    path('impressions/diplome/', views.certificate_pdf, name='certificate_pdf'),
]