from django.db.models import F, Q
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin

class SortableListMixin(LoginRequiredMixin, PermissionRequiredMixin):
    """Mixin that adds sorting capability to ListView"""
    
    default_sort_field = None  # Override this to set a default sort field
    default_sort_direction = 'asc'  # Default sort direction
    allowed_sort_fields = []  # List of fields that can be sorted, override this

    def sort(self, request_data, queryset, default_sort_fields):
        # Handle sorting
        sort_field = request_data.get('sort', '')
        if sort_field:
            # Store sort info for template
            self.sort_field = sort_field.lstrip('-')
            self.sort_direction = 'desc' if sort_field.startswith('-') else 'asc'
            queryset = queryset.order_by(sort_field)
        else:
            # Default sorting
            self.sort_field = ''
            self.sort_direction = ''
            queryset = queryset.order_by(*default_sort_fields or self.default_sort_field)
        return queryset

    def get_allowed_sort_fields(self):
        """Get the list of fields that can be sorted"""
        return self.allowed_sort_fields

    def get_sort_field(self):
        """Get the requested sort field and direction"""
        sort_field = self.request.GET.get('sort', '')
        if sort_field:
            clean_field = sort_field.lstrip('-') 
            if clean_field in self.get_allowed_sort_fields():
                return sort_field
        return None

    def get_context_data(self, **kwargs):
        """Add sort info to template context"""
        context = super().get_context_data(**kwargs)
        sort_field = self.get_sort_field()
        if sort_field:
            context['sort_field'] = sort_field.lstrip('-')
            context['sort_direction'] = 'desc' if sort_field.startswith('-') else 'asc'
        else:
            context['sort_field'] = ''
            context['sort_direction'] = ''
        return context
