import qrcode
from django.db.models import F, Q, Subquery, OuterRef, Value 
from django.db.models.aggregates import Sum, Count, Avg
from django.db import connection 
from datetime import datetime
from fpdf.fonts import FontFace
from fpdf.enums import TableCellFillMode
from main import utils as main_utils
from school import reports as school_reports
from school.models import TeacherLevel2, Enrollment, Level, PDFFile, GenericLevel
from exams import models, grades_utils
import time

DEFAULT_NAMES_WIDTH = 48
DEFAULT_IDS_WIDTH = 22
DEFAULT_NUMBERING_WIDTH = 10
DEFAULT_ROTATED_ITEMS_HEIGHT = 8.5
DEFAULT_ROTATED_DIFF = DEFAULT_ROTATED_ITEMS_HEIGHT - 8


class RegisterPDF(school_reports.Document):
    def add_content(self, queryset, level, redden_girls_names=False):
        self.set_xy(self.x + 10, 45 + 1)
        self.document_font = 'Times'

        if level.education == main_utils.EDUCATION_FRENCH:
            self.document_font = 'ArialNarrow'
        self.set_font(self.document_font, 'B', 12)

        title = f"LISTE D'APPEL MENSUEL " + f"{level} ({level.year})"
        self.cell(w=175, h=8, txt=title, border=1, align='C')
        self.set_xy(120, self.y + 15)

        summary = queryset.aggregate(
            boys=Count(
                'student', filter=Q(student__gender=main_utils.GENDER_MALE), 
                distinct=True) or 0,
            girls=Count(
                'student', filter=Q(student__gender=main_utils.GENDER_FEMALE), 
                distinct=True) or 0,
        )
        boys = summary['boys']
        girls = summary['girls']

        with self.table(
            align='LEFT', 
            text_align=('CENTER', 'CENTER', 'CENTER'),
            col_widths=(15, 15, 15),
            width=60,
            line_height=7) as table:
            self.add_students_count_component(table, boys, girls)
        
        education = level.education
        self.set_y(self.y + 5)
        self.set_font(self.document_font, size=10)

        self.set_line_width(0.3)
        headings_style = FontFace(emphasis="BOLD", color=255, fill_color=(128, 128, 128))
        headers = [
            {'N°': {'width': DEFAULT_NUMBERING_WIDTH, 'align': 'CENTER'}},
            {'Matricule': {'width': DEFAULT_IDS_WIDTH, 'align': 'CENTER'}},
            {'Nom et Prénoms': {'width': DEFAULT_NAMES_WIDTH, 'align': 'LEFT'}},
            {'Contact': {'width': 24, 'align': 'CENTER'}},
        ]

        for n in range(1, 32):
            headers.append({f'{str(n).zfill(2)}': {'width': 6, 'align': 'CENTER'}})

        if education == main_utils.EDUCATION_ARABIC:
            headers.reverse()
            
        widths = []
        aligns = []
        titles = []
        for header in headers:
            for key, value in header.items():
                widths += value['width'],
                if education == main_utils.EDUCATION_FRENCH:
                    titles += key,
                    aligns += value['align'],
                else:
                    aligns += str(value['align']).replace('LEFT', 'RIGHT'),
                    titles += school_reports.reshape_text(
                        main_utils.TRANSLATIONS_FR_AR.get(key.lower(), key)),

        with self.table(
            # borders_layout="NO_HORIZONTAL_LINES",
            cell_fill_color=(217, 217, 217),
            col_widths=widths,
            headings_style=headings_style,
            line_height=7.5,
            text_align=aligns,
            cell_fill_mode=TableCellFillMode.ROWS,
        ) as table:
            row = table.row()
            
            for title in titles:
                row.cell(title)

            counter = 1
            for enrollment in queryset:
                self.student = enrollment.student
                row = table.row()
                if redden_girls_names and self.student.gender == \
                   main_utils.GENDER_FEMALE:
                   self.set_text_color(255, 0, 0)

                if education == main_utils.EDUCATION_FRENCH:
                    row.cell(f'{str(counter).zfill(2)}')
                    row.cell(self.student.student_id or '')
                    row.cell(str(enrollment).upper()[:25])
                    row.cell(str(self.student.get_phone()).upper())
                    for n in range(1, 32):
                        row.cell('')
                else:
                    for n in range(32, 1, -1):
                        row.cell('')
                    full_name = school_reports.reshape_text(self.student.full_name_ar)
                    row.cell(str(self.student.get_phone()).upper())
                    row.cell(str(full_name or enrollment).upper())
                    row.cell(self.student.student_id or '')
                    row.cell(f'{str(counter).zfill(2)}')
                
                self.set_text_color(0, 0, 0)
                counter += 1



class DailyRegisterPDF(school_reports.Document):
    def add_content(self, queryset, level, redden_girls_names=False):
        self.set_xy(self.x + 10, 45 + 1)
        self.document_font = 'Times'
        if level.education == main_utils.EDUCATION_FRENCH:
            self.document_font = 'ArialNarrow'
        self.set_font(self.document_font, 'B', 12)
        title = f"LISTE D'APPEL JOURNALIER " + f"{level} ({level.year})"
        self.cell(w=175, h=8, txt=title, border=1, align='C')
        self.set_xy(120, self.y + 15)

        summary = queryset.aggregate(
            boys=Count(
                'student', filter=Q(student__gender=main_utils.GENDER_MALE), 
                distinct=True) or 0,
            girls=Count(
                'student', filter=Q(student__gender=main_utils.GENDER_FEMALE), 
                distinct=True) or 0,
        )
        boys = summary['boys']
        girls = summary['girls']

        with self.table(
            align='LEFT', 
            text_align=('CENTER', 'CENTER', 'CENTER'),
            col_widths=(15, 15, 15),
            width=60,
            line_height=7) as table:
            self.add_students_count_component(table, boys, girls)

        self.cell(txt=f"Date du jour: {'.' * 60}")
        
        education = level.education
        self.set_y(self.y + 5)
        self.set_font(self.document_font, size=10)

        self.set_line_width(0.3)
        headings_style = FontFace(emphasis="BOLD", color=255, fill_color=(128, 128, 128))
        headers = [
            {'N°': {'width': DEFAULT_NUMBERING_WIDTH, 'align': 'CENTER'}},
            {'Matricule': {'width': DEFAULT_IDS_WIDTH, 'align': 'CENTER'}},
            {'Nom et Prénoms': {'width': DEFAULT_NAMES_WIDTH, 'align': 'LEFT'}},
            {'Contact': {'width': 24, 'align': 'CENTER'}},
        ]

        for n in range(7, 18):
            if n == 12:
                headers.append({f'Midi': {'width': 12, 'align': 'CENTER'}})
            else:
                headers.append({f'{str(n).zfill(2)}h - {str(n + 1).zfill(2)}h': {'width': 12, 'align': 'CENTER'}})



        if education == main_utils.EDUCATION_ARABIC:
            headers.reverse()
            
        widths = []
        aligns = []
        titles = []
        for header in headers:
            for key, value in header.items():
                widths += value['width'],
                if education == main_utils.EDUCATION_FRENCH:
                    titles += key,
                    aligns += value['align'],
                else:
                    aligns += str(value['align']).replace('LEFT', 'RIGHT'),
                    titles += school_reports.reshape_text(
                        main_utils.TRANSLATIONS_FR_AR.get(key.lower(), key)),

        with self.table(
            # borders_layout="NO_HORIZONTAL_LINES",
            cell_fill_color=(217, 217, 217),
            col_widths=widths,
            headings_style=headings_style,
            line_height=7.5,
            text_align=aligns,
            cell_fill_mode=TableCellFillMode.ROWS,
        ) as table:
            row = table.row()
            
            for title in titles:
                row.cell(title)

            counter = 1
            for enrollment in queryset:
                row = table.row()
                if redden_girls_names and enrollment.student.gender == \
                   main_utils.GENDER_FEMALE:
                   self.set_text_color(255, 0, 0)

                if education == main_utils.EDUCATION_FRENCH:
                    row.cell(f'{str(counter).zfill(2)}')
                    row.cell(enrollment.student.student_id or '')
                    row.cell(str(enrollment).upper())
                    row.cell(str(enrollment.student.get_phone()).upper())
                    for n in range(7, 18):
                        row.cell('')
                else:
                    for n in range(7, 18):
                        row.cell('')
                    full_name = school_reports.reshape_text(enrollment.student.full_name_ar)
                    row.cell(str(enrollment.student.get_phone()).upper())
                    row.cell(str(full_name or enrollment).upper())
                    row.cell(enrollment.student.student_id or '')
                    row.cell(f'{str(counter).zfill(2)}')
                
                self.set_text_color(0, 0, 0)
                counter += 1


class MarkingSheetFr(school_reports.Document):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.headers = None
        self.document_font = None

    def footer(self):
        self.set_y(-20)
        self.set_text_color(0, 0, 0)
        self.cell(txt=f'Page {self.page_no()}', 
                    h=10, w=180, align='CENTER')

    def add_page(self, *args, **kwargs):
        super().add_page(*args, **kwargs)
        
        if self.pages_count > 1 and self.headers:
            self.set_text_color(0, 0, 0)
            family = self.font_family
            size = self.font_size
            self.add_table_header(self.headers)
            self.set_font(self.document_font, size=10)
            self.rotate(0)

    def add_table_header(self, headers):
        if self.cur_orientation == 'L':
            self.set_left_margin(self.x_right_aligned - 20)
        else:
            self.set_left_margin(4)
        self.set_y(self.y + 20)
        original_y = self.y
        self.set_fill_color(192, 192, 192)
        for index, header in enumerate(headers):
            first_header = ''
            rotated = False
            for i, key in enumerate(header.keys()):
                y = original_y 
                if i == 0:
                    first_header = key
                if index > 2:
                    self.rotate(90)
                    rotated = True
                else:
                    self.rotate(0)
                    rotated = False
                
                calculated_width = header[first_header]['width']
                
                height = DEFAULT_ROTATED_ITEMS_HEIGHT
                extra_width = 0
                if rotated:
                    extra_width += DEFAULT_ROTATED_ITEMS_HEIGHT - 3
                    calculated_width =  18 + extra_width
                    y += DEFAULT_ROTATED_ITEMS_HEIGHT
                    self.y = y
                else:
                    ratio = 15
                    self.y = original_y - ratio
                    height += ratio
                
                self.cell(calculated_width, height, txt=f'**{key}**', 
                          border=1, fill=True, markdown=True)

                if rotated:
                    self.set_x(self.x - 10 - extra_width + DEFAULT_ROTATED_DIFF)
                
                if index == 2:
                    current_x = self.x
                    self.ln()
                    self.set_x(current_x)

    def use_custom_table(self, queryset, subjects_count, data, headers,
                         redden_girls_names=True):
        self.headers = headers
        for index, enrollment in enumerate(queryset):
            id_or_identifier = enrollment.student.student_id \
                               or enrollment.student.identifier
            item = [
                str(index + 1).zfill(2), id_or_identifier, 
                str(enrollment.student)[:25],
            ]

            for i in range(subjects_count):
                item.append('')
            item.append('')
            item.append('')
            item.append('')
            item.append(
                redden_girls_names and \
                    enrollment.student.gender == main_utils.GENDER_FEMALE)
            data.append(item)

        # Add headers

        self.add_table_header(headers)

        # Add content
        y = self.y
        self.ln()
        self.set_y(self.y - DEFAULT_ROTATED_ITEMS_HEIGHT)
        self.rotate(0)
        self.set_font('ArialNarrow')
        self.document_font = 'ArialNarrow'
        for row in data:
            if row[-1]:
                self.set_text_color(255, 0, 0)
            else:
                self.set_text_color(0, 0, 0)
            for index, col in enumerate(row[:-1]):
                align = 'CENTER'
                width = 10
                if index == 1:
                    width = DEFAULT_IDS_WIDTH
                elif index == 2:
                    width = DEFAULT_NAMES_WIDTH
                    align = 'LEFT'
                elif not index == 0:
                    width = DEFAULT_ROTATED_ITEMS_HEIGHT

                self.cell(w=width, h=8, txt=col, border=1, align=align)
            self.ln()

    def use_default_table(self, widths, headings_style, aligns, titles, queryset,
                  redden_girls_names, is_second_cycle, subjects):
        with self.table(
            # borders_layout="NO_HORIZONTAL_LINES",
            cell_fill_color=(224, 235, 255),
            col_widths=widths,
            headings_style=headings_style,
            line_height=6.5,
            text_align=aligns,
            width=190,
        ) as table:
            row = table.row()
            
            for title in titles:
                row.cell(title)

            counter = 1
            for enrollment in queryset:
                row = table.row()
                if redden_girls_names and enrollment.student.gender == \
                   main_utils.GENDER_FEMALE:
                   self.set_text_color(255, 0, 0)
                    
                row.cell(f'{str(counter).zfill(2)}')
                row.cell(enrollment.student.student_id \
                        or enrollment.student.identifier or '')
                row.cell(str(enrollment).upper()[:30])

                if is_second_cycle:
                    for i in range(11):
                        row.cell('')
                    self.set_text_color(0, 0, 0)
                else:
                    for subject in subjects:
                        row.cell('')
                    row.cell('')
                    row.cell('')
                    row.cell('')
                counter += 1
                self.set_text_color(0, 0, 0)

        
    def add_content(self, user, queryset, level, redden_girls_names=True):
        document_font = 'ArialNarrow'
        self.add_header(user.school, cycle=level.generic_level.cycle)
        self.set_xy(self.x + 10, 45 + 1)
        self.set_font(document_font, 'B', 11)
        year = level.year
        title = f'FICHE DE NOTATION ' + f"{level} ({year})"
        self.cell(w=160, h=8, txt=title, border=1, align='C')
        self.set_xy(120, self.y + 15)
        table_y = self.y + 15

        self.set_font(document_font, '', 10)
        summary = queryset.aggregate(
            boys=Count(
                'student', filter=Q(student__gender=main_utils.GENDER_MALE), 
                distinct=True) or 0,
            girls=Count(
                'student', filter=Q(student__gender=main_utils.GENDER_FEMALE), 
                distinct=True) or 0,
        )
        boys = summary['boys']
        girls = summary['girls']
        is_second_cycle = queryset.first().is_second_cycle_fr
        subjects_count = 0

        with self.table(
            align='LEFT', 
            text_align=('CENTER', 'CENTER', 'CENTER'),
            col_widths=(15, 15, 15),
            width=60,
            line_height=7) as table:
            self.add_students_count_component(table, boys, girls)
        x = self.x
        y = self.y

        if is_second_cycle:
            self.set_xy(10, table_y - 15)
            with self.table(
                align='LEFT', 
                text_align=('LEFT', 'LEFT'),
                col_widths=(15, 15),
                width=60,
                line_height=7) as table:
                self.add_term_info_component(table, '')

        self.set_xy(x, y + 5)


        self.set_line_width(0.3)
        headings_style = FontFace(emphasis="BOLD", color=255, fill_color=(128, 128, 128))
        headers = [
            {'N°': {'width': DEFAULT_NUMBERING_WIDTH, 'align': 'CENTER'}},
            {'MAT': {'width': DEFAULT_IDS_WIDTH, 'align': 'CENTER'}},
            {'NOM ET PRENOMS': {'width': DEFAULT_NAMES_WIDTH, 'align': 'LEFT'}},
        ]

        data = []
        subjects_count = 0
        subjects = None
        
        if is_second_cycle:
            for i in range(10):
                item = {'N' + str(i + 1): {'width': 10, 'align': 'CENTER'}}
                headers.append(item)
            headers.append({'Moy.' : {'width': 13, 'align': 'CENTER'}})
        else:
            self.set_font_size(9.5)
            subjects = models.LevelSubject.objects.for_school(
                user, level.generic_level.short_name,
                level.education, level.year
            )

            subjects_count = subjects.count()

            width = 0
            if subjects_count <= 6:
                width = 14
            else:
                width = 11

            for subject in subjects:
                item = {str(subject.subject.abbreviation): {'width': width, 'align': 'CENTER'}}
                # if subjects_count <= 6:
                # else:
                #     item = {str(subject.subject.abbreviation): {'width': width, 'align': 'CENTER'}}
                    
                headers.append(item)
            headers.append({'TOTAL' : {'width': 12, 'align': 'CENTER'}})
            headers.append({'MOY.' : {'width': 12, 'align': 'CENTER'}})
            headers.append({'RANG' : {'width': 12, 'align': 'CENTER'}})
            
        widths = []
        aligns = []
        titles = []
        for header in headers:
            for key, value in header.items():
                widths += value['width'],
                titles += key,
                aligns += value['align'],

        if not is_second_cycle and subjects_count > 6:
            self.use_custom_table(queryset, subjects_count, data, headers, 
                                  redden_girls_names)
        else:
            self.use_default_table(widths, headings_style, aligns, titles,
                                   queryset, redden_girls_names, 
                                   is_second_cycle,
                                   subjects)


class MarkingSheetAr(school_reports.Document):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.headers = None
        self.document_font = 'Times'

    def footer(self):
        self.set_y(-20)
        self.set_text_color(0, 0, 0)
        self.cell(txt=f'Page {self.page_no()}', 
                    h=10, w=180, align='CENTER')

    def add_page(self, *args, **kwargs):
        super().add_page(*args, **kwargs)
        
        if self.pages_count > 1 and self.headers:
            self.set_text_color(0, 0, 0)
            family = self.font_family
            size = self.font_size
            self.add_table_header(self.headers)
            self.set_font(self.document_font, size=11)
            self.rotate(0)
            self.ln()
        
    def add_table_header(self, headers, template=1):
        self.set_left_margin(self.x_right_aligned)
        self.set_y(self.y + 30)
        self.original_y = self.y
        self.set_fill_color(192, 192, 192)
        self.set_font_size(10)
        for index, header in enumerate(headers):
            last_header = ''
            rotated = False
            for i, key in enumerate(header.keys()):
                y = self.original_y 
                if i == len(header) - 1:
                    last_header = key

                if index < len(headers) - 3:
                    self.rotate(90)
                    rotated = True
                else:
                    self.rotate(0)
                    rotated = False
                
                calculated_width = header[last_header]['width']
                
                height = DEFAULT_ROTATED_ITEMS_HEIGHT
                extra_width = 0
                if rotated:
                    extra_width += DEFAULT_ROTATED_ITEMS_HEIGHT - 3
                    calculated_width =  18 + extra_width
                    self.y = y
                    key = school_reports.reshape_text(key)

                else:
                    ratio = 15
                    self.y = self.original_y - ratio - DEFAULT_ROTATED_ITEMS_HEIGHT
                    height += ratio

                
                self.cell(calculated_width, height, txt=f'**{key}**', 
                        border=1, fill=True, markdown=True, align='CENTER' if template == 1 else 'LEFT')

                if rotated:
                    self.set_x(self.x - 10 - extra_width + DEFAULT_ROTATED_DIFF)
                
                if index == len(headers) - 4:
                    current_x = self.x
                    self.ln()
                    self.set_x(current_x)

    def use_custom_table(self, queryset, subjects_count, data, headers,
                         redden_girls_names=True, template=1):
        self.headers = headers
        self.get_headers_width(headers, DEFAULT_ROTATED_ITEMS_HEIGHT)
        for index, enrollment in enumerate(queryset):
            id_or_identifier = enrollment.student.student_id \
                               or enrollment.student.identifier
            full_name_ar = enrollment.student.full_name_ar
            if full_name_ar:
                full_name_ar = school_reports.reshape_text(full_name_ar)
            item = [
                str(index + 1).zfill(2), id_or_identifier, 
                str(full_name_ar) or str(enrollment.student) or '',
            ]

            if template == 1:
                for i in range(subjects_count):
                    item.append('')
            else:
                print('I am here')
                for i in range(10):
                    item.append('')
            item.append('')
            item.append('')
            item.append('')
            item.append(
                redden_girls_names and \
                    enrollment.student.gender == main_utils.GENDER_FEMALE)
            data.append(item)

        # Add headers
        headers.reverse()
        self.add_table_header(headers, template)

        # Add content
        y = self.y
        self.ln()
        self.set_y(self.y - DEFAULT_ROTATED_ITEMS_HEIGHT + 8.5)
        self.rotate(0)
        for row in data:
            row.reverse()
            if row[0]:
                self.set_text_color(255, 0, 0)
            else:
                self.set_text_color(0, 0, 0)
            for index, col in enumerate(row[1:]):
                align = 'CENTER'
                width = 10
                if index == len(row) - 3:
                    width = DEFAULT_IDS_WIDTH
                elif index == len(row) - 4:
                    width = DEFAULT_NAMES_WIDTH
                    align = 'RIGHT'
                else:
                    width = DEFAULT_ROTATED_ITEMS_HEIGHT
                self.cell(w=width, h=8, txt=col, border=1, align=align)
            self.ln()
        
    def add_content(self, user, queryset, level, redden_girls_names=True, template=1):
        document_font = 'Times'
        self.add_header(level.school, cycle=level.generic_level.cycle)
        self.set_xy(self.x + 10, 45 + 1)
        self.set_font(document_font, 'B', 11)
        self.document_font = document_font
        year =level.year 
        title = f'FICHE DE NOTATION ' + f"{level} ({year})"
        self.cell(w=160, h=8, txt=title, border=1, align='C')
        self.set_xy(120, self.y + 15)

        self.set_font(document_font, '', 10)
        summary = queryset.aggregate(
            boys=Count(
                'student', filter=Q(student__gender=main_utils.GENDER_MALE), 
                distinct=True) or 0,
            girls=Count(
                'student', filter=Q(student__gender=main_utils.GENDER_FEMALE), 
                distinct=True) or 0,
        )
        boys = summary['boys']
        girls = summary['girls']
        is_second_cycle = queryset.first().is_second_cycle_fr
        subjects_count = 0

        with self.table(
            align='LEFT', 
            text_align=('CENTER', 'CENTER', 'CENTER'),
            col_widths=(15, 15, 15),
            width=60,
            line_height=7) as table:
            self.add_students_count_component(table, boys, girls)
        
        self.set_y(self.y + 5)


        self.set_line_width(0.3)
        headings_style = FontFace(emphasis="BOLD", color=255, fill_color=(128, 128, 128))
        headers = [
            {'N°': {'width': DEFAULT_NUMBERING_WIDTH - 1.5, 'align': 'CENTER'}},
            {'MAT': {'width': DEFAULT_IDS_WIDTH, 'align': 'CENTER'}},
            {'NOM ET PRENOMS': {'width': DEFAULT_NAMES_WIDTH, 'align': 'LEFT'}},
        ]

        data = []
        subjects_count = 0

        self.set_font_size(9.5)
        subjects = models.LevelSubject.objects.for_school(
            user, level.generic_level.short_name,
            level.education, level.year
        ).filter(active=True)

        subjects_count = subjects.count()


        width = 11
        
        # if subjects_count > 10:
        #     self.def_orientation = 'L'
            
        if template == 1:
            for subject in subjects:
                if subjects_count <= 15:
                    item = {str(subject.subject.translation): {'width': width, 'align': 'CENTER'}}
                else:
                    item = {str(subject.subject.translation): {'width': width + 0.5, 'align': 'CENTER'}}
                    
                headers.append(item)
        else:
            for i in range(10):
                item = {str(i + 1): {'width': 10, 'align': 'RIGHT'}}
                headers.append(item)
        headers.append({'TOTAL' : {'width': 12, 'align': 'CENTER'}})
        headers.append({'MOY.' : {'width': 12, 'align': 'CENTER'}})
        headers.append({'RANG' : {'width': 12, 'align': 'CENTER'}})
            
        widths = []
        aligns = []
        titles = []
        for header in headers:
            for key, value in header.items():
                widths += value['width'],
                titles += key,
                aligns += value['align'],

        self.use_custom_table(queryset, subjects_count, data, headers, 
                            redden_girls_names, template=template)
            

def generate_marking_sheet(user, level, queryset, template=1):
    education = level.education

    doc = None
    subjects = models.LevelSubject.objects.for_school(
            user, level.generic_level.short_name,
            level.education, level.year
    )
    orientation = 'P'
    if subjects.count() > 10 and template == 1:
        orientation = 'L'

    if education == main_utils.EDUCATION_FRENCH:
        doc = MarkingSheetFr()
    else:
        doc = MarkingSheetAr(orientation)
    doc.add_page()
    if template == 1:
        doc.add_content(user, queryset, level)
    else:
        doc.add_content(user, queryset, level, template=2)
    return doc.get_file_response('Fiche de notation ' + str(level))


class SecondCycleReport(school_reports.Document):
    def add_content(self, queryset, term, year, annual_report=False):
        first = queryset.first()
        students_count = first.level_fr.enrollment_set.filter(active=True).count()
        school = first.school
        level_statistics = None
        level = None
        self.annual_report = annual_report
        
        if term.education == main_utils.EDUCATION_FRENCH:
            level = first.level_fr
        else:
            level = first.level_ar

        self.level_code = level.generic_level.short_name

        self.is_exam_level = main_utils.is_exam_level(self.level_code)
        if annual_report:
            level_statistics = models.LevelStatistics.objects.for_school(
                year=year, school=school, term=None, level=level
            ).first()
        else:
            level_statistics = models.LevelStatistics.objects.for_school(
                year=year, school=school, term=term, level=level
            ).first()

        self.year = str(first.year)
        self.gen_average =  0
        self.min_average = 0
        self.max_average = 0

        if level_statistics:
            self.gen_average = level_statistics.level_average
            self.min_average = level_statistics.min_average
            self.max_average = level_statistics.max_average

        teachers_qs = TeacherLevel2.objects.filter(level=first.level_fr)\
            .select_related('teacher')
        
        self.french_teacher = ''
        self.main_teacher = ''
        self.teachers = {}

        if teachers_qs.exists():
            for teacher in teachers_qs:
                for subject in teacher.subjects.filter(year=year):
                    print(str(year), str(subject), str(teacher.teacher))
                    self.teachers[str(subject.id)] = str(teacher.teacher)[:23]
                    if not self.french_teacher and subject.subject.is_sous_matiere:
                        self.french_teacher = str(teacher.teacher)
                if teacher.is_main_teacher and not self.main_teacher:
                    self.main_teacher = str(teacher.teacher) 
        
        # aggregated = models.LevelSubject.objects.filter(
        #     level=first.generic_level_fr, year=year,
        #     subject__education=main_utils.EDUCATION_FRENCH,
        # ).aggregate(
        #     lit_coefs=Sum('coefficient', filter=Q(subject__category=main_utils.CATEGORY_LETTERS)),
        #     science_coefs=Sum('coefficient', filter=Q(subject__category=main_utils.CATEGORY_SCIENCE)),
        #     other_coefs=Sum('coefficient', filter=Q(subject__category=main_utils.CATEGORY_OTHER)),
        # )
        self.lit_coefs = 0
        self.science_coefs = 0
        self.other_coefs = 0
        self.students_count = students_count
        start_time = time.time()
        for obj in queryset:
            if self.annual_report:
                self.add_student_report(
                    obj, term, school,
                    obj.termresult_set.filter(school_term=term).first(), 
                    year, annual_report=annual_report,
                    year_result=obj.educationyearresult_set.filter(
                        education=term.education).first(), 
                    )
            else:
                self.add_student_report(
                    obj, term, school,
                    obj.termresult_set.filter(school_term=term).first(), 
                    year, annual_report=annual_report)
        
        end_time = time.time()
        print('Query time', end_time - start_time)

    def add_student_report(
            self, enrollment, term, school, 
            term_result, year,
            annual_report=False,
            year_result=None):
        
        self.student = enrollment.student
        self.enrollment = enrollment
        self.term = term

        self.set_top_margin(4)
        self.add_page() 

        self.set_font('Helv', size=8)
        self.set_xy(self.x + 2, self.y + 10)
        x, y = self.x, self.y
        self.multi_cell(
            txt= \
            f"""MINISTERE DE L'EDUCATION NATIONALE
    ET DE L'ALPHABETISATION
    ----------------------------
    DRENA {school.location.dren}""",
            w=60, h=3, align='CENTER')

        self.set_y(10)
        self.cell(w=190, border=1, h=20)

        self.set_font_size(12)
        self.set_xy(x + 60, y)
        self.multi_cell(
            txt= \
            f"""**BULLETIN TRIMESTRIEL DE NOTES\n< {term} >**""",
            w=90, h=5, align='CENTER', markdown=True)
        
        self.set_font_size(11)
        self.set_xy(self.x + 8, y)
        self.multi_cell(
            txt= \
            f"""Année scolaire\n{year}""",
            w=30, h=5, align='CENTER')
        
        # School infos
        self.ln(6)
        self.cell(w=190, border=1, h=18)
        y = self.y
        self.set_font_size(8)
        self.set_xy(13, self.y + 2)
        if school.logo:
            self.image(school.logo.url, w=16, h=14)
        else:
            self.image('static/img/armoiries.png', w=16, h=14)
        self.set_xy(self.x + 20, y + 3)
        self.multi_cell(
            txt= \
            f"Etablissement      : **{ school.get_name(main_utils.CYCLE_SECONDARY) }**\n" + \
            f"Adresse postale   : {school.location} {' ' * 40} Téléphone: {school.phone1 or '-'}",
            w=140, h=5, align='LEFT', markdown=True)

        self.line(self.x - 15, self.y - 13, self.x - 15, self.y + 5)

        self.set_xy(self.x - 15, y + 3)
        self.multi_cell(
            txt= \
            f"Code   : **{school.code or '-'}**\n" + \
            f"Statut  : **{school.get_status_display()}**",
            w=140, h=5, align='LEFT', markdown=True)
        
        self.image(
            qrcode.make(
                f"{school}\n"
                f"{enrollment.student}\n" 
                f"{enrollment.student.student_id or '-'}\n"
                f"{enrollment.level_fr}")\
                .get_image(), 
            182, 31, w=16, h=16)

        self.set_font_size(12)
        self.ln(5)
        self.cell(w=190, border=1, h=35)
        self.set_xy(12, self.y + 3)

        # Student infos
        self.cell(w=150, txt=f'**{enrollment}**', markdown=True)
        self.set_font_size(9)
        self.ln(8)
        y = self.y
        self.set_x(self.x)
        self.multi_cell(
            w=25, h=7, markdown=True, align='RIGHT',
            txt="**Matricule :**\nClasse :\nEffectif :"

        )
        self.set_xy(self.x, y)
        self.set_font_size(11)
        self.multi_cell(
            w=25, h=7, markdown=True, align='LEFT',
            txt=f"**{enrollment.student.student_id or '-'}**\n{ enrollment.level_fr }\n{self.students_count}"
        )

        self.set_xy(self.x, y)
        self.set_font_size(9)
        self.multi_cell(
            w=30, h=6, markdown=True, align='RIGHT',
            txt="Sexe :\nNé(e) le :\nLieu de naissance :\nNationalité :"
        )

        self.set_xy(self.x, y)
        self.set_font_size(8)
        self.multi_cell(
            w=25, h=6, markdown=True, align='LEFT',
            txt=f"{self.student.gender}\n{self.student.birth_date_str()}\n{self.student.birth_place or '-'}\n{self.student.get_nationality_display()}"
        )
        
        self.set_xy(self.x - 5, y)
        self.set_font_size(9)
        self.multi_cell(
            w=30, h=6, markdown=True, align='RIGHT',
            txt="Rédoublant :\nRégime :\nAffecté :"
        )

        self.set_xy(self.x, y)
        self.set_font_size(10)
        self.multi_cell(
            w=45, h=6, markdown=True, align='LEFT',
            txt=f"{enrollment.get_qualite()}\n{enrollment.get_has_scholarship()}\n{enrollment.get_status()}"
        )
        self.set_font_size(9)
        
        url = ''
        if self.student.photo:
            url = self.student.photo.url

        self.add_image_or_gov_img_or_avatar(
            url, self.student.student_id,
            x=self.x - 13, y=y - 10, w=25, h=30)

        grades = models.Grade.objects \
            .filter(school_term=term, enrollment=enrollment)\
            .select_related('subject__subject')
        self.add_table(grades, term_result, school, year_result)
        
    def footer(self):
        self.set_y(-15)
        self.set_font_size(8)
        self.multi_cell(
            txt=f"\u00A9 Copyright EcolePro {self.year}\n" \
             "Veuillez conserver ce bulletin soigneusement."
             "Aucun duplicata ne sera délivré par l'établissement en cas de perte.",
            w=170, align='CENTER')
        
    def add_table(self, grades, term_result, school, year_result=None):
        self.ln(6)
        self.set_font_size(9)
        self.set_font('Helv', '')

        # Table data
        text = 'Appréciations'
        if self.annual_report and not self.is_exam_level:
            text = 'Moy. Annuelle'
        table_header = ['DISCIPLINES', 'Moy./20', 'Coef.', 'Total', 'Rang',
            text, 'Professeurs', 'Signature'
        ]
        
        french_average = term_result.french_average if term_result else 0
        french_coefs = term_result.french_coefs if term_result else 0

        if not main_utils.has_decimal_part(french_average):
            french_average = int(french_average) if french_average else 0
        
        average_coeffed = '-'
        if french_average and french_coefs:
            average_coeffed = french_average * french_coefs

        text = main_utils.APPRECIATIONS_SUR_20.get(str(int(french_average or 0)), '')
        if self.annual_report and not self.is_exam_level:
            text = self.enrollment.french_avg_summary
            if text:
                text = float(text)

                if not main_utils.has_decimal_part(text):
                    text = int(text)
                else:
                    text = round(text, 2)
            else:
                text = '-'

        french_summary = [
            'Français', 
            str(french_average), 
            str(term_result.french_coefs or '-' if term_result else '-'), 
            str(average_coeffed), term_result.get_french_rank() if term_result else 'NC',
            text, self.french_teacher, '']

        litterature_data = []
        litterature_data.append(table_header)
        litterature_data.append(french_summary)
        lit_total = 0
        self.lit_coefs = 0
        for grade in grades.filter(
            subject__subject__category=main_utils.CATEGORY_LETTERS):
            lit_total += self.format_data(litterature_data, grade, self.teachers)

            if grade.grade:
                self.lit_coefs += grade.subject.coefficient

        self.science_coefs = 0
        science_data = []
        science_total = 0
        for grade in grades.filter(
            subject__subject__category=main_utils.CATEGORY_SCIENCE):
            science_total += self.format_data(science_data, grade, self.teachers)

            if grade.grade:
                self.science_coefs += grade.subject.coefficient


        self.other_coefs = 0
        other_data = []
        other_total = 0
        for grade in grades.filter(
            subject__subject__category=main_utils.CATEGORY_OTHER):

            other_total += self.format_data(other_data, grade, self.teachers)

            if grade.grade:
                self.other_coefs += grade.subject.coefficient

        self.teachers_font = FontFace('Helv', size_pt=7)
        summary_font = FontFace(
            'Helv', size_pt=10, emphasis=True, 
            fill_color=(211, 211, 211))

        # Table
        with self.table(
            col_widths=(43, 15, 12, 14, 15, 25, 40, 18),
            text_align=(
                'LEFT', 'CENTER', 'CENTER', 'CENTER', 'CENTER',
                'CENTER', 'LEFT', 'CENTER'
            ),
            line_height=5
        ) as table:

            lit_summary = self.enrollment.studentsubjectgroupaverage_set.filter(
                term=self.term, group=main_utils.CATEGORY_LETTERS
            ).first()            
            science_summary = self.enrollment.studentsubjectgroupaverage_set.filter(
                term=self.term, group=main_utils.CATEGORY_SCIENCE
            ).first()            
            other_summary = self.enrollment.studentsubjectgroupaverage_set.filter(
                term=self.term, group=main_utils.CATEGORY_OTHER
            ).first()            
            # Lettres
            self.add_section_component(litterature_data, table)
            row = table.row()
            lit_sum_avg = '-'
            lit_sum_rank = '-'

            if lit_summary:
                lit_sum_avg = lit_summary.average
                lit_sum_rank = lit_summary.get_rank()

            row.cell(f"BILAN LETTRES {' ' * 19} {lit_sum_avg} {' ' * 6} {self.lit_coefs} {' ' * 10} {lit_total} {' ' * 7} {lit_sum_rank}", 
                     style=summary_font, colspan=8, padding=(0.4, 0.4))
            
            # Sciences
            science_sum_avg = '-'
            science_sum_rank = '-'
            if science_summary:
                science_sum_avg = science_summary.average
                science_sum_rank = science_summary.get_rank()
            self.add_section_component(science_data, table)
            row = table.row()
            row.cell(f"BILAN SCIENCES {' ' * 17} {science_sum_avg} {' ' * 6}  {self.science_coefs} {' ' * 10} { science_total } {' ' * 7} {science_sum_rank}", 
                     style=summary_font, colspan=8, padding=(0.4, 0.4))
            
            # Autres et totaux
            other_sum_avg = '-'
            other_sum_rank = '-'
            if other_summary:
                other_sum_avg = other_summary.average
                other_sum_rank = other_summary.get_rank()
            self.add_section_component(other_data, table)
            row = table.row()
            row.cell(f"BILAN AUTRES {' ' * 21} {other_sum_avg} {' ' * 6} {self.other_coefs} {' ' * 10} { other_total } {' ' * 7} {other_sum_rank}", 
                     style=summary_font, colspan=8, padding=(0.4, 0.4))

            row = table.row()
            row.cell('TOTAUX: ', colspan=2, align='RIGHT', style=FontFace(emphasis=True), padding=(0.4, 0.4))
            row.cell(
                f'{self.lit_coefs + self.science_coefs + self.other_coefs}', 
                align='CENTER', style=FontFace(emphasis=True))
            row.cell(
                f'{lit_total + science_total + other_total}', align='CENTER', 
                style=FontFace(emphasis=True))
            row.cell('', colspan=4, align='CENTER')

        self.ln(5)
        self.set_font_size(12)
        text = "DU TRIMESTRE" if not self.annual_report else "DE L'ANNEE"
        self.cell(w=190, txt=f"{'/' * 31} **BILAN {text}** {'/' * 31 }", 
                  border=1, h=7, align='CENTER', markdown=True)
        self.set_font_size(9)
        self.ln(7)

        if not self.annual_report:
            self.cell(w=65, txt='**Assiduité**', border=1, h=5, markdown=True, align='CENTER')
            self.ln()
            y = self.y
            self.multi_cell(w=32, txt="Nombre d'heures \n d'absence: -- 00 --", border=1, h=5,
                            markdown=True)
            
            self.set_xy(self.x, y)
            self.multi_cell(
                w=33, 
                txt="""Justifiées:  -- 00 --\nNon-justifiées: **-- 00 --**""", 
                border=1, h=5, markdown=True)
        else:
            self.cell(w=21, txt='**Trimestre 1**', border=1, h=5, markdown=True, align='CENTER')
            self.cell(w=22, txt='**Trimestre 2**', border=1, h=5, markdown=True, align='CENTER')
            self.cell(w=22, txt='**Trimestre 3**', border=1, h=5, markdown=True, align='CENTER')
            self.ln()
            y = self.y
            
            avg_1 = self.enrollment.__dict__.get('avg_for_term_1') or '-'
            if avg_1 and avg_1 != '-':
                avg_1 = round(avg_1, 2)
            rank_1 = self.enrollment.__dict__.get('rank_for_term_1')
            is_ex_1 = self.enrollment.__dict__.get('is_ex_for_term_1')
            rank_str_1 = main_utils.get_rank_str(rank_1, is_ex_1) or 'NC'
            
            avg_2 = self.enrollment.__dict__.get('avg_for_term_2') or '-'
            if avg_2 and avg_2 != '-':
                avg_2 = round(avg_2, 2)
            rank_2 = self.enrollment.__dict__.get('rank_for_term_2')
            is_ex_2 = self.enrollment.__dict__.get('is_ex_for_term_2') 
            rank_str_2 = main_utils.get_rank_str(rank_2, is_ex_2) or 'NC'
            
            avg_3 = self.enrollment.__dict__.get('avg_for_term_3') or '-'
            if avg_3 and avg_3 != '-':
                avg_3 = round(avg_3, 2)
            rank_3 = self.enrollment.__dict__.get('rank_for_term_3')
            is_ex_3 = self.enrollment.__dict__.get('is_ex_for_term_3')
            rank_str_3 = main_utils.get_rank_str(rank_3, is_ex_3) or 'NC'
            
            self.multi_cell(w=21, txt=f"Moy  : **{avg_1}** \nRang : **{rank_str_1}**", border=1, h=5,
                            markdown=True)
            
            self.set_xy(self.x, y)
            self.multi_cell(w=22, txt=f"Moy  : **{avg_2}** \nRang : **{rank_str_2}**", border=1, h=5,
                            markdown=True)
            
            self.set_xy(self.x, y)
            self.multi_cell(w=22, txt=f"Moy  : **{avg_3}** \nRang : **{rank_str_3}**", border=1, h=5,
                            markdown=True)
            
        self.set_xy(self.x, y - 5)
        
        y = self.y
        y_pos = y

        result_obj = None
        if not self.annual_report:
            result_obj = term_result
        else:
            result_obj = year_result

        text = 'Moyenne ' + ('trimestrielle' \
            if not self.annual_report else 'annuelle')
        self.multi_cell(w=60, 
                        txt=f'**--{text}-- ** \n\n' + 
                        f'Rang: **-- {result_obj.get_rank()} --** sur **-- {self.students_count} --**', 
                        markdown=True, border=1, h=5, align='CENTER')
        
        x, y = self.x, self.y

        self.set_font_size(12)
        self.set_xy(self.x - 40, y - 9)
        self.cell(txt=f'**-- {result_obj.average} --** /', markdown=True)

        self.set_font_size(9)
        self.set_xy(self.x - 0.5, self.y + 0.5)
        self.cell(txt='20')
        
        self.set_xy(x, y_pos)
        self.cell(txt='**Résultats de la classe**', border=1, 
                  w=65, h=5, align='CENTER', markdown=True)
        
        self.set_xy(x, y_pos + 5)
        self.multi_cell(w=30, txt=f"Moyenne générale \n de la classe: --{self.gen_average}--", border=1, h=5,
                        markdown=True)
        
        self.set_xy(x + 30, y_pos + 5)
        self.multi_cell(w=35, txt=f"Moyenne mini: --{self.min_average}-- \n Moyenne maxi: --{self.max_average}--", border=1, h=5,
                        markdown=True)

        self.ln(0)
        self.set_font_size(9)
        y = self.y
        self.set_y(self.y - 5)
        self.multi_cell(
            w=65, txt= \
                "       --**Mentions du Conseil de Classe**--\n" \
                "\n**   --* DISTINCTIONS--**" \
                "\n       Tableau d'honneur + Compliments" \
                "\n       Tableau d'honneur"
                "\n**   --* SANCTIONS--**" \
                "\n       Blâme travail" \
                "\n       Blâme conduite",
            border=1, h=6, align='LEFT', markdown=True)
        
        self.ln()

        appreciation = result_obj.get_appreciation()
        self.image(f"static/img/{'check-' if appreciation == 'great_job' else ''}square.png", w=4, h=4, y=self.y - 35.2, x=self.x + 2)
        self.image(f"static/img/{'check-' if appreciation == 'good_job' else ''}square.png", w=4, h=4, y=self.y - 29.2, x=self.x + 2)
        self.image(f"static/img/{'check-' if appreciation == 'bad_job' else ''}square.png", w=4, h=4, y=self.y - 17.2, x=self.x + 2)
        self.image("static/img/square.png", w=4, h=4, y=self.y - 11.2, x=self.x + 2)

        self.set_xy(self.x + 65, y - 5)
        x = self.x
        text = 'Appréciations du Conseil de classe'

        if self.annual_report:
            text = "Décision de fin d'année"

        self.set_font_size(10)
        distinction = result_obj.get_distinction()
        if self.annual_report:
            distinction = result_obj.get_decision(level_code=self.level_code)

        self.multi_cell(
                txt=f'**--{text}--**' \
                    f'\n\n**{distinction}**' \
                    '\n\n__Nom/Signature du professeur principal__' \
                    f'\n\n\n** {self.main_teacher} **', 
                w=60, border=1, align='CENTER',
                markdown=True, h=6)

        self.set_font_size(9)
        self.set_xy(x + 60, y - 5)
        current_date_str = datetime.today().strftime('%d/%m/%Y')
        self.multi_cell(
                txt="**--Chef d'établissement--**" \
                    f"\nFait à {school.exact_location or school.location}, le {current_date_str}" \
                    "\n\n**Le Directeur des Etudes**" \
                    f"\n\n\n\n**{school.director_secondary or school.director_fr or '-'}**", 
                w=65, border=1, align='CENTER',
                markdown=True, h=6)

    def add_section_component(self, data, table):
        for datum in data:
            row = table.row()
            style = self.font_style

            for i, col in enumerate(datum):
                subject = datum[0]
                family = self.font_family
                
                is_french = (type(subject) != str) and (subject.subject.is_sous_matiere)
                
                if type(subject) != str and i == 0:
                    col = subject.subject.name

                if is_french:
                    self.set_font('Times', style='I')

                    if i == 0:
                        col = f"{' ' * 6}{col}"

                if is_french and i >= 6:
                    col = ''

                if i == len(datum) - 2:
                    row.cell(str(col), style=self.teachers_font)
                else:
                    row.cell(str(col))
                self.set_font(family, style)

    def format_data(self, data, grade, teachers):
        avg_coef = '-'
        average = grade.grade

        mention = ''

        if self.annual_report and not self.is_exam_level:
            mention = self.enrollment.__dict__.get(f'annual_avg_for_subject_{grade.subject.subject_id}')
            print(str(mention), str(mention).isnumeric())
            if mention:
                mention = float(mention)
                if not main_utils.has_decimal_part(mention):
                    mention = int(mention)
                else:
                    mention = round(mention, 2)
                # mention = round(mention, 2)
            else:
                mention = '-'
        else:
            if average or average == 0:
                mention = main_utils.APPRECIATIONS_SUR_20.get(str(int(average)), '-')
        total = 0

        if average:
            if not main_utils.has_decimal_part(average):
                average = int(average)
            avg_coef = average * grade.subject.coefficient 
            total += avg_coef

        coef = grade.subject.coefficient
        if not coef:
            coef = '-'
        data.append(
            [
                grade.subject, 
                average or '-', grade.subject.coefficient if (average and average != '-') else '-', 
                avg_coef or '-', grade.get_rank_if_grade(),
                mention, teachers.get(str(grade.subject.id), ''), 
                ''
            ], 
        )

        return total

  
class TermResultsFr(school_reports.Document):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.headers = None

    def add_page(self, *args, **kwargs):
        super().add_page(*args, **kwargs)
        
        if self.pages_count > 1 and self.headers:
            self.set_text_color(0, 0, 0)
            family = self.font_family
            size = self.font_size
            self.add_table_header(self.headers)
            self.set_font(self.document_font, size=10)
            self.rotate(0)
    
    def add_table_header(self, headers):
        if self.cur_orientation == 'L':
            self.set_left_margin(self.x_right_aligned - 20)
        else:
            self.set_left_margin(4)
        self.set_y(self.y + 20)
        original_y = self.y
        self.set_fill_color(192, 192, 192)
        for index, header in enumerate(headers):
            first_header = ''
            rotated = False
            for i, key in enumerate(header.keys()):
                y = original_y 
                if i == 0:
                    first_header = key
                if index > 2:
                    self.rotate(90)
                    rotated = True
                else:
                    self.rotate(0)
                    rotated = False
                
                calculated_width = header[first_header]['width']
                
                height = DEFAULT_ROTATED_ITEMS_HEIGHT
                extra_width = 0
                if rotated:
                    extra_width += DEFAULT_ROTATED_ITEMS_HEIGHT - 3
                    calculated_width =  18 + extra_width
                    y += DEFAULT_ROTATED_ITEMS_HEIGHT
                    self.y = y
                else:
                    ratio = 15
                    self.y = original_y - ratio
                    height += ratio
                
                self.cell(calculated_width, height, txt=f'**{key}**', 
                          border=1, fill=True, markdown=True)

                if rotated:
                    self.set_x(self.x - 10 - extra_width + DEFAULT_ROTATED_DIFF)
                
                if index == 2:
                    current_x = self.x
                    self.ln()
                    self.set_x(current_x)


    def add_content(self, user, queryset, term, level):
        self.document_font = 'ArialNarrow'
        first = queryset.first()
        self.add_header(first.school, cycle=level.generic_level.cycle)
        self.set_xy(self.x + 10, 45 + 1)
        self.set_font(self.document_font, 'B', 11)
        year = first.year if queryset.exists() else ''
        title = f'RESULTATS : {term}' + f" {level} ({year})"
        self.cell(w=160, h=8, txt=title, border=1, align='C')
        self.set_xy(110, self.y + 15)

        self.set_font(self.document_font, '', 10)
        summary = queryset.aggregate(
            boys=Count(
                'student', filter=Q(student__gender=main_utils.GENDER_MALE), 
                distinct=True) or 0,
            girls=Count(
                'student', filter=Q(student__gender=main_utils.GENDER_FEMALE), 
                distinct=True) or 0,
        )
        boys = summary['boys']
        girls = summary['girls']
        is_second_cycle = first.is_second_cycle_fr
        subjects_count = 0

        result_obj = models.LevelStatistics.objects.get(term=term, level=level)
        with self.table(
            align='LEFT', 
            text_align=('LEFT', 'CENTER', 'CENTER', 'CENTER'),
            col_widths=(25, 20, 20, 20),
            width=80,
            line_height=5.5) as table:
            self.add_results_component(
                table, result_obj, students={'G': boys, 'F': girls})
        
        self.set_y(self.y + 5)


        self.set_line_width(0.3)
        headings_style = FontFace(emphasis="BOLD", color=255, fill_color=(128, 128, 128))

        names_width = DEFAULT_NAMES_WIDTH
        if is_second_cycle:
            names_width += 20

        headers = [
            {'N°': {'width': DEFAULT_NUMBERING_WIDTH, 'align': 'CENTER'}},
            {'MAT': {'width': DEFAULT_IDS_WIDTH, 'align': 'CENTER'}},
            {'NOM ET PRENOMS': {'width': names_width, 'align': 'LEFT'}},
        ]

        data = []
        subjects_count = 0
        subjects = None
        
        self.set_font_size(9.5)
        subjects = models.LevelSubject.objects.for_school(
            user, level.generic_level.short_name,
            level.education, level.year
        )

        subjects_count = subjects.count()

        width = 0
        if subjects_count <= 6:
            width = 14
        else:
            width = 11

        for subject in subjects:
            item = {str(subject.subject.abbreviation): {'width': width, 'align': 'CENTER'}}
            headers.append(item)

        
        if not is_second_cycle:
            headers.append({'TOTAL' : {'width': 12, 'align': 'CENTER'}})

        headers.append({'MOY.' : {'width': 12, 'align': 'CENTER'}})
        headers.append({'RANG' : {'width': 12, 'align': 'CENTER'}})

        if not is_second_cycle:
            headers.append({'OBS.' : {'width': 12, 'align': 'CENTER'}})
            
        widths = []
        aligns = []
        titles = []
        for header in headers:
            for key, value in header.items():
                widths += value['width'],
                titles += key,
                aligns += value['align'],

        if subjects_count > 6:
            self.use_custom_table(queryset, subjects_count, data, headers, term, True,
                                  term_max=term.max)
        else:
            self.use_default_table(widths, headings_style, aligns, titles,
                queryset, True, is_second_cycle, term, term_max=term.max)

    
    def use_custom_table(self, queryset, subjects, data, headers, term,
                         redden_girls_names=True, term_max=None):
        self.get_headers_width(headers, DEFAULT_ROTATED_ITEMS_HEIGHT)
        self.headers = headers
        is_second_cycle = queryset.first().is_second_cycle_fr

        self.set_font(self.document_font)
        for index, enrollment in enumerate(queryset):
            id_or_identifier = enrollment.student.student_id \
                               or enrollment.student.identifier
            item = [
                str(index + 1).zfill(2), id_or_identifier, 
                str(enrollment.student),
            ]

            for grade in enrollment.filtered_grades:
                grade_value = grade.grade
                if grade_value and not main_utils.has_decimal_part(grade_value):
                    grade_value = str(int(grade_value)).zfill(2)

                if grade_value  == 0:
                    item.append(0)
                elif not grade_value:
                    item.append('-')
                else:
                    item.append(str(grade_value))

            result_obj = enrollment.termresult_set.filter(school_term=term).first()
            result = enrollment.termresult_set.filter(school_term=term) \
                .values('average', 'rank', 'total').first()

            avg = 0
            rank = 0
            total = 0

            if result.get('average'):
                avg = float(result.get('average'))

            if result.get('rank'):
                rank = int(result.get('rank', 0))

            if result.get('total'):
                total = int(result.get('total', 0))
            
            if not enrollment.is_second_cycle_fr:
                item.append(str(total))
            
            item.append(str(avg))
            item.append(str(rank))

            if not enrollment.is_second_cycle_fr:
                if result_obj:
                    item.append(result_obj.get_decision(max=term_max))
                else:
                    item.append('')
            
            item.append(
                redden_girls_names and \
                    enrollment.student.gender == main_utils.GENDER_FEMALE)
            data.append(item)

        # Add headers
        self.add_table_header(headers)

        # Add content
        self.set_font(self.document_font)
        y = self.y
        self.ln()
        self.set_y(self.y - DEFAULT_ROTATED_ITEMS_HEIGHT)
        self.rotate(0)
        for row in data:
            if row[-1]:
                self.set_text_color(255, 0, 0)
            else:
                self.set_text_color(0, 0, 0)

            for index, col in enumerate(row[:-1]):
                align = 'CENTER'
                width = 10
                if index == 1:
                    width = DEFAULT_IDS_WIDTH
                elif index == 2:
                    width = (DEFAULT_NAMES_WIDTH + 20) if is_second_cycle else DEFAULT_NAMES_WIDTH
                    align = 'LEFT'
                elif not index == 0:
                    width = DEFAULT_ROTATED_ITEMS_HEIGHT

                self.cell(w=width, h=8, txt=col, border=1, align=align)
            self.ln()

    def use_default_table(self, widths, headings_style, aligns, titles, queryset,
                  redden_girls_names, is_second_cycle, term, term_max=None):
        with self.table(
            # borders_layout="NO_HORIZONTAL_LINES",
            cell_fill_color=(224, 235, 255),
            col_widths=widths,
            headings_style=headings_style,
            line_height=6.5,
            text_align=aligns,
            width=185,
        ) as table:
            row = table.row()
            
            for title in titles:
                row.cell(title)

            counter = 1
            for enrollment in queryset:
                row = table.row()
                if redden_girls_names and enrollment.student.gender == \
                   main_utils.GENDER_FEMALE:
                   self.set_text_color(255, 0, 0)
                    
                row.cell(f'{str(counter).zfill(2)}')
                row.cell(enrollment.student.student_id or '')
                row.cell(str(enrollment).upper()[:22])

                for grade in enrollment.filtered_grades:
                    grade_value = grade.grade
                    if grade_value and not main_utils.has_decimal_part(grade_value):
                        grade_value = str(int(grade_value)).zfill(2)

                    if grade_value  == 0:
                        row.cell(str(0))
                    elif not grade or not grade_value:
                        row.cell('-')
                    else:
                        row.cell(str(grade_value))

                result_obj = enrollment.termresult_set.filter(school_term=term).first()
                result = enrollment.termresult_set.filter(school_term=term) \
                    .values('average', 'rank', 'total').first()
                avg = 0
                if not result.get('average') is None:
                    avg = result.get('average', 0)
                avg = float(avg)
                rank = int(result.get('rank', 0) if result else 0)
                total = result.get('total', 0) if result else 0
                if not is_second_cycle:
                    row.cell(str(total))
                row.cell(str(avg))
                row.cell(str(rank))
                
                if not is_second_cycle and result_obj:
                    row.cell(str(result_obj.get_decision(term_max)))
                elif not is_second_cycle:
                    row.cell('-')

                counter += 1
                self.set_text_color(0, 0, 0)


    def footer(self):
        self.set_y(-20)
        self.set_text_color(0, 0, 0)
        self.cell(txt=f'Page {self.page_no()}', 
                    h=10, w=180, align='CENTER')



class PrimaryReportFr(school_reports.Document):
    def add_content(self, user, queryset, term, level, 
                report_type=PDFFile.CATEGORY_REPORT,
                is_last_term=False):
        year = level.year
        students_count = level.get_students_count()
        self.school = user.school
        director = self.school.director_fr or ''
        qs = TeacherLevel2.objects.filter(level=level)
        teacher = ''
        self.subjects = list(models.LevelSubject.objects.for_school(
            user, level_name=level.generic_level.short_name, 
            education=term.education, year=term.year).order_by('order'))

        terms_to_display = None
        terms_count = 0
        self.next_level = ''
        next_highest_level = GenericLevel.objects.filter(order__gt=level.generic_level.order) \
            .order_by('order').first()
        if next_highest_level:
            self.next_level = str(next_highest_level)

        self.is_last_term = is_last_term
        if report_type == PDFFile.CATEGORY_REPORT_WITH_PREVIOUS_TERMS_DATA:
            terms_to_display = list(models.SchoolTerm.objects.active(
                year=year, user=user, level=level.generic_level, education=level.education
            ).filter(term__order__lte=term.term.order).order_by('term__order'))
            terms_count = len(terms_to_display)

        if qs.exists():
            teacher = str(qs.first().teacher)[:20]

        term_max = term.max
        term_translation = term.term.translation
        location = user.school.exact_location or user.school.location

        for i, enrollment in enumerate(queryset):
            position = 1
            if (i + 1) % 2 == 0:
                position = 2

            grades = enrollment.filtered_grades
            report_url = f'https://www.ecolepro.net/examens/bulletin/?eleve={enrollment.id}&periode={term.id}'
            self.add_report(enrollment=enrollment, year=year, level=level, 
                            term=term, students_count=students_count, 
                            grades=grades, term_max_avg=term_max, location=location,
                            position=position, teacher=teacher, director=director,
                            url=report_url, term_translation=term_translation,
                            terms_to_display=terms_to_display, terms_count=terms_count)

    def add_report(self, enrollment, year, level, term, 
                   students_count, grades, 
                   term_max_avg, location, position=1,
                   teacher='', director='', url='',
                   term_translation='', terms_to_display=None,
                   terms_count=0, is_last_term=None):
        
        if position == 1:
            self.add_page(orientation='L')
        
        start_x = self.x
        if position == 2:
            start_x = 157

        self.add_default_header(start_x, 5, main_utils.CYCLE_PRIMARY, True)
        orange_color = (255, 180, 150)

        self.set_font('Arial', size=12)
        self.set_xy(start_x, 32)
        self.set_auto_page_break(True, 2)

        self.set_fill_color(*orange_color)
        self.multi_cell(text=f"**BULLETIN DE NOTES: {term} ({year})**", 
                  w=130, h=8, align='C',
                  markdown=True, fill=True)
        
        self.set_font('Arial')
        self.set_fill_color(0, 0, 0)
        text = f"Matricule: **{ enrollment.student.student_id or ' ' }** >>>> **{ enrollment }** {' ' * 3} \n" \
                f"Sexe: **{ enrollment.student.get_gender_display()}** {' ' * 5} Né(e) le: **{ enrollment.student.birth_date_str() } {' ' * 10} \n" \
                f"** à **{ enrollment.student.birth_place or ' '}**{' ' * 10} Classe: **{level}**  {' ' * 5} Effectif: **{ students_count }**"
        self.ln(5)
        self.set_x(start_x)
        self.set_font_size(10)
        self.multi_cell(w=190, text=text, markdown=True, h=6)

        if position == 2:
            self.dashed_line(self.w / 2, 0, self.w / 2, 500)

        self.ln(5)
        self.set_xy(start_x, self.y)
        x = self.x
        y = self.y

        data = []
        level_max_points = 0
        summary_data = {}
        max_points_value = 0

        # Initialize grades to empty ('-') value and retrieve term data from enrollment annotation 
        grades_per_term = {}
        if terms_count:
            for i, term_obj in enumerate(terms_to_display):
                for subj in self.subjects:
                    grades_per_term[f'term_{term_obj.term_id}_subject_{subj.subject_id}'] = '-'

                summary_data[f'max_term_{term_obj.term_id}'] = 0
        for grade in grades:
            grade_value = grade.grade if (grade.grade or grade.grade == 0)  else '-'

            if (grade.grade or grade.grade == 0) and not main_utils.has_decimal_part(grade.grade):
                grade_value = int(grade_value)
                grade_value = str(grade_value).zfill(2)

            # Add grades to a dict for easy access and summary data (points, avg, rank)
            if terms_count:
                grades_per_term[f"term_{grade.school_term.term.id}_subject_{grade.subject.subject.id}"] = grade_value
                max_points_value = grade.subject.max if grade_value != '-' else 0
                current_value = summary_data.get(f'max_term_{grade.school_term.term.id}', 0)
                summary_data[f'max_term_{grade.school_term.term.id}'] =  max_points_value + current_value


            else:
                data.append(
                    [
                        grade.subject.subject.name,
                        grade_value,
                        grade.subject.max if grade_value != '-' else '-',
                    ],
                )
            level_max_points += grade.subject.max if grade_value != '-' else 0
        
        # Add grades and term results to list 
        if terms_count:
            for subject in self.subjects:
                to_append = [str(subject.subject.name)]
                for term_obj in terms_to_display:
                    grade_str = str(grades_per_term[f"term_{term_obj.term_id}_subject_{subject.subject_id}"])                        
                    to_append.append(grade_str)
                    if terms_count == 1:
                        to_append.append(subject.max)
                data.append(to_append)

            # Term results
            if terms_count > 1:
                self.add_summary_component(enrollment=enrollment, terms_to_display=terms_to_display,
                                        data=data, value_key='total_for_term_', description='TOTAL',
                                        summary_data=summary_data)
                self.add_summary_component(enrollment=enrollment, terms_to_display=terms_to_display,
                                        data=data, value_key='avg_for_term_', description='MOYENNE',
                                        summary_data=summary_data, max_value=terms_to_display[0].max)
                self.add_summary_component(enrollment=enrollment, terms_to_display=terms_to_display,
                                        data=data, value_key='rank_for_term_', description='RANG',
                                        summary_data=summary_data, max_value=students_count, 
                                        is_rank=True)
                self.add_summary_component(enrollment=enrollment, terms_to_display=terms_to_display,
                                        data=data, value_key='avg_for_term_', description='MENTION',
                                        summary_data=summary_data, is_distinction=True, 
                                        term_max_avg=term_max_avg, education=term.education)
                self.add_summary_component(enrollment=enrollment, terms_to_display=terms_to_display,
                                        data=data, value_key='avg_for_term_', description='RESULTAT',
                                        summary_data=summary_data, is_decision=True, 
                                        term_max_avg=term_max_avg, education=term.education)

        self.set_font('Arial', size=10)

        widths = [60, 25, 25]
        aligns = ['LEFT', 'CENTER', 'CENTER']

        if terms_count == 3:
            widths = [55, 25, 25, 25]
            aligns = ['LEFT', 'CENTER', 'CENTER', 'CENTER']
        elif terms_count == 4:
            widths = [50, 20, 20, 20, 20]
            aligns = ['LEFT', 'CENTER', 'CENTER', "CENTER", 'CENTER']
        bigger = FontFace(emphasis=True)
        colored = FontFace(emphasis=True, fill_color=(224, 255, 224), color=(0, 0, 0))
        line_height = 5.2
        if len(data) <= 6:
            line_height = 6

        with self.table(
            align='LEFT', width=130, col_widths=widths,
            cell_fill_mode="ROWS", cell_fill_color=(230, 230, 230),
            line_height=line_height, text_align=aligns) as table:
            row = table.row()
            row.cell('MATIERE')
            if terms_count > 1:
                for term_obj in terms_to_display:
                    row.cell(f"{str(term_obj.term.name.upper().replace('COMPOSITION', 'COMPO'))}",
                             style=FontFace(emphasis=True, size_pt=9))
            else:
                row.cell('NOTE')
                row.cell('SUR')

            print(data)
            for index, item in enumerate(data):
                row = table.row()
                for i, datum in enumerate(item):
                    if terms_count > 1 and index >= len(data) - 5:
                        row.cell(str(datum), style=colored)
                    else:
                        row.cell(str(datum))


            if terms_count > 1:
                pass
            else:
                row = table.row()
                total = enrollment.result_total or 0
                if not main_utils.has_decimal_part(total):
                    total = int(total)
                row.cell('TOTAL', style=colored)
                row.cell(f'{total} / {level_max_points}', style=colored, colspan=2)

                row = table.row()
                row.cell('MOYENNE', style=colored)
                row.cell(f'{enrollment.result_average} / {term_max_avg}', style=colored, colspan=2)
                
                row = table.row()
                row.cell('RANG', style=colored)
                row.cell(f'{main_utils.get_rank_str(enrollment.result_rank, enrollment.result_is_ex)} / {students_count}', 
                        style=colored, colspan=2)
                
                row = table.row()
                row.cell('MENTION', style=colored)
                distinction = ''
                
                distinction = main_utils.compute_distinction(
                    enrollment.result_average, max=term_max_avg,
                    education=main_utils.EDUCATION_ARABIC)

                row.cell(f'{distinction}', style=colored, colspan=2)
                
                row = table.row()
                row.cell('RESULTAT DE LA COMPO.', style=colored)
                decision = 'N. Admis'
                admitted = enrollment.result_average and (enrollment.result_average) >= (term_max_avg / 2)
                if admitted:
                    decision = 'Admis'

                row.cell(f'{decision}', style=colored, colspan=2)

            if self.is_last_term:
                row = table.row()
                annual_avg = enrollment.__dict__.get('annual_avg')
                annual_rank = enrollment.__dict__.get('annual_rank')
                annual_is_ex = enrollment.__dict__.get('annual_is_ex')
                annual_rank_str = main_utils.get_rank_str(annual_rank, is_ex=annual_is_ex)
                annual_distinction_fr = main_utils.compute_distinction(annual_avg, term_max_avg, 
                                        education=term.education, 
                                        return_language=main_utils.EDUCATION_FRENCH)
                annual_decision_fr = main_utils.get_decision(annual_avg, term_max_avg, True)

                if annual_decision_fr == 'A':
                    annual_decision_fr = 'ADMIS' + f' AU {self.next_level}' if self.next_level else 'ADMIS'
                else:
                    annual_decision_fr = 'REDOUBLE'
                
                if annual_avg:
                    annual_avg = round(annual_avg, 2)
                else:
                    annual_avg = '-'

                row.cell(colspan=len(widths), text=f'Moy. Annuelle : {annual_avg} / {int(term_max_avg)}  ****** RANG : {annual_rank_str} ', 
                         style=FontFace(emphasis=True, fill_color=orange_color), padding=(0.7, 0.7),
                         align='CENTER')
                row = table.row()
                row.cell(colspan=len(widths), text=f'Mention : {annual_distinction_fr}  -> {annual_decision_fr}', 
                         style=FontFace(emphasis=True, fill_color=orange_color), padding=(0.7, 0.7),
                         align='CENTER')

        # Add photo to report
        student = enrollment.student
        photo_url = student.photo.url if student.photo else ''
        if not photo_url:
            photo_url = student.blank_photo()[1:]
        print(photo_url)
        try:
            self.image(photo_url, x + 110, y - 25, w=20, h=23)
        except:
            print('An error occured while loading photo. Check your internet connection')
            pass

        self.set_font('Arial', size=10)
        self.set_xy(start_x + 55, self.y + 5)
        self.set_font_size(11)
        self.cell(text=f'{location}, le {main_utils.get_date_str()}', w=75, align='RIGHT')

        self.set_font('Arial')
        self.ln()
        self.set_x(start_x)
        y = self.y
        self.multi_cell(w=60, text=f"--Le tenant de la classe--\n** {teacher[:20]} **", markdown=True, h=9,
                        align='LEFT')

        self.set_font('Arial')
        self.ln()
        self.set_xy(start_x + 70, y)
        self.multi_cell(w=60, text=f"--Le directeur--\n **{director[:20]}**", markdown=True, h=9, align='RIGHT')
        
        qr_text = f"EcolePro \nBulletin de notes {student.student_id or ''}\n{student}\n{term} / {level} / {year}\n" \
                  f"Vous pouvez télécharger ce bulletin en suivant "\
                  f"le lien ci-dessous avec [un compte autorisé]:\n{url}"
        self.set_xy(self.x - 70, y)
        qr = qrcode.make(qr_text)
        self.image(qr.get_image(), x=self.x, y=self.y, w=18, h=18)

        # self.ln(4)

        self.set_font_size(8)
        copyright_symbol = '\u00A9'
        text = f"{copyright_symbol} EcolePro {year}\n"
        text += '__Ce bulletin de notes est à conserver soigneusement__.'
        self.set_xy(start_x, -10)
        self.multi_cell(w=140, align='CENTER', text=text, markdown=True)


# class PrimaryReportFr(school_reports.Document):
#     def add_content(self, user, queryset, term, level):
#         year = level.year
#         students_count = level.get_students_count()
#         self.school = user.school
#         director = self.school.director_fr
#         qs = TeacherLevel2.objects.filter(level=level)
#         teacher = ''

#         if qs.exists():
#             teacher = str(qs.first().teacher)[:30]
#         term_max = term.max
#         for i, enrollment in enumerate(queryset):
#             position = 1
#             if (i + 1) % 2 == 0:
#                 position = 2

#             grades = enrollment.filtered_grades
#             report_url = f'https://www.ecolepro.ci/examens/bulletin/?eleve={enrollment.id}&periode={term.id}'
#             self.add_report(enrollment=enrollment, year=year, level=level, 
#                             term=term, term_max_avg=term_max, students_count=students_count, 
#                             grades=grades, location=user.school.location,
#                             position=position, teacher=teacher, director=director,
#                             url=report_url)

#     def add_report(self, enrollment, year, level, 
#                    term, term_max_avg,
#                    students_count, grades, 
#                    location, position=1,
#                    teacher='', director='', url=''):
        
#         if position == 1:
#             self.add_page(orientation='L')
        
#         start_x = self.x
#         if position == 2:
#             start_x = 157

#         self.add_default_header(start_x, 5, main_utils.CYCLE_PRIMARY, True)

#         self.set_font('Arial', size=12)
#         self.set_xy(start_x, 32)
#         self.set_auto_page_break(True, 2)

#         self.set_fill_color(255, 200, 0)
#         self.cell(text=f'**BULLETIN DE NOTES: {term} ({year})**', 
#                   border=1, w=130, h=8, align='C',
#                   markdown=True, fill=True)
        
#         self.set_font('Arial')
#         self.set_fill_color(0, 0, 0)
#         text = f"Matricule: **{ enrollment.student.student_id or ' ' }** >>>> **{ enrollment }** {' ' * 3} \n" \
#                 f"Sexe: **{ enrollment.student.get_gender_display()}** {' ' * 5} Né(e) le: **{ enrollment.student.birth_date_str() } {' ' * 10} \n" \
#                 f"** à **{ enrollment.student.birth_place or ' '}**{' ' * 10} Classe: **{level}**  {' ' * 5} Effectif: **{ students_count }**"
#         self.ln(12)
#         self.set_x(start_x)
#         self.set_font_size(10)
#         self.multi_cell(w=190, text=text, markdown=True, h=6)

#         if position == 2:
#             self.dashed_line(150, 0, 150, 500)

#         self.ln(5)
#         self.set_xy(start_x, self.y)
#         x = self.x
#         y = self.y

#         data = []
#         level_max_points = 0

#         for grade in grades:
#             grade_value = grade.grade  or '-'

#             if grade.grade and not main_utils.has_decimal_part(grade.grade):
#                 grade_value = int(grade_value)
#                 grade_value = str(grade_value).zfill(2)

#             data.append(
#                 [
#                     grade.subject.subject.name,
#                     grade_value,
#                     grade.subject.max if grade_value != '-' else '-'
#                 ]
#             )
#             level_max_points += grade.subject.max if grade_value != '-' else 0
        
#         line_height = 5.5
#         if len(data) <= 6:
#             line_height = 6

#         self.set_font('Arial')

#         with self.table(
#             align='LEFT', width=130, col_widths=(60, 25, 25),
#             cell_fill_mode="ROWS", cell_fill_color=(230, 230, 230),
#             line_height=line_height, text_align=('LEFT', 'CENTER', 'CENTER')) as table:
#             row = table.row()
#             row.cell('MATIERE')
#             row.cell('NOTE')
#             row.cell('SUR')

#             for item in data:
#                 row = table.row()
#                 for datum in item:
#                     row.cell(str(datum))
#             row = table.row()
#             total = enrollment.result_total or 0
#             if not main_utils.has_decimal_part(total):
#                 total = int(total)
#             row.cell('TOTAL', align='RIGHT', style=FontFace(emphasis=True))
#             row.cell(f'{total}', style=FontFace(emphasis=True))
#             row.cell(f'{level_max_points}', style=FontFace(emphasis=True))

#             bigger = FontFace(emphasis=True, color=(255, 0, 0))
#             row = table.row()
#             row.cell('< RECAPITULATIF >', colspan=3, align='CENTER')
            
#             row = table.row()
#             row.cell('MOYENNE', align='RIGHT', style=FontFace(emphasis=True))
#             row.cell(f'{enrollment.result_average} / {term_max_avg}', style=bigger, colspan=2)
            
#             row = table.row()
#             row.cell('RANG', align='RIGHT', style=FontFace(emphasis=True))
#             row.cell(f'{main_utils.get_rank_str(enrollment.result_rank, enrollment.result_is_ex)} / {students_count}', 
#                      style=bigger, colspan=2)
            
#             row = table.row()
#             row.cell('MENTION', align='RIGHT', style=FontFace(emphasis=True))
#             distinction = ''

#             result_avg = enrollment.result_average
#             try:
#                 result_avg = int(result_avg)
#             except:
#                 pass
            
#             if term_max_avg == 10:
#                 distinction = main_utils.APPRECIATIONS_SUR_10.get(str(result_avg), '')
#             elif term_max_avg == 20:
#                 distinction = main_utils.APPRECIATIONS_SUR_20.get(str(result_avg), '')

#             row.cell(f'{distinction}', style=bigger, colspan=2)
            
#             row = table.row()
#             row.cell('RESULTAT', align='RIGHT', style=FontFace(emphasis=True))
#             decision = 'Non-Admis'
#             if enrollment.result_average and (enrollment.result_average) >= (term_max_avg / 2):
#                 decision = 'Admis'

#             row.cell(f'{decision}', style=bigger, colspan=2)

#         # Add photo to report
#         student = enrollment.student
#         photo_url = student.photo.url if student.photo else ''
#         if not photo_url:
#             photo_url = student.blank_photo()[1:]
#         print(photo_url)
#         try:
#             self.image(photo_url, x + 110, y - 25, w=20, h=23)
#         except:
#             print('An error occured while loading photo')
#             pass

#         self.set_font('Arial', size=10)
#         self.set_xy(start_x + 90, self.y + 5)
#         self.set_font_size(9)
#         self.cell(text=f'{location}, le {main_utils.get_date_str()}')

#         self.set_font('Arial')
#         self.ln()
#         self.set_x(start_x)
#         y = self.y
#         self.multi_cell(w=60, text=f'--Le tenant de la classe--\n** {teacher} **', markdown=True, h=9,
#                         align='LEFT')

#         self.set_font('Arial')
#         self.ln()
#         self.set_xy(start_x + 70, y)
#         self.multi_cell(w=60, text=f'--Le Directeur--\n **{director}**', markdown=True, h=9, align='RIGHT')
        
#         qr_text = f"EcolePro \nBulletin de notes {student.student_id or ''}\n{student}\n{term} / {level} / {year}\n" \
#                   f"Vous pouvez télécharger ce bulletin en suivant "\
#                   f"le lien ci-dessous avec [un compte autorisé]:\n{url}"
#         self.set_xy(self.x - 70, y)
#         qr = qrcode.make(qr_text)
#         self.image(qr.get_image(), x=self.x, y=self.y, w=18, h=18)

#         # self.ln(4)

#         self.set_font_size(8)
#         copyright_symbol = '\u00A9'
#         text = f"{copyright_symbol} EcolePro 2023\n"
#         text += '__Ce bulletin de notes est à conserver soigneusement__.'
#         self.set_xy(start_x, -10)
#         self.multi_cell(w=140, align='CENTER', text=text, markdown=True)


class PrimaryReportAr(school_reports.Document):
    def add_content(self, user, queryset, term, level, 
                report_type=PDFFile.CATEGORY_REPORT,
                is_last_term=False):
        year = level.year
        students_count = level.get_students_count()
        self.school = user.school
        director = self.school.director_ar or ''
        qs = TeacherLevel2.objects.filter(level=level)
        teacher = ''
        self.subjects = list(models.LevelSubject.objects.for_school(
            user, level_name=level.generic_level.short_name, 
            education=term.education, year=term.year).order_by('order'))

        terms_to_display = None
        terms_count = 0
        self.next_level = ''
        next_highest_level = GenericLevel.objects.filter(order__gt=level.generic_level.order) \
            .order_by('order').first()
        if next_highest_level:
            self.next_level = str(next_highest_level)

        self.is_last_term = is_last_term
        if report_type == PDFFile.CATEGORY_REPORT_WITH_PREVIOUS_TERMS_DATA:
            terms_to_display = list(models.SchoolTerm.objects.active(
                year=year, user=user, level=level.generic_level, education=level.education
            ).filter(term__order__lte=term.term.order).order_by('term__order'))
            terms_to_display.reverse()
            terms_count = len(terms_to_display)

        if qs.exists():
            teacher = str(qs.first().teacher)[:30]
            teacher = self.reshape_text(teacher)

        term_max = term.max
        term_translation = term.term.translation
        location = user.school.exact_location or user.school.location
        for i, enrollment in enumerate(queryset):
            position = 1
            if (i + 1) % 2 == 0:
                position = 2

            grades = enrollment.filtered_grades
            report_url = f'https://www.ecolepro.net/examens/bulletin/?eleve={enrollment.id}&periode={term.id}'
            self.add_report(enrollment=enrollment, year=year, level=level, 
                            term=term, students_count=students_count, 
                            grades=grades, term_max_avg=term_max, location=location,
                            position=position, teacher=teacher, director=director,
                            url=report_url, term_translation=term_translation,
                            terms_to_display=terms_to_display, terms_count=terms_count)

    def add_report(self, enrollment, year, level, term, 
                   students_count, grades, 
                   term_max_avg, location, position=1,
                   teacher='', director='', url='',
                   term_translation='', terms_to_display=None,
                   terms_count=0, is_last_term=None):
        
        if position == 1:
            self.add_page(orientation='L')
        
        start_x = self.x
        if position == 2:
            start_x = 157

        self.add_default_header(start_x, 5, main_utils.CYCLE_PRIMARY, True)
        orange_color = (255, 180, 150)

        self.set_font('Arial', size=12)
        self.set_xy(start_x, 32)
        self.set_auto_page_break(True, 2)

        arabic_title = main_utils.TRANSLATIONS_FR_AR.get('bulletin') + ' ' + (term_translation or 'البببب')
        self.set_fill_color(*orange_color)
        self.multi_cell(text=f"**{school_reports.reshape_text(arabic_title)} \n BULLETIN DE NOTES: {term} ({year})**", 
                  border=1, w=130, h=5, align='C',
                  markdown=True, fill=True)
        
        self.set_font('Arial')
        self.set_fill_color(0, 0, 0)
        text = f"Matricule: **{ enrollment.student.student_id or ' ' }** >>>> **{ enrollment }** {' ' * 3} \n" \
                f"{school_reports.reshape_text(enrollment.student.full_name_ar or '')} \n" \
                f"Sexe: **{ enrollment.student.get_gender_display()}** {' ' * 5} Né(e) le: **{ enrollment.student.birth_date_str() } {' ' * 10} \n" \
                f"** à **{ enrollment.student.birth_place or ' '}**{' ' * 10} Classe: **{self.reshape_text(str(level))}**  {' ' * 5} Effectif: **{ students_count }**"
        self.ln(2)
        self.set_x(start_x)
        self.set_font_size(10)
        self.multi_cell(w=190, text=text, markdown=True, h=6)

        if position == 2:
            self.dashed_line(self.w / 2, 0, self.w / 2, 500)

        # self.ln(5)
        self.set_xy(start_x, self.y)
        x = self.x
        y = self.y

        data = []
        level_max_points = 0
        summary_data = {}
        max_points_value = 0
        grades_per_term = {}

        # Initialize grades to empty ('-') value and retrieve term data from enrollment annotation 
        if terms_count:
            for i, term_obj in enumerate(terms_to_display):
                for subj in self.subjects:
                    grades_per_term[f'term_{term_obj.term_id}_subject_{subj.subject_id}'] = '-'
                summary_data[f'max_term_{term_obj.term_id}'] = 0

        for grade in grades:
            grade_value = grade.grade if (grade.grade or grade.grade == 0)  else '-'

            if (grade.grade or grade.grade == 0) and not main_utils.has_decimal_part(grade.grade):
                grade_value = int(grade_value)
                grade_value = str(grade_value).zfill(2)

            # Add grades to a dict for easy access and summary data (points, avg, rank)
            if terms_count:
                grades_per_term[f"term_{grade.school_term.term.id}_subject_{grade.subject.subject.id}"] = grade_value
                max_points_value = grade.subject.max if grade_value != '-' else 0
                current_value = summary_data.get(f'max_term_{grade.school_term.term.id}', 0)
                summary_data[f'max_term_{grade.school_term.term.id}'] =  max_points_value + current_value


            else:
                data.append(
                    [
                        grade.subject.subject.name,
                        grade.subject.max if grade_value != '-' else '-',
                        grade_value,
                        school_reports.reshape_text(grade.subject.subject.translation)
                    ],
                )
            level_max_points += grade.subject.max if grade_value != '-' else 0
        
        # Add grades and term results to list 
        if terms_count:
            for subject in self.subjects:
                to_append = [str(subject.subject.name)]
                for term_obj in terms_to_display:
                    grade_str = str(grades_per_term[f"term_{term_obj.term_id}_subject_{subject.subject_id}"])
                    # if grade_str and grade_str != '-':
                    #     grade_str += f" / {subject.max}"
                    to_append.append( grade_str)
                to_append.append(self.reshape_text(subject.subject.translation))
                data.append(to_append)

            # Term results
            self.add_summary_component(enrollment=enrollment, terms_to_display=terms_to_display,
                                       data=data, value_key='total_for_term_', description='TOTAL',
                                       summary_data=summary_data, translation='مجموع الدرجات', education=term.education)
            self.add_summary_component(enrollment=enrollment, terms_to_display=terms_to_display,
                                       data=data, value_key='avg_for_term_', description='MOYENNE',
                                       summary_data=summary_data, max_value=terms_to_display[0].max,
                                       translation='المعدل', education=term.education)
            self.add_summary_component(enrollment=enrollment, terms_to_display=terms_to_display,
                                       data=data, value_key='rank_for_term_', description='RANG',
                                       summary_data=summary_data, max_value=students_count,
                                       translation='الترتيب', is_rank=True, education=term.education)
            self.add_summary_component(enrollment=enrollment, terms_to_display=terms_to_display,
                                       data=data, value_key='avg_for_term_', description='MENTION',
                                       summary_data=summary_data,
                                       translation='التقدير', is_distinction=True, 
                                       term_max_avg=term_max_avg, education=term.education)
            self.add_summary_component(enrollment=enrollment, terms_to_display=terms_to_display,
                                       data=data, value_key='avg_for_term_', description='RESULTAT',
                                       summary_data=summary_data,
                                       translation='معدل', is_decision=True, 
                                       term_max_avg=term_max_avg, education=term.education)

        line_height = 4.8
        if len(data) <= 6:
            line_height = 6

        self.set_font('Arial')

                
        if len(data) >= 12:
            line_height = 4.1
            self.set_font_size(8.6)

        widths = [45, 25, 25, 35]
        aligns = ['LEFT', 'CENTER', 'CENTER', 'RIGHT']
        rank_translation = self.reshape_text('الترتيب')

        if terms_count == 3:
            widths = [40, 20, 20, 20, 30]
            aligns = ['LEFT', 'CENTER', 'CENTER', "CENTER", 'RIGHT']
        bigger = FontFace(emphasis=True, color=(255, 0, 0))
        colored = FontFace(emphasis=True, fill_color=(224, 255, 224), color=(0, 0, 0))
        with self.table(
            align='LEFT', width=130, col_widths=widths,
            cell_fill_mode="ROWS", cell_fill_color=(230, 230, 230),
            line_height=line_height, text_align=aligns) as table:
            row = table.row()
            row.cell('MATIERE')
            if terms_count:
                for term_obj in terms_to_display:
                    row.cell(f"{self.reshape_text(term_obj.term.translation)}\n{str(term_obj.term.name).title().replace('Trimestre', 'Trim.')}",
                             style=FontFace(emphasis=True, size_pt=9))
            else:
                row.cell('TOTAL\n' + school_reports.reshape_text('الكبري'))
                row.cell('NOTE\n' + school_reports.reshape_text('المكتسبة'))
            row.cell(school_reports.reshape_text('المواد الدراسية'))
            emphasized = FontFace(emphasis=True)
            for index, item in enumerate(data):
                row = table.row()
                for i, datum in enumerate(item):
                    if (i + 1) == len(item) and (not (index >= len(data) - 5)):
                        row.cell(str(datum), style=FontFace(size_pt=self.font_size_pt + 2))
                    elif terms_count and index >= len(data) - 5:
                        row.cell(str(datum), style=colored)
                    else:
                        row.cell(str(datum))


            if terms_count:
                pass
            else:
                row = table.row()
                total = enrollment.result_total or 0
                if not main_utils.has_decimal_part(total):
                    total = int(total)
                row.cell('TOTAL', style=bigger)
                row.cell(f'{level_max_points}', style=bigger)
                row.cell(f'{total}', style=bigger)
                row.cell(school_reports.reshape_text('مجموع الدرجات'), style=bigger)

                row = table.row()
                row.cell('MOYENNE', style=bigger)
                row.cell(f'{enrollment.result_average} / {term_max_avg}', style=bigger, colspan=2)
                row.cell(school_reports.reshape_text('المعدل'), style=bigger, align='RIGHT')
                
                row = table.row()
                row.cell('RANG', style=bigger)
                row.cell(f'{main_utils.get_rank_str(enrollment.result_rank, enrollment.result_is_ex)} / {students_count}', 
                        style=bigger, colspan=2)
                row.cell(rank_translation, style=bigger, align='RIGHT')
                
                row = table.row()
                row.cell('MENTION', style=bigger)
                distinction = ''
                
                distinction = main_utils.compute_distinction(
                    enrollment.result_average, max=term_max_avg,
                    education=main_utils.EDUCATION_ARABIC)
                distinction_ar = main_utils.compute_distinction(
                    enrollment.result_average, max=term_max_avg,
                    education=main_utils.EDUCATION_ARABIC, 
                    return_language=main_utils.EDUCATION_ARABIC)

                row.cell(f'{distinction}', style=bigger)
                row.cell(f'{school_reports.reshape_text(distinction_ar)}', style=bigger)
                row.cell(school_reports.reshape_text('التقدير'), style=bigger, align='RIGHT')
                
                row = table.row()
                row.cell('RESULTAT', style=bigger)
                decision = 'N. Admis'
                admitted = enrollment.result_average and (enrollment.result_average) >= (term_max_avg / 2)
                if admitted:
                    decision = 'Admis'

                row.cell(f'{decision}', style=bigger)
                decision_ar = main_utils.get_result_translation(enrollment.student.gender, admitted)
                row.cell(f'{school_reports.reshape_text(decision_ar)}', style=bigger)
                row.cell(school_reports.reshape_text('النتيجة'), style=bigger, align='RIGHT')

            if self.is_last_term:
                row = table.row()
                last_avg_translation = self.reshape_text('معدل السنوي')
                distinction_translation = self.reshape_text('التقدير')
                annual_avg = enrollment.__dict__.get('annual_avg')
                annual_rank = enrollment.__dict__.get('annual_rank')
                annual_is_ex = enrollment.__dict__.get('annual_is_ex')
                annual_rank_str = main_utils.get_rank_str(annual_rank, is_ex=annual_is_ex)
                annual_distinction_fr = main_utils.compute_distinction(annual_avg, term_max_avg, 
                                        education=term.education, 
                                        return_language=main_utils.EDUCATION_FRENCH)
                annual_decision_fr = main_utils.get_decision(annual_avg, term_max_avg, True)
                annual_distinction_ar = self.reshape_text(main_utils.compute_distinction(annual_avg, term_max_avg, 
                                        education=term.education, 
                                        return_language=main_utils.EDUCATION_ARABIC))
                annual_decision_ar = self.reshape_text(main_utils.get_result_translation(enrollment.student.gender, 
                                            annual_decision_fr == 'A'))

                if annual_decision_fr == 'A':
                    annual_decision_fr = 'ADMIS' + f' AU {self.next_level}' if self.next_level else 'ADMIS'
                else:
                    annual_decision_fr = 'REDOUBLE'
                
                if annual_avg and str(annual_avg).isnumeric():
                    annual_avg = round(annual_avg, 2)
                row.cell(colspan=len(widths), text=f'MOY. ANNUELLE : {annual_avg} / {int(term_max_avg)} : {last_avg_translation} ****** RANG : {annual_rank_str}  : {rank_translation}', 
                         style=FontFace(emphasis=True, fill_color=orange_color), padding=(0.7, 0.7),
                         align='CENTER')
                row = table.row()
                row.cell(colspan=len(widths), text=f"MENTION : {annual_distinction_fr} / {annual_distinction_ar} : {distinction_translation}  ****** {annual_decision_fr} / {annual_decision_ar}  : {self.reshape_text('النتيجة')}", 
                         style=FontFace(emphasis=True, fill_color=orange_color), padding=(0.7, 0.7),
                         align='CENTER')

        # Add photo to report
        student = enrollment.student
        photo_url = student.photo.url if student.photo else ''
        if not photo_url:
            photo_url = student.blank_photo()[1:]
        try:
            self.image(photo_url, x + 110, y - 25, w=20, h=23)
        except:
            print('An error occured while loading photo. Check your internet connection')
            pass

        self.set_font('Arial', size=10)
        self.set_xy(start_x + 55, self.y + 5)
        self.set_font_size(11)
        self.cell(text=f'{location}, le {main_utils.get_date_str()}', w=75, align='RIGHT')

        self.set_font('Arial')
        self.ln()
        self.set_x(start_x)
        y = self.y
        self.multi_cell(w=60, text=f"--{school_reports.reshape_text('توقيع المعلم')}--\n** {teacher[:20]} **", markdown=True, h=9,
                        align='LEFT')

        self.set_font('Arial')
        self.ln()
        self.set_xy(start_x + 70, y)
        director_ar = ''
        if director:
            director_ar = self.reshape_text(director[:20])
        self.multi_cell(w=60, text=f"--{school_reports.reshape_text('توقيع المدير')}--\n **{director_ar}**", markdown=True, h=9, align='RIGHT')
        
        # if len(data) <= 12:
        qr_text = f"EcolePro \nBulletin de notes {student.student_id or ''}\n{student}\n{term} / {level} / {year}\n" \
                f"Vous pouvez télécharger ce bulletin en suivant "\
                f"le lien ci-dessous avec [un compte autorisé]:\n{url}"
        self.set_xy(self.x - 70, y)
        qr = qrcode.make(qr_text)
        self.image(qr.get_image(), x=self.x, y=self.y, w=18, h=18)

        # self.ln(4)

        self.set_font_size(8)
        copyright_symbol = '\u00A9'
        text = f"{copyright_symbol} EcolePro {year}\n"
        text += '__Ce bulletin de notes est à conserver soigneusement__.'
        self.set_xy(start_x, -10)
        self.multi_cell(w=140, align='CENTER', text=text, markdown=True)


class TermResultsAr(school_reports.Document):
    def footer(self):
        self.set_y(-20)
        self.set_text_color(0, 0, 0)
        self.cell(txt=f'Page {self.page_no()}', 
                    h=10, w=180, align='CENTER')
        
    def add_page(self, *args, **kwargs):
        super().add_page(*args, **kwargs)
        
        if self.pages_count > 1:
            self.set_text_color(0, 0, 0)
            family = self.font_family
            size = self.font_size
            self.add_table_header(self.headers)
            self.set_font(self.document_font, size=10)
            self.rotate(0)
            self.ln()
        
    def add_table_header(self, headers):
        self.set_left_margin(self.original_x)
        self.set_y(self.y + 30)
        self.original_y = self.y
        self.set_fill_color(192, 192, 192)
        self.set_font_size(10)
        for index, header in enumerate(headers):
            last_header = ''
            rotated = False
            for i, key in enumerate(header.keys()):
                y = self.original_y 
                if i == len(header) - 1:
                    last_header = key

                if index < len(headers) - 3 and index > 0:
                    self.rotate(90)
                    rotated = True
                else:
                    self.rotate(0)
                    rotated = False
                
                calculated_width = header[last_header]['width']
                print(header, calculated_width)
                height = DEFAULT_ROTATED_ITEMS_HEIGHT
                extra_width = 0
                if rotated:
                    extra_width += DEFAULT_ROTATED_ITEMS_HEIGHT - 3
                    calculated_width =  18 + extra_width
                    self.y = y
                    key = school_reports.reshape_text(key)

                else:
                    ratio = 15
                    self.y = self.original_y - ratio - DEFAULT_ROTATED_ITEMS_HEIGHT
                    height += ratio

                print('Width is', calculated_width, 'for', header)
                self.cell(calculated_width, height, txt=f'**{key}**', 
                        border=1, fill=True, markdown=True, align='CENTER')

                if rotated:
                    self.set_x(self.x - 10 - extra_width + DEFAULT_ROTATED_DIFF)
                
                if index == len(headers) - 4 or index == 0:
                    current_x = self.x
                    self.ln()
                    self.set_x(current_x)

    def use_custom_table(self, queryset, subjects_count, data, headers,
                         term, redden_girls_names=True, term_max=None):
        self.get_headers_width(headers, DEFAULT_ROTATED_ITEMS_HEIGHT)
        for index, enrollment in enumerate(queryset):
            id_or_identifier = enrollment.student.student_id \
                               or enrollment.student.identifier
            full_name_ar = enrollment.student.full_name_ar
            if full_name_ar:
                full_name_ar = school_reports.reshape_text(full_name_ar)
            else:
                full_name_ar = str(enrollment.student)

            item = [
                str(index + 1).zfill(2), id_or_identifier, 
                str(full_name_ar) or str(enrollment.student) or '',
            ]

            for grade in enrollment.filtered_grades:
                grade_value = grade.grade
                if grade_value and not main_utils.has_decimal_part(grade_value):
                    grade_value = str(int(grade_value)).zfill(2)

                if grade_value  == 0 or grade_value == 0.0 or grade_value == '0' or grade_value == '00':
                    item.append(0)
                elif not grade_value:
                    item.append('-')
                else:
                    item.append(str(grade_value))

            result_obj = enrollment.termresult_set.filter(school_term=term).first()
            result = enrollment.termresult_set.filter(school_term=term) \
                .values('average', 'rank', 'total').first()
            avg = 0
            rank = 0
            total = 0

            if result and result.get('average'):
                avg = float(result.get('average', 0))

            if result and result.get('rank'):
                rank = int(result.get('rank', 0))

            if result and result.get('total'):
                total = int(result.get('total', 0))

            item.append(str(total))
            item.append(str(avg))
            item.append(str(rank))

            if result_obj:
                item.append(result_obj.get_decision(max=term_max))
                item.append(self.reshape_text(result_obj.compute_distinction(
                    education=main_utils.EDUCATION_ARABIC, 
                    max=term_max,
                    return_language=main_utils.EDUCATION_ARABIC
                )))
            else:
                item.append('')
                item.append('')

            item.append(
                redden_girls_names and enrollment.student.gender == 'F'
            )
            data.append(item)

        # Add headers
        headers.reverse()

        if subjects_count > 2:
            self.def_orientation = 'L'

        if subjects_count <= 15:
            self.original_x = self.x_right_aligned
        else:
            self.original_x = 6
        self.add_table_header(headers)

        # Add content
        y = self.y
        self.ln()
        self.set_y(self.y - DEFAULT_ROTATED_ITEMS_HEIGHT + 8.5)
        self.rotate(0)
        for row in data:
            row.reverse()
            if row[0]:
                self.set_text_color(255, 0, 0)
            else:
                self.set_text_color(0, 0, 0)
            for index, col in enumerate(row[1:]):
                align = 'CENTER'
                width = 10
                if index == len(row) - 3:
                    width = DEFAULT_IDS_WIDTH
                elif index == len(row) - 4:
                    width = DEFAULT_NAMES_WIDTH - 10
                    align = 'RIGHT'
                elif index == 0:
                    width = 15
                else:
                    width = DEFAULT_ROTATED_ITEMS_HEIGHT

                self.cell(w=width, h=8, txt=col, border=1, align=align)
            self.ln()

    def add_content(self, user, queryset, term, level, redden_girls_names=True):
        self.document_font = 'Times'
        self.add_header(level.school, cycle=level.generic_level.cycle)
        self.set_xy(self.x + 10, 45 + 1)
        self.set_font(self.document_font, 'B', 11)
        year = level.year if queryset.exists() else ''
        title = f'RESULTATS : {term}' + f" {self.reshape_text(str(level))} ({year})"
        self.cell(w=160, h=8, txt=title, border=1, align='C')
        self.set_xy(120, self.y + 15)

        self.set_font(self.document_font, '', 10)
        summary = queryset.aggregate(
            boys=Count(
                'student', filter=Q(student__gender=main_utils.GENDER_MALE), 
                distinct=True) or 0,
            girls=Count(
                'student', filter=Q(student__gender=main_utils.GENDER_FEMALE), 
                distinct=True) or 0,
        )
        boys = summary['boys']
        girls = summary['girls']
        is_second_cycle = queryset.first().is_second_cycle_fr
        subjects_count = 0

        result_obj = models.LevelStatistics.objects.get(term=term, level=level)
        with self.table(
            align='LEFT', 
            text_align=('LEFT', 'CENTER', 'CENTER', 'CENTER'),
            col_widths=(25, 20, 20, 20),
            width=80,
            line_height=5.5) as table:
            self.add_results_component(
                table, result_obj, students={'G': boys, 'F': girls}, 
                education=main_utils.EDUCATION_ARABIC)
            
        # with self.table(
        #     align='LEFT', 
        #     text_align=('CENTER', 'CENTER', 'CENTER'),
        #     col_widths=(15, 15, 15),
        #     width=60,
        #     line_height=7) as table:
        #     self.add_students_count_component(table, boys, girls)
        
        self.set_y(self.y + 5)

        self.set_line_width(0.3)
        headings_style = FontFace(emphasis="BOLD", color=255, fill_color=(128, 128, 128))
        headers = [
            {self.reshape_text(main_utils.TRANSLATIONS_FR_AR['n°']): {'width': DEFAULT_NUMBERING_WIDTH - 1.5, 'align': 'CENTER'}},
            {self.reshape_text(main_utils.TRANSLATIONS_FR_AR['matricule']): {'width': DEFAULT_IDS_WIDTH, 'align': 'CENTER'}},
            {self.reshape_text(main_utils.TRANSLATIONS_FR_AR['nom']): {'width': DEFAULT_NAMES_WIDTH - 10, 'align': 'LEFT'}},
        ]

        data = []
        subjects_count = 0

        self.set_font_size(9.5)
        subjects = models.LevelSubject.objects.for_school(
            user, level.generic_level.short_name,
            level.education, level.year
        )

        subjects_count = subjects.count()

        width = 11
        
        if subjects_count > 10:
            self.def_orientation = 'L'
            

        for subject in subjects:
            if subjects_count <= 6:
                item = {str(subject.subject.translation): {'width': width, 'align': 'CENTER'}}
            else:
                item = {str(subject.subject.translation): {'width': width, 'align': 'CENTER'}}
                
            headers.append(item)
        headers.append({main_utils.TRANSLATIONS_FR_AR['total'] : {'width': 12, 'align': 'CENTER'}})
        headers.append({main_utils.TRANSLATIONS_FR_AR['moyenne'] : {'width': 14, 'align': 'CENTER'}})
        headers.append({main_utils.TRANSLATIONS_FR_AR['rang'] : {'width': 12, 'align': 'CENTER'}})
        headers.append({main_utils.TRANSLATIONS_FR_AR['resultat'] : {'width': 12, 'align': 'CENTER'}})
        headers.append({self.reshape_text(main_utils.TRANSLATIONS_FR_AR['mention']) : {'width': 15, 'align': 'CENTER'}})
            
        widths = []
        aligns = []
        titles = []
        for header in headers:
            for key, value in header.items():
                widths += value['width'],
                titles += key,
                aligns += value['align'],
        self.headers = headers
        self.use_custom_table(queryset, subjects_count, data, headers, term,
                            redden_girls_names, term_max=term.max)

class TermResultsSummarizedAr(school_reports.Document):
    def footer(self):
        self.set_y(-20)
        self.set_text_color(0, 0, 0)
        self.cell(txt=f'Page {self.page_no()}', 
                    h=10, w=180, align='CENTER')
        
    def add_page(self, *args, **kwargs):
        super().add_page(*args, **kwargs)
        
        if self.pages_count > 1:
            self.set_text_color(0, 0, 0)
            family = self.font_family
            size = self.font_size
            self.set_font(self.document_font, size=10)
            self.rotate(0)
            self.ln()
        

    def use_custom_table(self, queryset, subjects_count, data, headers,
                         term, redden_girls_names=True, term_max=None):
        self.get_headers_width(headers, DEFAULT_ROTATED_ITEMS_HEIGHT)
        for index, enrollment in enumerate(queryset):
            id_or_identifier = enrollment.student.student_id \
                               or enrollment.student.identifier
            full_name_ar = enrollment.student.full_name_ar
            if full_name_ar:
                full_name_ar = school_reports.reshape_text(full_name_ar)
            else:
                full_name_ar = str(enrollment.student)

            item = [
                str(index + 1).zfill(2), id_or_identifier, 
                str(full_name_ar) or str(enrollment.student) or '',
                enrollment.student.gender,
            ]

            result_obj = enrollment.termresult_set.filter(school_term=term).first()
            result = enrollment.termresult_set.filter(school_term=term) \
                .values('average', 'rank', 'total').first()
            avg = 0
            rank = 0
            total = 0

            if result and result.get('average'):
                avg = float(result.get('average', 0))

            if result and result.get('rank'):
                rank = int(result.get('rank', 0))

            if result and result.get('total'):
                total = int(result.get('total', 0))

            item.append(str(total))
            item.append(str(avg))
            item.append(str(rank))

            if result_obj:
                item.append(result_obj.get_decision(max=term_max))
                item.append(self.reshape_text(result_obj.compute_distinction(
                    education=main_utils.EDUCATION_ARABIC, 
                    max=term_max,
                    return_language=main_utils.EDUCATION_ARABIC
                )))
            else:
                item.append('')
                item.append('')

            item.append(
                redden_girls_names and enrollment.student.gender == 'F'
            )
            data.append(item)

        # Add headers
        headers.reverse()

        # if subjects_count <= 15:
        #     self.original_x = self.x_right_aligned
        # else:
        #     self.original_x = 6
        aligns = ['CENTER', 'CENTER', 'CENTER', 'CENTER', 'CENTER', 'CENTER', 'R', 'CENTER', 'CENTER']
        widths = [20, 20, 20, 20, 20, 20, 80, 40, 20]

        with self.table(
            align='LEFT', col_widths=widths,
            cell_fill_mode="ROWS", cell_fill_color=(230, 230, 230),
            line_height=6, text_align=aligns) as table:
            row = table.row()
            font_size = self.font_size
            self.set_font_size(11)
            for header in headers:
                for key in header.keys():

                    row.cell(key, style=FontFace(emphasis=True, fill_color=(230, 230, 230)))
                    
            for j, item in enumerate(data):
                row = table.row()
                if item[-1]:
                    self.set_text_color(255, 0, 0)
                else:
                    self.set_text_color(0, 0, 0)

                for i, datum in enumerate(reversed(item[:-1])):
                    row.cell(datum)
                self.set_text_color(0, 0, 0)

    def add_content(self, user, queryset, term, level, redden_girls_names=True):
        self.document_font = 'Times'
        self.add_header(level.school, cycle=level.generic_level.cycle)
        self.set_xy(self.x + 10, 45 + 1)
        self.set_font(self.document_font, 'B', 11)
        year = level.year if queryset.exists() else ''
        title = f'RESULTATS : {term}' + f" {self.reshape_text(str(level))} ({year})"
        self.cell(w=160, h=8, txt=title, border=1, align='C')
        self.set_xy(120, self.y + 15)

        self.set_font(self.document_font, '', 10)
        summary = queryset.aggregate(
            boys=Count(
                'student', filter=Q(student__gender=main_utils.GENDER_MALE), 
                distinct=True) or 0,
            girls=Count(
                'student', filter=Q(student__gender=main_utils.GENDER_FEMALE), 
                distinct=True) or 0,
        )
        boys = summary['boys']
        girls = summary['girls']
        is_second_cycle = queryset.first().is_second_cycle_fr
        subjects_count = 0

        result_obj = models.LevelStatistics.objects.get(term=term, level=level)
        with self.table(
            align='LEFT', 
            text_align=('LEFT', 'CENTER', 'CENTER', 'CENTER'),
            col_widths=(25, 20, 20, 20),
            width=80,
            line_height=5.5) as table:
            self.add_results_component(
                table, result_obj, students={'G': boys, 'F': girls}, 
                education=main_utils.EDUCATION_ARABIC)
            
        # with self.table(
        #     align='LEFT', 
        #     text_align=('CENTER', 'CENTER', 'CENTER'),
        #     col_widths=(15, 15, 15),
        #     width=60,
        #     line_height=7) as table:
        #     self.add_students_count_component(table, boys, girls)
        
        self.set_y(self.y + 5)

        self.set_line_width(0.3)
        headings_style = FontFace(emphasis="BOLD", color=255, fill_color=(128, 128, 128))
        headers = [
            {self.reshape_text(main_utils.TRANSLATIONS_FR_AR['n°']): {'width': DEFAULT_NUMBERING_WIDTH - 1.5, 'align': 'CENTER'}},
            {self.reshape_text(main_utils.TRANSLATIONS_FR_AR['matricule']): {'width': DEFAULT_IDS_WIDTH, 'align': 'CENTER'}},
            {self.reshape_text(main_utils.TRANSLATIONS_FR_AR['nom']): {'width': DEFAULT_NAMES_WIDTH - 10, 'align': 'LEFT'}},
            {self.reshape_text(main_utils.TRANSLATIONS_FR_AR['sexe']): {'width': DEFAULT_NAMES_WIDTH - 10, 'align': 'LEFT'}},
        ]

        data = []
        subjects_count = 0

        self.set_font_size(9.5)
        subjects = models.LevelSubject.objects.for_school(
            user, level.generic_level.short_name,
            level.education, level.year
        )

        subjects_count = subjects.count()

        width = 11
        
        if subjects_count > 10:
            self.def_orientation = 'L'
            
        headers.append({self.reshape_text(main_utils.TRANSLATIONS_FR_AR['total']) : {'width': 12, 'align': 'CENTER'}})
        headers.append({self.reshape_text(main_utils.TRANSLATIONS_FR_AR['moyenne']) : {'width': 14, 'align': 'CENTER'}})
        headers.append({self.reshape_text(main_utils.TRANSLATIONS_FR_AR['rang']) : {'width': 12, 'align': 'CENTER'}})
        headers.append({self.reshape_text(main_utils.TRANSLATIONS_FR_AR['resultat']) : {'width': 12, 'align': 'CENTER'}})
        headers.append({self.reshape_text(main_utils.TRANSLATIONS_FR_AR['mention']) : {'width': 15, 'align': 'CENTER'}})
            
        widths = []
        aligns = []
        titles = []
        for header in headers:
            for key, value in header.items():
                widths += value['width'],
                titles += key,
                aligns += value['align'],
        self.headers = headers
        self.use_custom_table(queryset, subjects_count, data, headers, term,
                            redden_girls_names, term_max=term.max)



class FicheTable(school_reports.Document):
    def add_content(self, queryset, level, start_number=1):
        self.set_margins(5, 5, 5)
        self.add_page()
        self.set_font('Times')
        education = level.education

        school = level.school

        if education == main_utils.EDUCATION_ARABIC:
            school = school.translation
        else:
            school = school.get_name(level.generic_level.cycle)

        level_name = str(level)
        for i, enrollment in enumerate(queryset):
            if education == main_utils.EDUCATION_ARABIC:
                self.add_student_data_arabic(
                    student=enrollment.student,
                     table_num=i + start_number, level=level_name, school=school)
            else:
                self.add_student_data_french(
                    student=enrollment.student,
                    table_num=i + start_number, level=level_name, school=school
                )
            self.dashed_line(10, self.y + 5, 200, self.y + 5, space_length=2)
            self.set_y(self.y + 10)

            if  (i + 1) % 7 == 0:
                self.add_page()
    
    def add_student_data_french(self, student, school, table_num, level):
        # self.rect(5, 5, 200, 25, round_corners=)
        self.multi_cell(
            w=200, 
            text=f"**{school}\nFICHE DE TABLE**", 
            h=5, align='CENTER', markdown=True)
        self.ln(0)
        self.set_x(35)
        self.multi_cell(
            text=f"__Numéro de table__: **{table_num}**\n__Nom et Prénoms__: **{student.get_full_name()}**        __Sexe__: **{student.gender}**  \nNé(e) le: **{student.birth_date_str()}** {' ' * 15} __Classe__: **{level}**", 
            w=180, align='LEFT', h=5, markdown=True)
        self.add_image_or_gov_img_or_avatar(
            student.photo.url if student.photo else '', student.student_id, x=10, y=self.y - 30, h=30, w=25)

    
    def add_student_data_arabic(self, student, school, table_num, level):
        # self.rect(5, 5, 200, 25, round_corners=)
        self.multi_cell(
            w=200, 
            text=school_reports.reshape_text(f"**{school}\nFICHE DE TABLE**"),
            h=5, align='CENTER', markdown=True)
        self.ln(0)
        self.set_x(20)
        txt = school_reports.reshape_text(f"**{table_num}**") + " : " + school_reports.reshape_text('رقم طاولة') + "\n" + \
            school_reports.reshape_text("**" + level + "**") + " : " + school_reports.reshape_text(" الفصل :")
        txt += school_reports.reshape_text(f"                     **{student.full_name_ar}**") + " : " + school_reports.reshape_text('إسم و لقب') + "\n"
        txt += school_reports.reshape_text(f"**{student.birth_date_str()} { str(student.birth_place_ar or student.birth_place or '')}**") + \
               " : " + school_reports.reshape_text(" تاريخ ومكان الميلاد :")
        
        self.multi_cell(
            text=txt, 
            w=180, align='RIGHT', h=5, markdown=True)
        self.add_image_or_gov_img_or_avatar(student.photo.url if student.photo else '', 
            student.student_id, x=10, y=self.y - 30, h=30, w=25)


class SecondCycleReportAr(school_reports.Document):
    def add_content(self, user, queryset, term, level, report_type=PDFFile.CATEGORY_REPORT_WITH_PREVIOUS_TERMS_DATA, is_last_term=False):
        self.year = level.year
        self.school = term.school
        self.students_count = level.enrollment_ar.filter(active=True).count()
        self.director = self.school.director_ar or ''
        teachers_qs = TeacherLevel2.objects.filter(level=level)\
            .select_related('teacher')
        self.subjects = list(models.LevelSubject.objects.for_school(
            user, level_name=level.generic_level.short_name, 
            education=term.education, year=term.year).order_by('order'))
        # for subj in self.subjects:
        #     print('--------------------------------->', str(subj), end='\n')
        
        # Terms to display
        self.term_max = term.max
        self.term_translation = term.term.translation
        self.terms_to_display = None
        self.terms_count = 0
        self.next_level = ''
        next_highest_level = GenericLevel.objects \
            .filter(order__gt=level.generic_level.order) \
            .order_by('order').first()
        if next_highest_level:
            self.next_level = str(next_highest_level)

        self.is_last_term = is_last_term
        if report_type == PDFFile.CATEGORY_REPORT_WITH_PREVIOUS_TERMS_DATA:
            self.terms_to_display = list(models.SchoolTerm.objects.active(
                year=self.year, user=user, 
                level=level.generic_level, education=level.education
            ).filter(term__order__lte=term.term.order).order_by('term__order'))
            self.terms_to_display.reverse()
            self.terms_count = len(self.terms_to_display)

        arabic_title = main_utils.TRANSLATIONS_FR_AR.get('bulletin') + \
            ' ' + (term.term.translation or 'البببب')
        school = level.school

        level_statistics = None

        if not self.is_last_term:
            level_statistics = models.LevelStatistics.objects.for_school(
                year=self.year, school=school, term=term
            ).first()
        else:
            level_statistics = models.LevelStatistics.objects.for_school(
                year=self.year, school=school, term=None
            ).first()

        self.gen_average = 0
        self.min_average = 0
        self.max_average = 0

        if level_statistics:
            self.gen_average = level_statistics.level_average or '0'
            self.min_average = level_statistics.min_average or '0'
            self.max_average = level_statistics.max_average or '0'
        
        self.teachers = {}
        self.school_location =str(school.exact_location) or str(school.location)
        self.current_date = datetime.today().strftime('%d-%m-%Y')

        if teachers_qs.exists():
            for teacher in teachers_qs:
                for subject in teacher.subjects.all():
                    self.teachers[str(subject.id)] = str(teacher.teacher)[:23]
        
        term_name = str(term)
        level_name = str(level)
        school_name = str(school)
        for enrollment in queryset:
            print(f'---------------> Generating report: {term_name} | {level_name} | {school_name[:15]} ---> {enrollment}')
            self.add_report(enrollment, term, level, title=arabic_title)

    def add_report(self, enrollment, term, level, title=''):
        self.term = term
        self.add_page()
        self.set_auto_page_break(True, 0)
        self.add_header_arabic_schools(main_utils.CYCLE_SECONDARY)
        self.set_font('Arial', size=12)
        self.set_margins(10, 5, 5)
        # self.set_fill_color(255, 213, 128)
        self.set_y(self.y + 15)
        title = school_reports.reshape_text(title)
        self.multi_cell(w=190, border=1, text=f'**{title}\nBULLETIN DE NOTES {term}**', 
                h=5, align='C', markdown=True)

        photo_url = enrollment.student.photo.url if enrollment.student.photo else ''
        self.add_image_or_gov_img_or_avatar(photo_url, enrollment.student.student_id, 
                                            x=175, y=self.y + 2, h=27, w=22)
        y = self.y
        self.set_y(y + 2)
        self.cell(w=130, text=f'**{ enrollment }**', 
                markdown=True, align='L')

        self.set_xy(100, y + 2)
        self.set_font('TimesNewRoman', size=11)
        self.cell(w=70, text=f"**{school_reports.reshape_text(enrollment.student.full_name_ar or '')}**", 
                markdown=True, align='R')

        self.ln(5)
        new_y = self.y
        self.set_font('Arial', size=11)
        self.multi_cell(text='Matricule :\nNé(e) le :' \
                            f'\nClasse :\nEffectif :', 
                            w=25, h=5, markdown=True, align='R')

        self.set_xy(35, new_y)
        self.set_font('Arial', size=11)
        self.multi_cell(text=f"{enrollment.student.student_id or '-'}\n{enrollment.student.birth_date_str()}" \
                            f" à {self.reshape_text(enrollment.student.birth_place_ar) or ' '}\n{self.reshape_text(str(level))}\n{self.students_count}", 
                            w=65, h=5, markdown=True, align='C')

        self.set_xy(100, new_y)
        self.set_font('Arial', size=11)
        self.multi_cell(text=school_reports.reshape_text('رقم تسجيل\nتاريخ ومكان الميلاد' \
                            ' \nالفصل\nعدد الطلاب'), 
                            w=30, h=5, markdown=True, align='R')
        self.ln(5)
        # self.set_y(self.y + 35)
        level_max_points = 0
        max_points_value = 0
        summary_data = {}
        grades_per_term = {}
        self.religion_data = {}
        self.religion_total = {}
        self.religion_count = {}
        self.religion_count_values = {}
        self.religion_max = {}
        self.arabic_data = {}
        self.arabic_total = {}
        self.arabic_count = {}
        self.arabic_count_values = {}
        self.arabic_max = {}

        total = 0
        if not self.terms_count:
            self.religion_data = []
            self.religion_total = 0
            self.religion_count = 0
            self.religion_count_values = 0
            self.religion_max = 0
            self.arabic_data = []
            self.arabic_total = 0
            self.arabic_count = 0
            self.arabic_count_values = 0
            self.arabic_max = 0

        self.data = []
        if not self.terms_count:
            self.data.append(
                [
                    "Matières", 
                    f"{school_reports.reshape_text('المكتسبة')}\nNote", 
                    f"{school_reports.reshape_text('الكبري')}\nTotal", 
                    f"{school_reports.reshape_text('المواد الدراسية')}", 
                    f"{school_reports.reshape_text('التوجيه')}\nAppréciations", 
                    f"{school_reports.reshape_text('المعلمون')}\nEnseignants"
                ],
            )
        else:
            to_append = ["Matières"]
            for term_obj in self.terms_to_display:
                to_append.append(self.reshape_text(term_obj.term.translation))
            
            to_append.extend(
                [
                    f"{school_reports.reshape_text('المواد الدراسية')}", 
                    f"{school_reports.reshape_text('التوجيه')}\nAppréciations", 
                    f"{school_reports.reshape_text('المعلمون')}\nEnseignants",
                ]
            )
            self.data.append(to_append)
            
        # Initialize grades and data dicts to empty values
        if self.terms_count:
            for i, term_obj in enumerate(self.terms_to_display):
                for subj in self.subjects:
                    grades_per_term[f'term_{term_obj.term_id}_subject_{subj.subject_id}'] = '-'
                    
                    if subj.subject.category == main_utils.CATEGORY_RELIGION:
                        self.religion_data[f'{subj.subject.id}'] = {'grade': '-', 'grade_value': '-', 'appreciation': '-'}
                    else:
                        self.arabic_data[f'{subj.subject.id}'] = {'grade': '-', 'grade_value': '-', 'appreciation': '-'}

                summary_data[f'max_term_{term_obj.term_id}'] = 0
                self.religion_total[f'term_{term_obj.term_id}'] = 0
                self.religion_max[f'term_{term_obj.term_id}'] = 0
                self.religion_count[f'term_{term_obj.term_id}'] = 0
                self.religion_count_values[f'term_{term_obj.term_id}'] = 0
                
                self.arabic_total[f'term_{term_obj.term_id}'] = 0
                self.arabic_max[f'term_{term_obj.term_id}'] = 0
                self.arabic_count[f'term_{term_obj.term_id}'] = 0
                self.arabic_count_values[f'term_{term_obj.term_id}'] = 0

        admitted = None
        result_ar = None
        result = None

        if not self.terms_count:
            admitted = bool(enrollment.result_average and enrollment.result_average >= (term.max / 2))
            result = main_utils.get_result_translation(
                enrollment.student.gender, 
                admitted=admitted,
                return_language=main_utils.EDUCATION_FRENCH
            )
            result_ar = school_reports.reshape_text(main_utils.get_result_translation(
                enrollment.student.gender, 
                admitted=admitted,
            ))
        else:
            admitted = {}
            result_ar = {}
            result = {}
            for i, term_obj in enumerate(self.terms_to_display):
                avg = enrollment.__dict__.get(f'avg_for_term_{i + 1}')
                admitted_value = bool(avg and avg >= (term.max / 2))
                admitted[f'term_{term_obj.term_id}'] = admitted_value

                result_ar[f'term_{term_obj.term_id}'] = school_reports.reshape_text(
                    main_utils.get_result_translation(
                    enrollment.student.gender, 
                    admitted=admitted_value)
                )
                
        # Summary by group: religion and arabic
        print('Summarizing grades for', str(enrollment), enrollment.filtered_grades)
        group_font = FontFace(emphasis=True, fill_color=(235, 235, 235), size_pt=10)
        if not self.terms_count:
            for grade in enrollment.filtered_grades:
                appreciation = school_reports.reshape_text(main_utils.compute_distinction(
                    grade.grade, grade.subject.max, main_utils.EDUCATION_ARABIC,
                    main_utils.EDUCATION_ARABIC
                ))
                
                grade_value = grade.grade if (grade.grade or grade.grade == 0)  else '-'
                if (grade.grade or grade.grade == 0) and not main_utils.has_decimal_part(grade.grade):
                    grade_value = int(grade_value)
                    
                    total += grade_value
                self.add_grades_to_dict(grade, grade_value, appreciation)
            
        else:
            for term_obj in self.terms_to_display:
                for grade in enrollment.filtered_grades:
                    if grade.school_term.term_id == term_obj.term_id:
                        grade_value = grade.grade if (grade.grade or grade.grade == 0)  else '-'
                        if (grade.grade or grade.grade == 0) and not main_utils.has_decimal_part(grade.grade):
                            grade_value = int(grade_value)
                        appreciation = school_reports.reshape_text(main_utils.compute_distinction(
                            grade.grade, grade.subject.max, main_utils.EDUCATION_ARABIC,
                            main_utils.EDUCATION_ARABIC
                        ))

                        self.add_grades_to_dict(grade, grade_value, appreciation)
                        grades_per_term[f"term_{grade.school_term.term.id}_subject_{grade.subject.subject.id}"] = grade_value
                        max_points_value = grade.subject.max if grade_value != '-' else 0
                        current_value = summary_data.get(f'max_term_{grade.school_term.term.id}', 0)
                        summary_data[f'max_term_{grade.school_term.term.id}'] =  max_points_value + current_value


        # Religion-related grades
        to_append = self.add_subject_group_summary_line(
            'المواد الدينية', self.religion_total, self.religion_count, 
            self.religion_max, self.religion_data, main_utils.CATEGORY_RELIGION,
            'Moyenne Religion', self.religion_count_values
        )
        self.data.append(to_append)

        # Arabic related grades      
        to_append = self.add_subject_group_summary_line(
            'المواد العربية', self.arabic_total, self.arabic_count, 
            self.arabic_max, self.arabic_data, main_utils.CATEGORY_ARABIC,
            'Moyenne Arabe', self.arabic_count_values
        )
        self.data.append(to_append)

        if not self.terms_count:
            result_total = enrollment.result_total
            result_avg = enrollment.result_average

            if result_total and not main_utils.has_decimal_part(result_total):
                result_total = int(result_total)
            if result_avg and not main_utils.has_decimal_part(result_avg):
                result_avg = int(result_avg)

            rank_str = main_utils.get_rank_str(enrollment.result_rank, enrollment.result_is_ex)
            summary_data= [
                ['Total', str(result_total), str(self.arabic_max + self.religion_max), school_reports.reshape_text('المجموع'), ''],
                ['Moyenne', str(result_avg), term.max, school_reports.reshape_text('المعدل'), ''],
                ['Rang', str(rank_str), self.students_count, school_reports.reshape_text('الترتيب'), ''],
                ['Résultat', f'{result}', f'{result_ar}', school_reports.reshape_text('النتيجة'), ''],
            ]
        else:
            summary_data = []

            to_append = ['Total']
            values = []
            for i, term_obj in enumerate(self.terms_to_display):
                total_value = enrollment.__dict__.get(f'total_for_term_{i + 1}')
                values.append(str(total_value) if total_value else '-')
            to_append.extend(reversed(values))
            to_append.extend([school_reports.reshape_text('المجموع'), ''])
            summary_data.append(to_append)

            to_append = ['Moyenne']
            values = []
            for i, term_obj in enumerate(self.terms_to_display):
                avg_value = enrollment.__dict__.get(f'avg_for_term_{i + 1}')
                values.append(str(avg_value) if avg_value else '-')
            to_append.extend(reversed(values))
            to_append.extend([school_reports.reshape_text('المعدل'), ''])
            summary_data.append(to_append)

            to_append = ['Rang']
            values = []
            for i, term_obj in enumerate(self.terms_to_display):
                rank_value = enrollment.__dict__.get(f'rank_for_term_{i + 1}')
                rank_value = main_utils.get_rank_str(rank_value, enrollment.__dict__.get(f'is_ex_for_term_{i + 1}'))
                values.append(str(rank_value) if rank_value else '-')
            to_append.extend(reversed(values))
            to_append.extend([school_reports.reshape_text('الترتيب'), ''])
            summary_data.append(to_append)

            to_append = ['Résultat']
            values = []
            for term_obj in self.terms_to_display:
                result_ar_value = result_ar.get(f'term_{term_obj.term_id}')
                values.append(result_ar_value)
            to_append.extend(reversed(values))
            to_append.extend([school_reports.reshape_text('النتيجة'), ''])
            summary_data.append(to_append)


        light_green = FontFace('Arial', emphasis=True, size_pt=14, fill_color=(144, 238, 144))
        footer_font = FontFace('Arial', emphasis=True, size_pt=10, fill_color=(255, 255, 255))
        footer_font_red = FontFace('Arial', emphasis=True, size_pt=10, fill_color=(235, 235, 235), color=(255, 0, 0))
        col_widths = (35, 15, 15, 30, 25, 40)
        text_align = ('LEFT', 'CENTER', 'CENTER', 'RIGHT', 'CENTER', 'RIGHT')

        if self.terms_count:
            col_widths = [35]
            text_align = ['LEFT']

            for i in range(self.terms_count):
                col_widths.append(15)
                text_align.append('CENTER')

            col_widths.extend([30, 25, 40]) 
            text_align.extend(['RIGHT', 'CENTER', 'LEFT'])      

        self.set_font_size(10)
        with self.table( 
            text_align=text_align,
            col_widths=col_widths,
            line_height=4.9,
            headings_style=group_font) as table:
                for item in self.data:
                    row = table.row()
                    if 'moyenne' in str(item[0]).lower() :
                        row.cell(str(item[0]), style=group_font)
                        if not self.terms_count > 1:
                            row.cell(f"{item[1]}", style=group_font)
                            row.cell(str(f"{item[3]}"), style=group_font, colspan=2, align='RIGHT')
                        else:
                            i = 0
                            for i in range(self.terms_count):
                                row.cell(str(f"{item[i + 1]}"), style=group_font)
                            
                            row.cell(str(item[i + 2]), style=group_font)
                        row.cell('', colspan=2, style=group_font)
                    else:
                        for col in item:
                            row.cell(str(col))

                row = table.row()
                row.cell(f"RECAPITULATIF {' ' * 72} {school_reports.reshape_text('ملخص')}" , colspan=len(self.data[0]), 
                    align='LEFT', style=FontFace(emphasis=True))
                
                for index, item in enumerate(summary_data):

                    row = table.row()
                    row.cell(str(item[0]), style=footer_font_red)

                    if not self.terms_count:
                        row.cell(f"{item[1]} / {item[2]}", style=footer_font_red, colspan=2)
                        row.cell(str(item[3]), style=footer_font_red)
                        row.cell('', style=footer_font_red, colspan=2)
                    else:
                        for i, term_obj in enumerate(self.terms_to_display):
                            row.cell(item[i + 1], style=footer_font_red)
                        row.cell(str(item[self.terms_count + 1]), style=footer_font_red)
                        row.cell('', style=footer_font_red, colspan=2)

                row = table.row()
                colspan = 2

                if self.terms_count == 3:
                    colspan = 3
                
                text = 'Résultats de la classe'
                if self.is_last_term:
                    text = "Résultat général de l'élève"

                row.cell( school_reports.reshape_text('نتائج الصف') + '\n'+ text, colspan=colspan, align='CENTER', 
                        style=footer_font)
                row.cell(school_reports.reshape_text('مجلس الفصل') + '\n' + 'Le Conseil de classe', colspan=2,
                        align='CENTER', style=footer_font)
                row.cell(school_reports.reshape_text('توقيع المدير') + '\n' + "Signature du directeur", colspan=2,
                        align='CENTER', style=footer_font)

    # Min and max average
        self.set_font_size(10) 
        self.set_x(self.x - 2.5)
        y = self.y
        width = 35+25.9
        if self.terms_count == 3:
            width += 11.581

        if not self.is_last_term:
            self.multi_cell(w=width, text=f"\nMoyenne mini     **--{self.min_average}--**     **{self.reshape_text('أدنى معدل')}**"
                        f"\n\n\nMoyenne maxi     **--{self.max_average}--**     **{self.reshape_text('أعلى معدل')}**"
                        f"\n\n\n   Moy. classe     **--{self.gen_average}--**     **{self.reshape_text('معدل الصف')}\n\n**",
                        border=1, align='C', markdown=True)
        else:
            rank_str = main_utils.get_rank_str(enrollment.annual_rank, enrollment.annual_is_ex)
            distinction = main_utils.compute_distinction(
                enrollment.annual_avg or 0, term.max, main_utils.EDUCATION_ARABIC,
                return_language=main_utils.EDUCATION_FRENCH)
            distinction_ar = main_utils.compute_distinction(
                enrollment.annual_avg or 0, term.max, 
                main_utils.EDUCATION_ARABIC, 
                return_language=main_utils.EDUCATION_ARABIC)
            self.multi_cell(w=width, text=f"\nMoyenne Annuelle     **--{enrollment.annual_avg or 'NC'}-- / {(term.max)}**     **{self.reshape_text('معدل السنوي')}**"
                        f"\n\n\nRang     **--{rank_str or 'NC'}-- / {self.students_count}**     **{self.reshape_text('ترتيب')}**"
                        f"\n\n\n   Mention     ** {distinction} / {self.reshape_text(distinction_ar)}**     **{self.reshape_text('التقدير')}\n\n**",
                        border=1, align='C', markdown=True)

        x = 35+33.45
        if self.terms_count == 3:
            x += 11.581

        self.set_xy(x, y)
        distinction = main_utils.compute_distinction(
            average=enrollment.result_average,
            max=term.max,
            education=main_utils.EDUCATION_ARABIC,
            return_language=main_utils.EDUCATION_FRENCH
        )

        distinction_ar = school_reports.reshape_text(main_utils.compute_distinction(
            average=enrollment.result_average,
            max=term.max,
            education=main_utils.EDUCATION_ARABIC,
            return_language=main_utils.EDUCATION_ARABIC
        ))
        
        width = 30+24.85
        if self.terms_count == 3:
            width -= 4.9

        if not self.is_last_term:        
            self.multi_cell(w=width, text=f"\n--Mention--\n\n\n{distinction_ar}\n**{distinction}**\n\n\n\n",
                        border=1, markdown=True, align='C')
        else:
            decision = main_utils.get_decision(enrollment.annual_avg, term.max, True)
            decision_str = ''
            if decision == 'A':
                decision_str = 'Admis(e) en classe supérieure'
            else:
                decision_str = 'Rédouble'
            
            decision_ar = self.reshape_text(main_utils.get_result_translation(enrollment.student.gender, decision == 'A'))
            font_size_pt = self.font_size_pt
            self.set_font_size(font_size_pt + 1.29)
            self.multi_cell(w=width, text=f"\n--Décision de fin d'année--\n\n\n{decision_str}\n\n**{decision_ar}**\n\n",
                        border=1, markdown=True, align='C')
            self.set_font_size(font_size_pt)
        x = 123.25
        if self.terms_count == 3:
            x += 6.85
        self.set_xy(x, y)

        width = 79.25
        if self.terms_count == 3:
            width -= 6.72

        self.multi_cell(w=width, text=f"\n** {self.director} **\n\n\n\n\n\n {self.school_location}, le {self.current_date}\n\n",
                    border=1, markdown=True, align='C')

        # Copyrigt footer
        self.set_y(-10)
        self.set_font_size(8)
        copyright_symbol = '\u00A9'
        self.multi_cell(w=190, text=f'{copyright_symbol} Copyright EcolePro 2023 | Application de gestion des écoles islamiques. \nCe bulletin est à conserver soigneusement.' 
                    'En cas de perte aucun duplicata ne sera délivré',
                    align='C')
        self.set_xy(-25, -22)
        self.image(
            qrcode.make(
                f'Copyright EcolePro {self.year} - Bulletin {self.term} - {enrollment} - Généré le {self.current_date}'
            ).get_image(), 
                x=self.x, w=18, h=18
        )

    def add_grades_to_dict(self, grade, grade_value, appreciation):
        if grade.subject.subject.category == main_utils.CATEGORY_RELIGION:
            if not self.terms_count:
                self.religion_total += (grade_value if grade.grade else 0)
                self.religion_max += grade.subject.max
                self.religion_count += grade.subject.coefficient
                self.religion_count_values += grade.subject.coefficient if grade.grade else 0
                self.religion_data.append(
                    {str(grade.subject.subject_id): {
                        'grade': grade, 'grade_value': grade_value,
                        'appreciation': appreciation,
                    }}
                )
            else:
                self.religion_total[f'term_{grade.school_term.term_id}'] += (grade_value if grade.grade else 0)
                self.religion_max[f'term_{grade.school_term.term_id}'] += grade.subject.max
                self.religion_count[f'term_{grade.school_term.term_id}'] += grade.subject.coefficient
                self.religion_count_values[f'term_{grade.school_term.term_id}'] += grade.subject.coefficient if grade.grade else 0
                self.religion_data[f'term_{grade.school_term.term_id}_subject_{grade.subject.subject_id}'] = {
                        'grade': grade, 'grade_value': grade_value,
                        'appreciation': appreciation,
                    }
        else:
            if not self.terms_count:
                self.arabic_total += (grade_value if grade_value != '-' else 0)
                self.arabic_max += grade.subject.max
                self.arabic_count += grade.subject.coefficient
                self.arabic_count_values += grade.subject.coefficient if grade.grade else 0
                self.arabic_data.append(
                    {str(grade.subject.subject_id): {
                        'grade': grade, 'grade_value': grade_value,
                        'appreciation': appreciation,
                    }}
                )
            else:
                self.arabic_total[f'term_{grade.school_term.term_id}'] += (grade_value if grade_value != '-' else 0)
                self.arabic_max[f'term_{grade.school_term.term_id}'] += grade.subject.max
                self.arabic_count[f'term_{grade.school_term.term_id}'] += grade.subject.coefficient
                self.arabic_count_values[f'term_{grade.school_term.term_id}'] += grade.subject.coefficient if grade.grade else 0
                self.arabic_data[f'term_{grade.school_term.term_id}_subject_{grade.subject.subject_id}'] = {
                        'grade': grade, 'grade_value': grade_value,
                        'appreciation': appreciation,
                }
                
    
    def add_subject_group_summary_line(
            self, group_name, group_total, group_count, 
            group_max, group_data, group_category,
            group_name_fr, group_count_values):
        
        if not self.terms_count:
            for item in group_data:
                if not self.terms_count:
                    for key in item:
                        grade = item[key]['grade']
                        grade_value = item[key]['grade_value']
                        appreciation = item[key]['appreciation']
                        teacher = self.teachers.get(str(grade.subject.id), '')
                        self.data.append(
                            [
                                str(grade.subject.subject.name), 
                                str(grade_value), 
                                str(grade.subject.max if not grade_value == '-' else '-'), 
                                school_reports.reshape_text(grade.subject.subject.translation),
                                appreciation,  
                                self.reshape_text(teacher)
                            ]
                        )
        else:
            for subject in self.subjects:
                if subject.subject.category == group_category:
                    subject_grade_for_term = [subject.subject.name]

                    for term_obj in self.terms_to_display:
                        key = f'term_{term_obj.term_id}_subject_{subject.subject_id}'
                        if key in group_data:
                            grade = group_data[key]['grade']
                            grade_value = group_data[key]['grade_value']
                            subject_grade_for_term.append(grade_value)

                    teacher = self.teachers.get(str(subject.id), '')
                    appreciation = group_data[f'term_{self.term.term_id}_subject_{subject.subject_id}']['appreciation']
                    subject_grade_for_term.extend(
                        [
                        school_reports.reshape_text(grade.subject.subject.translation),
                        appreciation,  
                        self.reshape_text(teacher)
                        ]
                    )
                    self.data.append(subject_grade_for_term)

        to_append = []
        if not self.terms_count:
            avg = round(group_total / (group_count or 1), 2)
            max_avg = group_max / (group_count or 1)

            if avg and not main_utils.has_decimal_part(avg):
                avg = int(avg)
            
            if max_avg and not main_utils.has_decimal_part(max_avg):
                max_avg = int(max_avg)
            elif max_avg:
                max_avg = round(float(max_avg), 2)


            to_append = [
                    f'{group_name_fr}', 
                    str(avg), 
                    str(max_avg), 
                    school_reports.reshape_text(group_name) + ' ' + school_reports.reshape_text(' معدل')
                ]
        else:
            to_append = [f'{group_name_fr}',]
            for term_obj in self.terms_to_display:
                avg = round(group_total[f'term_{term_obj.term_id}'] / (group_count_values[f'term_{term_obj.term_id}'] or 1), 2)
                max_avg = group_max[f'term_{term_obj.term_id}'] / (group_count[f'term_{term_obj.term_id}'] or 1)

                if avg and not main_utils.has_decimal_part(avg):
                    avg = int(avg)
                
                # if max_avg and not main_utils.has_decimal_part(max_avg):
                max_avg = int(max_avg)

                to_append.append(f'{avg}')
            to_append.append(school_reports.reshape_text(group_name) + ' ' + school_reports.reshape_text(' معدل'))
        return to_append
    

class AnnualResultFeatures(school_reports.Document):
    
    def add_annual_results_statistics(self, level, terms):
        enrollment_field = 'enrollment'
        if level.education == main_utils.EDUCATION_ARABIC:
            enrollment_field += '_ar'
        
        boys_params = {
            f'{enrollment_field}__student__gender':main_utils.GENDER_MALE,
        }
        girls_params = {
            f'{enrollment_field}__student__gender':main_utils.GENDER_FEMALE,
        }
        queryset = Level.objects.filter(id=level.id).annotate(
            boys=Count(enrollment_field, filter=Q(**boys_params), distinct=True),
            girls=Count(enrollment_field, filter=Q(**girls_params), distinct=True),
            students=F('boys') + F('girls')
        )
        first = None

        table_data = [
            ['PERIODES', 'GARCONS', 'FILLES', 'EFFECTIF', 'PRESENTS', 'ADMIS', '% ADMIS'],
        ]
        for term in terms:
            prefix = f'term_{term.id}'
            queryset = self.annotate_statistics_for_level(level, term, queryset, prefix)

            first = queryset.first()
            percentage = 0
            if first.__dict__[f'girls'] and first.__dict__[f'girls']:
                percentage = first.__dict__[f'{prefix}_students_perc']
            elif first.__dict__[f'boys']:
                percentage = first.__dict__[f'{prefix}_boys_perc']
            else:
                percentage = first.__dict__[f'{prefix}_girls_perc']
            
            table_data.append(
                [ term.term.abbreviation,
                first.boys or '-', 
                first.girls or '-', 
                first.students or '-',
                first.__dict__[f'{prefix}_students_present'] or '-',
                first.__dict__[f'{prefix}_students_admitted'] or '-',
                round(percentage, 2) if percentage else '-',]
            )
        
        prefix = 'year'
        queryset = self.annotate_statistics_for_level(level, term=None, queryset=queryset, prefix=prefix)
        first = queryset.first()
        percentage = 0
        if first.__dict__[f'girls'] and first.__dict__[f'girls']:
            percentage = first.__dict__[f'{prefix}_students_perc']
        elif first.__dict__[f'boys']:
            percentage = first.__dict__[f'{prefix}_boys_perc']
        else:
            percentage = first.__dict__[f'{prefix}_girls_perc']

        table_data.append(
            [
                'Résultat Annuel',
                first.boys or '-', first.girls or '-',
                first.students or '-',
                first.__dict__[f'{prefix}_students_present'] or '-',
                first.__dict__[f'{prefix}_students_admitted'] or '-',
                round(percentage, 2) if percentage else '-',
            ]
        )
        self.set_font('Helv', size=10)
        grayscale = 220
        if level.education == main_utils.EDUCATION_ARABIC:
            for data in table_data:
                data.reverse()

        with self.table(width=200, cell_fill_mode='ROWS', cell_fill_color=grayscale, line_height=7) as table:
            row = table.row()
            row.cell('TABLEAU RECAPITULATIF', colspan=7, align='C')
            
            row = table.row(style=FontFace(emphasis=True))
            for data in table_data[0]:
                row.cell(str(data), align='C')

            for i, data in enumerate(table_data[1:]):
                row = table.row()
                for j, item in enumerate(data):
                    row.cell(str(item), align='C')
        
    def annotate_statistics_for_level(self, level, term, queryset, prefix):
        return queryset.annotate(
            **{f'{prefix}_boys_admitted': \
                Subquery(models.LevelStatistics.objects.filter(
                    term=term, level=level
                ).only('boys_admitted').values('boys_admitted')[:1])
            },
            **{f'{prefix}_girls_admitted': \
                Subquery(models.LevelStatistics.objects.filter(
                    term=term, level=level
                ).only('girls_admitted').values('girls_admitted')[:1])
            },
            **{f'{prefix}_students_admitted': \
                F(f'{prefix}_boys_admitted') + \
                F(f'{prefix}_girls_admitted')
            },
            **{f'{prefix}_boys_present': \
                Subquery(models.LevelStatistics.objects.filter(
                    term=term, level=level
                ).only('boys_present').values('boys_present')[:1])
            },
            **{f'{prefix}_girls_present': \
                Subquery(models.LevelStatistics.objects.filter(
                    term=term, level=level
                ).only('girls_present').values('girls_present')[:1])
            },
            **{f'{prefix}_students_present': \
                F(f'{prefix}_boys_present') + \
                F(f'{prefix}_girls_present')
            },
            **{f'{prefix}_boys_perc': \
                Subquery(models.LevelStatistics.objects.filter(
                    term=term, level=level
                ).only('boys_perc').values('boys_perc')[:1])
            },
            **{f'{prefix}_girls_perc': \
                Subquery(models.LevelStatistics.objects.filter(
                    term=term, level=level
                ).only('girls_perc').values('girls_perc')[:1])
            },
            **{f'{prefix}_students_perc': (F(f'{prefix}_boys_perc') + F(f'{prefix}_girls_perc')) / 2
            },
        )

class AnnualResultsFr(AnnualResultFeatures):
    def add_content(self, level, terms, user, queryset=None):
        self.add_page()
        self.add_header(school=level.school, cycle=level.generic_level.cycle)
        self.set_auto_page_break(True, 10)
        self.set_margins(10, 10, 5)
        self.set_xy(20, self.y + 15)
        self.set_font('Helv', size=10)
        # self.set_fill_color(255, 213, 128)
        self.multi_cell(w=self.w - 40, border=1, text=f'**MATRICE DES MOYENNES {str(level).upper()}**', 
                h=8, align='C', markdown=True)
        self.ln(10)

        # Add annual results stats components
        self.add_annual_results_statistics(level, terms)
        self.ln(10)

        is_primary_fr = main_utils.is_primary_fr(level)
        last_term = grades_utils.get_last_term(level, user)
        last_term_id = last_term.id
        last_term_coefficient = last_term.coefficient

        widths = (8, 18, 45,)
        for term in terms:
            if not is_primary_fr or not term.id == last_term_id :
                widths += (10, 10, )
            else:
                widths += (15, 15, )

        if not is_primary_fr:
            widths += (10, 10, 15, 7,)
        else:
            widths += (10, 10, 10, 10)


        grayscale = 200
        grayscale_150 = 230
        self.set_font('Helv', size=10)

        aggregated = models.SchoolTerm.objects.filter(
            school__id=level.school_id, 
            education=level.education,
            level__id=level.generic_level_id,
            active=True) \
        .aggregate(
            max_total=Sum('max'),
            coefs=Sum('coefficient'),
            coefs_count=Count('id')
        )

        max_total = aggregated['max_total'] or 1
        coefs = aggregated['coefs'] or 1
        coefs_count = aggregated['coefs_count'] or 1
        terms_max = max_total / (coefs_count)

        with self.table(
            col_widths=widths, first_row_as_headings=True, 
            align='CENTER',
            cell_fill_mode='ROWS', cell_fill_color=grayscale_150,
            num_heading_rows=2, 
            headings_style=FontFace(fill_color=grayscale, emphasis=True)) as table:

            # heading
            row = table.row()
            row.cell('N°', align='C', rowspan=2)
            row.cell('MATRICULE', align='C', rowspan=2)
            row.cell('NOM ET PRENOMS', align='C', rowspan=2)

            for term in terms:
                if term.id != last_term_id or not is_primary_fr:
                    row.cell(f'{term.term.abbreviation}', colspan=2, align='C')
                else:
                    row.cell('Moy. Classe', align='C', style=FontFace(size_pt=9))
                    row.cell('Moy. Pass.', align='C', style=FontFace(size_pt=9))
                    row.cell('Total', align='C', style=FontFace(size_pt=9))

            if not is_primary_fr:
                row.cell('Résultats Ann.', colspan=4, align='C')
            else:
                row.cell('Résultats Ann.', colspan=3, align='C')

            row = table.row()
            for i, term in enumerate(terms):
                if not (is_primary_fr and term.id == last_term_id):
                    row.cell('Moy.', align='C')
                    row.cell('Rang', align='C')
                else:
                    text = '+'.join([str(f'C{ j + 1}') for j in range(i)])
                    row.cell(f'{text}/{i}', align='C', style=FontFace(size_pt=8))
                    row.cell(f'C{i + 1} x {last_term_coefficient}', align='C')
                    row.cell('-', align='C') 
            row.cell('MGA', align='C')
            row.cell('Rang', align='C')

            if not is_primary_fr:
                row.cell('Mention', align='C')

            row.cell('DFA', align='C')
            
            # Content
            for i, enrollment in enumerate(queryset):
                row = table.row()
                row.cell(str(i + 1).zfill(2), align='C')
                row.cell(f"{enrollment.student.student_id or ''}", align='C')
                row.cell(f'{enrollment.student}')

                for i, term in enumerate(terms):
                    if term.id != last_term_id or not is_primary_fr:
                        avg = enrollment.__dict__[f'avg_{i}']
                        rank = enrollment.__dict__[f'rank_{i}']
                        is_ex = enrollment.__dict__[f'is_ex_{i}']

                        rank_str = main_utils.get_rank_str(rank, is_ex)
                        avg_str = ''
                        if avg:
                            avg_str = round(avg, 2)
                        else:
                            rank_str = '-'
                        row.cell(f"{avg_str or '-'}", align='C')
                        row.cell(f"{rank_str}", align='C')
                    else:
                        total_avg = (enrollment.class_average or 0) + (enrollment.admission_average or 0) * last_term_coefficient
                        row.cell(str(enrollment.class_average if enrollment.class_average else '-'), align='C')
                        if enrollment.admission_average:
                            row.cell(str(float(enrollment.admission_average) * last_term_coefficient), align='C')
                        else:
                            row.cell('-', align='C')
                        row.cell(str(total_avg or '-'), align='C')

                avg = enrollment.__dict__['mga']
                rank = enrollment.__dict__['mga_rank']
                is_ex = enrollment.__dict__['mga_is_ex']

                rank_str = main_utils.get_rank_str(rank, is_ex)
                if not avg:
                    rank_str = '-'
                row.cell(f"{enrollment.mga or '-'}", align='C')
                row.cell(f"{rank_str}", align='C')
                distinction = main_utils.compute_distinction(
                    float(enrollment.mga or 0), max=terms_max, 
                    education=level.education)

                if not is_primary_fr:
                    row.cell(distinction, align='C')
                row.cell(f"{enrollment.mga_decision or models.EducationYearResult.DECISION_STAYS_DOWN}", align='C')
    

# Todo: adapt to arabic
class AnnualResultsAr(AnnualResultFeatures):
    def add_content(self, level, terms, queryset=None):
        self.add_page()
        self.add_header(school=level.school, cycle=level.generic_level.cycle)
        self.set_auto_page_break(True, 10)
        self.set_margins(10, 10, 5)
        self.set_xy(20, self.y + 30)
        self.set_font('TimesNewRoman', size=10)
        # self.set_fill_color(255, 213, 128)
        self.multi_cell(w=self.w - 40, border=1, 
                text=f"**RESULTATS DE FIN D'ANNEE {str(level).upper()}**", 
                h=8, align='C', markdown=True)
        self.ln(4)

        self.add_annual_results_statistics(level, terms)
        self.ln(10)

        widths = [8, 18, 45,]
        for term in terms:
            widths.append(10)
            widths.append(10)

        widths.append(10)
        widths.append(10)
        widths.append(15)
        widths.append(7)
        # widths += (10, 10, 15, 7,)

        widths.reverse()

        grayscale = 200
        grayscale_150 = 230
        self.set_font('Helv', size=10)

        aggregated = models.SchoolTerm.objects.filter(
            school__id=level.school_id, 
            education=level.education,
            level__id=level.generic_level_id,
            active=True,
            year=level.year) \
        .aggregate(
            max_total=Sum('max'),
            coefs=Sum('coefficient'),
            coefs_count=Count('id')
        )

        max_total = aggregated['max_total'] or 1
        coefs = aggregated['coefs'] or 1
        coefs_count = aggregated['coefs_count']
        terms_max = max_total / coefs_count

        # Content
        table_data = []
        for i, enrollment in enumerate(queryset):
            data = []
            data.append(str(i + 1).zfill(2))
            data.append(f"{enrollment.student.student_id or ''}")

            if enrollment.student.full_name_ar:
                data.append(self.reshape_text(enrollment.student.full_name_ar))
            else:
                data.append(f'{enrollment.student}')

            for i, term in enumerate(reversed(terms)):
                avg = enrollment.__dict__[f'avg_{i}']
                rank = enrollment.__dict__[f'rank_{i}']
                is_ex = enrollment.__dict__[f'is_ex_{i}']

                rank_str = main_utils.get_rank_str(rank, is_ex)
                if not avg:
                    rank_str = '-'

                data.append(f"{avg or '-'}")
                data.append(f"{rank_str}")

            avg = enrollment.__dict__[f'mga']
            rank = enrollment.__dict__[f'mga_rank']
            is_ex = enrollment.__dict__[f'mga_is_ex']

            rank_str = main_utils.get_rank_str(rank, is_ex)
            if not avg:
                rank_str = '-'
            data.append(f"{enrollment.mga or '-'}")
            data.append(f"{rank_str}")

            distinction = main_utils.compute_distinction(
                float(enrollment.mga), max=terms_max, 
                education=level.education, 
                return_language=level.education)
            data.append(self.reshape_text(distinction))
            data.append(f"{enrollment.mga_decision or models.EducationYearResult.DECISION_STAYS_DOWN}")
            table_data.append(data)

        self.set_font('TimesNewRoman', size=10)
        with self.table(
            col_widths=widths, first_row_as_headings=True, 
            align='CENTER',
            cell_fill_mode='ROWS', cell_fill_color=grayscale_150,
            num_heading_rows=2, 
            headings_style=FontFace(fill_color=grayscale, emphasis=True)) as table:

            # New
            row = table.row()
            row.cell('Résultats Ann.', colspan=4, align='C')
            for term in reversed(terms):
                row.cell(f'{term.term.abbreviation}', colspan=2, align='C')

            row.cell('NOM ET PRENOMS', align='C', rowspan=2)
            row.cell('MATRICULE', align='C', rowspan=2)
            row.cell('N°', align='C', rowspan=2)

            row = table.row()
            row.cell('DFA', align='C')
            row.cell('Mention', align='C')
            row.cell('Rang', align='C')
            row.cell('MGA', align='C')

            for term in reversed(terms):
                row.cell('Rang', align='C')
                row.cell('Moy.', align='C')
            
            # Table data
            names_col_index = len(table_data[0]) - 3
            for data in table_data:
                reversed_data = reversed(data)
                row = table.row()
                i = 0
                for item in reversed_data:
                    align = 'C'
                    text = str(item)
                    if i == names_col_index or i == 1:
                        align = 'R'
                        text = item
                    row.cell(text, align=align)
                    i += 1
            

class CombinedResultsArabic(school_reports.Document):
    def add_content(self, queryset, level, subjects, terms):
        self.school = level.school
        self.subjects = list(subjects)
        self.grades = {}
        self.terms = terms
        self.term_max = terms.first().max / 2
        self.set_margins(5, 5, 5)
        self.set_auto_page_break(True, margin=10)
        self.add_page(orientation='L')
        self.add_default_header(self.x, self.y, level.generic_level.cycle)

        # Add grades to dict for easy access
        queryset = list(queryset)
        for enrollment in queryset:
            for grade in enrollment.filtered_grades:
                self.grades[f'enrollment_{enrollment.id}_term_{grade.school_term.id}_subject_{grade.subject.id}'] = grade.grade

        name_key = self.reshape_text(main_utils.TRANSLATIONS_FR_AR.get('nom'))
        headers = {}

        aligns = []
        widths = []
        for subject in reversed(self.subjects):
            width = 10
            headers[self.reshape_text(str(subject))] = width    
            aligns.append('CENTER')
            widths.append(width)
        widths.extend([20, 5])
        aligns.extend(['RIGHT', 'CENTER'])
        headers[name_key] = 20
        headers['#'] = 5
        self.ln(10)
        
        self.cell(
            w=self.w - 10, align='CENTER', 
            text=f'NOTES GENERALES PAR MATIERE - {str(level).upper()}', 
            border=True,
            h=8
        )

        self.ln(10)
        self.set_font_size(10)

        with self.table(
            col_widths=widths, text_align=aligns, cell_fill_mode='ROWS',
            cell_fill_color=220) as table:
            
            row = table.row()
            for key in headers.keys():
                row.cell(key)

            self.set_font_size(11)
            for i, enrollment in enumerate(queryset):
                self.add_data(enrollment, table, i+1)

    def add_data(self, enrollment, table, counter):
        row = table.row()
        self.set_font('ArialNarrow', size=11)
        for subject in reversed(self.subjects):
            # self.set_font_size(12)
            grades = []
            stays_down = False
            grades_total = 0
            grades_count = 0
            grade_exists = False
            subject_total = 0
            for term in self.terms:
                grade = self.grades.get(f'enrollment_{enrollment.id}_term_{term.id}_subject_{subject.id}')
                if grade or grade == 0:
                    grade_exists = True
                    grades_total += grade
                    subject_total += grade
                    grades_count += 1
                    if not main_utils.has_decimal_part(grade):
                        grade = str(int(grade))
                elif not grade:
                    grade = 'N'

                grades.append(grade)
            grades_str = '-'.join(grades)
            stays_down = (grades_total / (grades_count or 1)) < self.term_max
            if stays_down and grade_exists:
                self.set_text_color(255, 0, 0)
            if not main_utils.has_decimal_part(grades_total):
                grades_total = int(grades_total)
            row.cell(f'{grades_str} =>{grades_total}')
            self.set_text_color(0, 0, 0)
        
        self.set_text_color(0, 0, 0)
        self.set_font('Times', size=14)
        row.cell(str(self.reshape_text(enrollment.student.full_name_ar)))
        row.cell(str(counter))


border_color_schemes = {
    'default': (68, 117, 161),
    'dark_blue': (19, 79, 255),
    'orange': (255, 119, 9),
    'green': (0, 176, 80),
}

class Certificate(school_reports.Document):
    def generate(self, enrollment, school, level, max_avg):
        self.enrollment = enrollment
        self.student = enrollment.student
        self.level = level.short_name
        self.school = school
        self.school_logo = self.school.logo.url if self.school.logo else "static/img/logo.png"
        self.max_avg = max_avg
        self.border_color = self.get_color()

        self.add_page()
        self.add_border()
        if self.school.left_header and self.school.right_header:
            self.add_header()
        else:
            self.add_default_header(self.x + 15, self.y + 15, main_utils.CYCLE_SECONDARY, adjust_position=False)
        self.add_title()
        self.add_french_content()
        self.add_arabic_content()
        self.add_photo()
        self.add_signatures()

    def get_exam_translation(self):
        if self.level == 'CM2':
            return main_utils.CEPE_TRANSLATION
        elif self.level == '3EME':
            return main_utils.BEPC_TRANSLATION
        elif self.level == 'TLE':
            return main_utils.BAC_TRANSLATION
    
    def get_color(self):
        if self.level == 'CM2':
            return "default"
        elif self.level == '3EME':
            return "orange"
        elif self.level == 'TLE':
            return "green"
    
    def get_exam(self):
        if self.level == 'CM2':
            return "CERTIFICAT D'ETUDES PRIMAIRES ELEMENTAIRES"
        elif self.level == '3EME':
            return "BREVET D'ETUDES DU PREMIER CYCLE"
        elif self.level == 'TLE':
            return "BACCALAUREAT ISLAMIQUE"

    def get_exam_desc(self):
        if self.level == 'CM2':
            return "primaires"
        elif self.level == '3EME':
            return 'secondaires du 1er cycle'
        elif self.level == 'TLE':
            return 'secondaires du 2nd cycle'
    
    def add_border(self):
        self.image(f'static/img/borders/cepe/{self.border_color}.png', 2, 2, self.w - 5, self.h - 5)

        with self.local_context(fill_opacity=0.25):
            self.image(self.school_logo, 50, self.h / 2, 40)
            self.image(self.school_logo, self.w - 90, self.h / 2, 40)

    def add_header(self):
        text = self.school.left_header
        self.set_xy(25, 25)
        self.set_font_size(10)
        self.multi_cell(text=text, w=self.w / 3.5, align='C')
        
        if self.school.logo:
            self.image(self.school_logo, h=25, x=(self.w / 2) - 10, y=25)

        self.set_xy(self.w - 115, 25)
        self.set_font_size(12)
        text = self.school.right_header
        self.multi_cell(text=self.reshape_text(text), w=self.w / 3.5, align='C')

    def add_title(self):
        self.ln(5)
        # self.set_xy(self.w / 3 + 20, 60)
        self.set_line_width(1)
        self.set_draw_color(border_color_schemes[f'{self.border_color}'])
        self.rect(self.w / 2 - 105, 65, 180, 20, round_corners=('TOP_RIGHT', 'BOTTOM_LEFT'))

        self.set_xy(self.w / 2 - 75, 68)
        self.set_font_size(24)
        self.set_text_color(border_color_schemes[f'{self.border_color}'])
        text = "**" + self.get_exam_translation() + "**"
        self.multi_cell(text=self.reshape_text(text), w=120, align='C', markdown=True)
        
        self.set_xy(self.w / 2 - 75, 76)
        self.set_font_size(18)
        self.set_text_color(border_color_schemes[f'{self.border_color}'])
        with self.local_context(text_mode="STROKE", line_width=1):
            self.cell(text=self.get_exam(), w=120, align='C')

        code = qrcode.make(
            "Diplome Généré par l'Application EcolePro: www.ecolepro.net\n"
            f"{self.school}\n"
            f"{self.get_exam()} Numéro {self.enrollment.certificate_num}\n"
            f"{self.student}\n"
            f"Né(e) le {self.student.birth_date_str()} à {self.student.birth_place}\n"
        )
        self.image(code.get_image(), self.w - 70, 60, 30, 30)
        # text = "BREVET D'ETUDES DU PREMIER CYCLE"
        # self.multi_cell(text=self.reshape_text(text), w=120, align='C', markdown=True)

    def add_french_content(self):
        self.set_font_size(12)
        self.set_xy(27, 90)
        self.set_text_color(0, 0, 0)
        mention = main_utils.compute_distinction(self.enrollment.average, self.max_avg, 
                                                 education=main_utils.CATEGORY_ARABIC, 
                                                return_language=main_utils.EDUCATION_FRENCH)
        text = f"""La Direction de  l'école **{self.school}** 
Certifie  que  l'élève : **--{self.student}--**
né (e)  le **{self.student.birth_date_str() or '....../...../..........'}** à **{self.student.birth_place or '..................................'}**
a terminé   ses  études  {self.get_exam_desc()}  et  passé
avec  satisfaction  son examen  final  de l'année
académique **{self.enrollment.year}** avec  mention **{mention}**
En  foi  de  quoi , nous  délivrons  le  présent  certificat
    Enregistre  sous  le  N˚ **{self.enrollment.certificate_num or '2024/001'}**
    Fait  le  **{datetime.today().strftime('%d/%m/%Y')}**
        """
        self.multi_cell(text=text, markdown=True, h=6.5, w=self.w / 2 - 15)

    def add_arabic_content(self):
        self.set_font_size(12)
        self.set_xy(self.w / 2 + 13, 90)
        self.set_text_color(0, 0, 0)
        student = self.student.full_name_ar
        school = self.school.translation
        birth_day = str(self.student.birth_day).zfill(2)
        birth_month = str(self.student.birth_month).zfill(2)
        birth_year = self.student.birth_year
        school_year = self.enrollment.year
        mention = main_utils.compute_distinction(self.enrollment.average,
                                                 max=self.max_avg, 
                                                 education=main_utils.CATEGORY_ARABIC, 
                                             return_language=main_utils.EDUCATION_ARABIC)
        code = self.enrollment.certificate_num or '2024/001'
        current_date = datetime.today().strftime('%d\%m\%Y')
        birth_place = self.student.birth_place_ar or '................................'
        exam = self.get_exam_translation().split(' ')[1]
        text = f"""تشــــــهد إدارة **{school}** 
بأن الطالب (ة)   **{student}** 
المولود (ة) في  **{birth_place}**  بتأريخ  **{birth_day or '.....'}**\**{birth_month or '.....'}**\**{birth_year or '..........'}**م
قـد أتم  (ت) دراستــه ( ها ) **{exam}** ، ونجح  (ت) في الاختبار النهـــــــــائي 
للعام الدراســي  **{school_year}** بتقدير **{mention}** 
ولذا منح (ت) هذه الشهــادة ليتمتع بالحقوق  والامتيازات المتعلقة بها .
والله نسأل  له ( لها ) التوفيق والسداد 
     سجلت برقم : **{code}** 
     التاريخ : **{current_date}**م 
**"""
        self.multi_cell(text=self.reshape_text(text), markdown=True, h=6.5, 
                        w=self.w / 2 - 40, align='R')

    def add_photo(self):
        self.image(self.enrollment.photo_url_or_blank_photo(), 
                   self.w / 2 - 15, w=27, h=30, y=self.y - 30)

    def add_signatures(self):
        self.ln(6)
        y = self.y
        self.set_x(25)
        self.multi_cell(
            text="**" + self.reshape_text("توقيع صاحب الشهادة") + "**\n" + "Le  Titulaire", 
            w=self.w / 3 - 20, align='C', markdown=True, h=6
        )

        self.set_xy(self.w / 3 - 16, y)
        self.multi_cell( 
            text="**" + self.reshape_text("توقيع مدير المدرسة") + "**\n" + "Le  Directeur", 
            w=self.w / 2 - 20, align='C', markdown=True, h=6
        )

        self.set_xy(self.w / 2 + 20, y)
        self.multi_cell( 
            text="**" + self.reshape_text("الختم الرسمي") + "**\n" + "Cachet officiel", 
            w=self.w / 2 - 20, align='C', markdown=True, h=6    
        )