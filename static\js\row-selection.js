
  // Row selection highlighting
  function countChecked() {
    // Initialize a counter variable
  var checkedCount = 0;
  allChecked = document.body.querySelector('#select-all').checked;

  for (var i = 0; i < tableRows.length; i++) {
    var row = tableRows[i];
    var checkbox = row.querySelector('input[type="checkbox"]');
      if (checkbox.checked) {
        checkedCount++; // Increment the counter if checked
      }
    }
  return checkedCount;
  }

  var tableRows = document.body.querySelector('.checkbox-table').querySelectorAll('tr.checkbox');
  var selectedCount = 0;
  tableRows.forEach(row => {
    row.addEventListener('click', function() {
      updateCheckedStatus(this, false)
    });
    row.addEventListener('change', function() {
      updateCheckedStatus(this, true);
    });
  });

  // (un)Check all
  var selectAll = document.body.querySelector('#select-all');
  selectAll.addEventListener('change', function() {
    resultElement = document.body.querySelector('#selected');
    var selectAllChecked = selectAll.checked;
    tableRows.forEach(row => {
      checkbox = row.querySelector('.row-checkbox');
      if (selectAllChecked) {
        if (!row.classList.contains('highlight')) {
          row.classList.add('highlight');
        }
      } else {
        if (checkbox.checked && !row.classList.contains('highlight')) {
          row.classList.add('highlight');
        } else {
          row.classList.remove('highlight');
        }
      }
    })

    setTimeout(
      function() {
        resultElement.innerHTML = countChecked(false) + " élément(s) sélectionnés";
        toggleSubmitBtn(countChecked());
      }, 1
    )

  })

function toggleSubmitBtn(checkedCount) {
  if (checkedCount > 0) {
      document.body.querySelector('#action_submit').disabled = false;
    } else {
      document.body.querySelector('#action_submit').disabled = true;
  }
}

function updateCheckedStatus(tr, checkTrue) {
  const checkbox = tr.querySelector('input[type="checkbox"]'); 
    var checkedItems = 0;
    var checked = false;
    if (checkTrue) {
      checked = checkbox.checked;
    } else {
      checked = !checkbox.checked;
    } 

    if (checked) {
      if (tr.id !== 'select-all-input') {
        tr.classList.add('highlight');
      }
      checkedItems = countChecked();
      if (!checkTrue) checkedItems++;
      toggleSubmitBtn(checkedItems);
    } 
    else {
      if (tr.id !== 'select-all-input') {
        tr.classList.remove('highlight');
      }
      
      checkedItems = countChecked();
      if (!checkTrue) checkedItems--;
      toggleSubmitBtn(checkedItems);
    }

    resultElement = document.body.querySelector('#selected');
    resultElement.innerHTML = checkedItems + " élément(s) sélectionnés";
}
