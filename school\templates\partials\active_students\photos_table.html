<table class="table table-striped table-sm table-hover table-bordered" 
    {% if active_nav == 'ar' or active_nav == 'fr' %} id="datatable" {% endif %}>
    <thead class="bg-primary text-white">
    <tr>
        <th>Photo</th>
        <th style="min-width: 120px;">Nom et Prénoms</th>
        <th>Mod</th>
    </tr>
    </thead>
    <tbody>
        {% for enrollment in enrollments %}
            <tr>
                <td class="align-middle text-center" style="min-width: 60px;" 
                    hx-target="this" hx-trigger="saved from:body" 
                    hx-get="{% url 'school:photo_url' enrollment.student.id %}">
                    {% if enrollment.student.photo %}
                        <img data-original="{{ enrollment.student.photo.url }}" 
                            class="lazy border img-thumbnail rounded-circle">
                    {% elif enrollment.is_second_cycle_fr and enrollment.student.student_id %}
                        <img data-original="{{ enrollment.student.government_photo }}" 
                            class="lazy border img-thumbnail rounded-circle"
                            id="{{ enrollment.id }}"
                            onload="if (this.src.endsWith('CC')) {
                            this.src = '{{ enrollment.student.blank_photo }}'
                            }"
                            onerror="this.src = '{{ enrollment.student.blank_photo }}'">
                    {% else %}
                        <img data-original="{{ enrollment.student.blank_photo }}" 
                            class="lazy border img-thumbnail rounded-circle">
                    {% endif %}
                  </td>
                <td class="align-middle">{{ enrollment }} <br>
                    <span class="text-muted">{{ enrollment.student.student_id|default_if_none:''}}</span></td>
                <td class="align-middle" hx-target="this">
                    <a href="" class="btn btn-sm btn-warning" 
                       hx-get="{% url 'school:photo_import' enrollment.student.id %}">Modifier</a>
                </td>
            </tr>
        {% endfor %}
</tbody>
</table>

<script>
    if (typeof(feather) != "undefined") {
    feather.replace();
    $("img.lazy").lazyload();

  }
    document.addEventListener("DOMContentLoaded", function() {
	    $("img.lazy").lazyload();
    });
</script>