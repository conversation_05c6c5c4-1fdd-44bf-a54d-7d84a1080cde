""" Defines tabs items """
from django.urls import reverse, reverse_lazy
from main.utils import EDUCATION_ARABIC, EDUCATION_FRENCH

# Students list tabs

def get_students_navs(url):
    return [
        {
            'description': 'Elèves inscrits', 
            'url': f'{url}?statut=inscrits', 
            'active_nav': 'inscrits',
            'icon': 'check-square',
        },
        {
            'description': 'Non-inscrits', 
            'url': f'{url}?statut=non-inscrits', 
            'active_nav': 'non-inscrits',
            'icon': 'x',
        },
    ]

def get_payments_summary_navs(user, url, period_url, level_url):
    navs = [
        {
            'description': 'Entrées du jour', 
            'url': f'{url}?periode=aujourdhui', 
            'active_nav': 'day_payments',
            'icon': 'sun',
        },
        {
            'description': 'Toutes les Entrées', 
            'url': f'{url}?periode=tout', 
            'active_nav': 'all_payments',
            'icon': 'file-text',
        },
        {
            'description': 'Par classe', 
            'url': f'{level_url}?lang=fr', 
            'active_nav': 'classe',
            'icon': 'columns',
        },
    ]

    if user.school.education == EDUCATION_ARABIC:
        navs.append(
            {
                'description': 'Par classe arabe', 
                'url': f'{level_url}?lang=ar', 
                'active_nav': 'classe_ar',
                'icon': 'columns',
            },
        )

    navs.append(
        {
            'description': 'Sélect. une période', 
            'url': period_url, 
            'active_nav': 'period_payments',
            'icon': 'calendar',
        },
    )
    return navs

def get_payments_list_navs(url):
    return [
        {
            'description': 'Aujourdhui', 
            'url': f'{url}?periode=aujourdhui', 
            'active_nav': 'day_payments'
        },
        {
            'description': '100 Derniers', 
            'url': f'{url}?periode=recents', 
            'active_nav': 'recent_payments'
        },
    ]

def get_pricing_navs(user, url, category_url):
    arabic_school = user.school.education == EDUCATION_ARABIC
    navs = []

    if not user.school.pricing_option == 0:
        navs.append(
            {
                'description': "Français", 
                'url': f'{url}?education=fr', 
                'active_nav': 'pricing_fr'
            },
        )

    if arabic_school:
        navs.append({
            'description': f"Arabe", 
            'url': f'{url}?education=ar', 
            'active_nav': 'pricing_ar'
        })

    # navs.append({
    #     'description': f"Rubriques", 
    #     'url': category_url, 
    #     'active_nav': 'category'
    # })

    return navs

def get_levels_navs(user, url):
    arabic_school = (user.school.education == EDUCATION_ARABIC)
    navs = [
        {
            'description': "Classes " + ('françaises' if arabic_school else ''), 
            'url': f'{url}?education=fr', 
            'active_nav': 'fr'
        },
    ]

    if arabic_school:
        navs.append({
            'description': f"Classes arabes", 
            'url': f'{url}?education=ar', 
            'active_nav': 'ar'
        })
    return navs

def get_levels_stats_navs(user, url):
    arabic_school = (user.school.education == EDUCATION_ARABIC)
    navs = [
        {
            'description': "Effectifs " + ('français' if arabic_school else ''), 
            'url': f'{url}?education=fr', 
            'active_nav': 'fr'
        },
    ]

    if arabic_school:
        navs.append({
            'description': f"Effectifs arabes", 
            'url': f'{url}?education=ar', 
            'active_nav': 'ar'
        })
    return navs

def get_active_students_navs(user, url, import_url='', export_url='', show_list=True):
    navs = []

    if show_list:
        navs.append({
            'description': 'Elèves inscrits', 
            'url': url, 
            'active_nav': 'all',
            'icon': 'users'
        })
    else:
        import_url = f'{import_url}?nolist'
        export_url = f'{export_url}?nolist'
        
    navs.extend(
        [
            {
                'description': 'Importer', 
                'url': import_url, 
                'active_nav': 'import',
                'icon': 'arrow-up'
            },
            {
                'description': 'Exporter Excel',
                'url': export_url,
                'active_nav': 'export',
                'icon': 'arrow-down'
            }
        ]
    )
    return navs

def level_list_navs(url, user):
    navs = [
        {
            'description': 'Listes', 
            'url': f'{url}?education=fr', 
            'active_nav': 'fr'
        },
    ]

    if user.school.education == EDUCATION_ARABIC:
        navs.append({
            'description': 'Listes arabe', 
            'url': f'{url}?education=ar', 
            'active_nav': 'ar'
        })


def level_attribution_navs(url, user):
    navs = [
        {
            'description': 'Attribuer classes', 
            'url': f'{url}?education=fr', 
            'active_nav': 'fr'
        },
    ]

    if user.school.education == EDUCATION_ARABIC:
        navs.append({
            'description': 'Attribuer classes arabe', 
            'url': f'{url}?education=ar', 
            'active_nav': 'ar'
        })
    return navs

def students_photos_navs(url, user):
    navs = [
        {
            'description': 'Elèves Récents', 
            'url': f'{url}?recents', 
            'active_nav': '',
            'icon': 'rotate-ccw'
        },
        {
            'description': 'Par classe', 
            'url': f'{url}?education=fr', 
            'active_nav': 'fr'
        },
    ]

    if user.school.education == EDUCATION_ARABIC:
        navs.append({
            'description': 'Par classes arabes', 
            'url': f'{url}?education=ar', 
            'active_nav': 'ar'
        })
    return navs


def get_imports_nav(url):
    return [
        {
            'description': 'Importation élèves', 
            'url': url, 
            'active_nav': 'students'
        }
    ]

def get_balance_nav(url, expenses_url):
    return [
        {
            'description': 'Solde Actuel', 
            'url': f'{url}?type=solde', 
            'active_nav': 'solde'
        },
        {
            'description': 'Dépenses', 
            'url': f'{expenses_url}', 
            'active_nav': ''
        },
    ]


def get_teachers_nav(user, url):
    is_arabic_school = user.school.education == EDUCATION_ARABIC
    nav = [
        {
            'description': 'Français' if is_arabic_school else ' Liste' , 
            'url': f'{url}?lang=F', 
            'active_nav': EDUCATION_FRENCH
        },
    ]

    if is_arabic_school:
        nav.append(
            {
                'description': 'Arabe', 
                'url': f'{url}?lang=A', 
                'active_nav': EDUCATION_ARABIC
            },
        )
    
    return nav


def get_student_detail_navs(url, user):
    navs = [
        {
            'description': 'Identité', 
            'url': url, 
            'active_nav': None
        },
        {
            'description': 'Paiement Scolarité', 
            'url': f'{url}?section=paiements', 
            'active_nav': 'paiements'
        },
        {
            'description': 'Moyennes', 
            'url': f'{url}?section=moyennes', 
            'active_nav': 'moyennes'
        },
    ]

    if user.school.education == EDUCATION_ARABIC:
        navs.append(
            {
                'description': 'Moyennes Arabes', 
                'url': f'{url}?section=moyennes_arabes', 
                'active_nav': 'moyennes_arabes'
            },
        )
    return navs


def get_settings_nav(url):
    return [
        {
            'description': 'Ecole', 
            'url': f'{url}?section=ecole', 
            'active_nav': 'ecole'
        },
        {
            'description': 'Direction', 
            'url': f'{url}?section=direction', 
            'active_nav': 'direction'
        },
        {
            'description': 'En-têtes documents', 
            'url': f'{url}?section=headers', 
            'active_nav': 'headers'
        },
        # {
        #     'description': 'Mon compte', 
        #     'url': f'{url}?section=compte', 
        #     'active_nav': 'compte'
        # },
    ]


def sms_nav(url):
    return [
        {
            'description': 'Solde', 
            'url': f'{url}', 
            'active_nav': 'balance'
        },
    ]

def get_staff_nav(url, roles_url, payment_options_url, payments_url):
    return [
        {
            'description': 'Liste du Personnel', 
            'url': f'{url}', 
            'active_nav': 'staff',
            'icon': 'users',
        },
        {
            'description': 'Emplois', 
            'url': f'{roles_url}', 
            'active_nav': 'staff_roles',
            'icon': 'grid',
        },
        {
            'description': 'Rubriques de Paie', 
            'url': f'{payment_options_url}', 
            'active_nav': 'payment_options',
            'icon': 'list',
        },
        {
            'description': 'Paiements des salaires', 
            'url': f'{payments_url}', 
            'active_nav': 'paiements',
            'icon': 'dollar-sign',
        },
        # {
        #     'description': 'Mon compte', 
        #     'url': f'{url}?section=compte', 
        #     'active_nav': 'compte'
        # },
    ]