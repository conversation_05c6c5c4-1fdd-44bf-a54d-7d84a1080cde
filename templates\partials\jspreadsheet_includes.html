<!-- jSpreadsheet CSS and JS -->
<script src="https://cdn.jsdelivr.net/npm/jspreadsheet-ce@4/dist/index.min.js"></script>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/jspreadsheet-ce@4/dist/jspreadsheet.min.css" type="text/css" />
<script src="https://cdn.jsdelivr.net/npm/jsuites/dist/jsuites.min.js"></script>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/jsuites/dist/jsuites.min.css" type="text/css" />
<style>
    /* Custom styles for jspreadsheet */
    .jexcel {
        border-top: 1px solid #ccc;
        border-left: 1px solid #ccc;
    }
    
    .jexcel > thead > tr > td {
        background-color: #007bff !important;
        color: white !important;
        font-weight: bold;
    }
    
    .jexcel > tbody > tr > td {
        padding: 4px;
        vertical-align: middle;
    }
    
    .jexcel > tbody > tr:nth-child(even) > td {
        background-color: #f8f9fa;
    }
    
    .jexcel > tbody > tr:hover > td {
        background-color: #e9ecef;
    }
    
    .jexcel .highlight {
        background-color: #e3f2fd !important;
    }
    
    .jexcel .editor {
        border: 2px solid #007bff !important;
    }
    
    .jexcel_toolbar {
        background-color: #f8f9fa;
        padding: 10px;
        border-radius: 4px;
        margin-bottom: 10px;
    }
    
    /* Responsive adjustments */
    @media (max-width: 768px) {
        .jexcel_content {
            overflow-x: auto;
        }
    }
</style>
