import os, datetime
from datetime import date
from typing import Any
from django.core import validators
from django.conf import settings
from django.core.cache import cache
from django.db import models
from django.db.models.query import QuerySet
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from django.db.models.aggregates import Sum, Count
from django.db.models import F, Q, Subquery, OuterRef
from django.templatetags.static import static
import cloudinary
from cloudinary.models import CloudinaryField
import ssl
from decouple import config 
from school import school_utils
from main import utils as custom_utils
from main import sms

cloudinary.config(
    cloud_name=config('CLOUDINARY_CLOUD_NAME'),
    api_key=config('CLOUDINARY_API_KEY'),
    api_secret=config('CLOUDINARY_SECRET_KEY')
)

class YearManager(models.Manager):
    def get_queryset(self):
        cache_key = 'years'
        cached = cache.get(cache_key)
        if cached:
            return cached
        qs = super().get_queryset()
        cache.set(cache_key, qs, timeout=60 * 5)
        return qs

class Year(models.Model):
    name = models.PositiveSmallIntegerField(_('abbréviation'), 
           help_text=_('Ex: 2023'), unique=True, db_index=True)
    full_name = models.CharField(_('libellé'), max_length=9, 
                help_text=_('Ex: 2022-2023'), unique=True)
    active = models.BooleanField(_('en cours'), default=False)
    previous = models.ForeignKey('self', on_delete=models.PROTECT, null=True, blank=True,
                related_name='previous_year', verbose_name=_('année précédente'))
    comment = models.CharField(max_length=255)
    objects = YearManager()

    class Meta:
        verbose_name = _('année scolaire')
        verbose_name_plural = _('années scolaires')

    def __str__(self):
        return self.full_name
    

# class LocationManager(models.Manager):
#     def get_queryset(self):
#         cache_key = 'locations'
#         cached = cache.get(cache_key)
#         if cached:
#             return cached
#         qs = super().get_queryset()
#         cache.set(cache_key, qs, timeout=60 * 5)
#         return qs


class Location(models.Model):
    code = models.CharField(max_length=255)
    name = models.CharField(_('DRENA'), max_length=255)
    dren = models.CharField(max_length=255, null=True, blank=True)
    
    class Meta:
        verbose_name = _('DRENA')
        verbose_name_plural = _('DRENA')
        ordering = ['name']

    def __str__(self):
        return self.name
    

class Founder(custom_utils.TimeStampedModel):
    name = models.CharField(_('nom et prénoms'), max_length=255)
    phone = models.CharField(_('contact'), max_length=255)

    class Meta:
        verbose_name = 'fondateur'
        ordering = ['name']

    def __str__(self):
        return f'{self.name} ({self.phone})'


class SchoolManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().defer(
            'sms_phone', 'sms_sent', 'sms_message'
        )

class School(custom_utils.TimeStampedModel):
    PRICING_BY_ARABIC = 0
    PRICING_BY_FRENCH = 1
    PRICING_BY_HIGHEST_LEVEL = 2
    PRICING_BY_LOWEST_LEVEL = 3
    PRICING_BY_BOTH = 4
    PRICING_CHOICES = (
        (PRICING_BY_ARABIC, 'Selon la classe arabe'),
        (PRICING_BY_FRENCH, 'Selon la classe française'),
        (PRICING_BY_HIGHEST_LEVEL, 'Selon le plus haut niveau'),
        (PRICING_BY_LOWEST_LEVEL, 'Selon le plus bas niveau'),
        (PRICING_BY_BOTH, 'Combiner les frais des deux classes'),
    )

    CYCLE_CHOICES = (
        (custom_utils.CYCLE_PRIMARY, 'Primaire'),
        (custom_utils.CYCLE_SECONDARY, 'Secondaire'),
        (custom_utils.CYCLE_BOTH, 'Primaire et Secondaire'),
    )

    STATUS_PRIVATE = 'PV'
    STATUS_PUBLIC = 'PB'
    STATUS_CHOICES = (
        (STATUS_PRIVATE, 'Privé'),
        (STATUS_PUBLIC, 'Publique'),
    )

    name = models.CharField(_("nom de l'école"), max_length=255, help_text="Dénomination de l'école")
    name_secondary = models.CharField(_('nom au secondaire'), 
                                      max_length=255, null=True, 
                                      blank=True)
    translation = models.CharField(_('traduction en arabe'), max_length=255, 
                  null=True, blank=True, help_text=_('pour les écoles islamiques'))
    location = models.ForeignKey(Location, verbose_name=_('DRENA'), 
               on_delete=models.PROTECT)
    exact_location = models.CharField('Localité', max_length=255, null=True)
    email = models.EmailField(null=True, blank=True)
    phone1 = models.CharField(_('contact 1'), max_length=14)
    phone2 = models.CharField(_('contact 2'), max_length=14, 
             null=True, blank=True)
    code = models.CharField(max_length=255, null=True, blank=True,
                            help_text=_("Laissez vide si vous n'en avez pas."))
    cycle = models.CharField(
        max_length=1, choices=custom_utils.CYCLE_CHOICES,
        default=custom_utils.CYCLE_BOTH
    )
    status = models.CharField(
            _("statut de l'école"), max_length=2, 
            choices=STATUS_CHOICES,
            default=STATUS_PRIVATE)
    education = models.CharField(_("type d'école"), max_length=1, 
               choices=custom_utils.EDUCATION_CHOICES, 
               default=custom_utils.EDUCATION_ARABIC)
    address = models.CharField(
        _('addresse postale'), max_length=255, 
        null=True, blank=True)
    director_fr = models.CharField(
        _('directeur'), max_length=255, null=True, blank=True)
    director_ar = models.CharField(
        _('directeur arabe'), max_length=255, null=True, blank=True)
    director_secondary = models.CharField(
        _('directeur des études'), max_length=255, 
        null=True, blank=True)
    founder = models.ForeignKey(Founder, on_delete=models.PROTECT, 
              verbose_name=_('fondateur'), null=True, blank=True)
    pricing_option = models.PositiveSmallIntegerField(
        _('calculer le coût de la scolarité selon'), 
        choices=PRICING_CHOICES, default=PRICING_BY_BOTH)
    association = models.CharField(_('Association Islamique'),
        max_length=2, choices=custom_utils.ASSOCIATION_CHOICES,
        default=custom_utils.ASSOCIATION_OEECI,
        null=True, blank=True, help_text=_('pour les écoles islamiques'))
    
    IEP = models.CharField(_('IEPP'), max_length=255, null=True, blank=True, 
            help_text=_('pour les écoles primaires'))
    secteur_p = models.CharField(
        _('secteur pédagogique'), max_length=255,
        null=True, blank=True, 
        help_text=_('pour les écoles primaires'))
    use_class_average = models.BooleanField('Utiliser moy. classe pour calculs au primaire', default=True)

    # Files 
    logo = CloudinaryField("logo", folder='school/school_photos/', 
            null=True, blank=True)
    header_img = CloudinaryField("entête", 
                 folder='school/schools_headers/', null=True, blank=True)
    header_img_secondary = CloudinaryField("entête au secondaire", 
                 folder='school/schools_headers/', null=True, blank=True)
    header_img_primary = CloudinaryField("entête au primaire", 
                 folder='school/schools_headers/', null=True, blank=True)
    left_header = models.TextField('En-tête-gauche', null=True, blank=True)
    right_header = models.TextField('En-tête-droite', null=True, blank=True)
    cherifla_id = models.PositiveSmallIntegerField(null=True, blank=True)
    sms_phone = models.CharField(max_length=10, null=True, blank=True)
    sms_message = models.TextField(null=True, blank=True)
    sms_sent = models.BooleanField(default=True)
    def get_header_img(self, cycle):
        if cycle == custom_utils.CYCLE_PRIMARY and \
            self.header_img_primary:
            return self.header_img_primary 
        elif cycle == custom_utils.CYCLE_SECONDARY and \
            self.header_img_secondary:
            return self.header_img_secondary 
        return self.header_img

    class Meta:
        verbose_name = _('école')
        ordering = ['name']

    def __str__(self):
        return self.name

    def get_subjects(self, year, education=None):
        queryset = self.levelsubject_set.filter(year=year)
        if education:
            queryset = queryset.filter(education=education)
        return queryset
    
    def get_terms(self, year):
        return self.schoolterm_set.filter(year=year)

    def get_name(self, cycle):
        name = ''
        if cycle and cycle == custom_utils.CYCLE_SECONDARY:
            if self.name_secondary:
                name = self.name_secondary
            else:
                name = self.name.upper() \
                    .replace('EPC', 'COLLEGE PRIVE') \
                    .replace('EPP', 'COLLEGE PRIVE') \
                    .replace('EPV', 'COLLEGE PRIVE')
        else:
            name = self.name.upper()
        return f'{name} {self.exact_location or self.location}'


class Subschool(custom_utils.TimeStampedModel):
    name = models.CharField(_('nom'), max_length=255)
    name_ar = models.CharField(_('nom arabe'), max_length=255, null=True, blank=True)
    school = models.ForeignKey(School, on_delete=models.CASCADE)
    cycle = models.CharField(
        max_length=1, choices=custom_utils.CYCLE_CHOICES,
        default=custom_utils.CYCLE_BOTH
    )
    is_main_school = models.BooleanField(default=False)

    class Meta:
        verbose_name = _('sous-école')
        verbose_name_plural = _('sous-écoles')
        ordering = ['school', 'name']

    def __str__(self):
        return self.name
    

class SchoolSettings(models.Model):
    FORMULA_BY_EDUCATION='E'
    FORMULA_BY_DIVISION='D'
    FORMULA_CHOICES = (
        (FORMULA_BY_EDUCATION, 'Par langue'),
        (FORMULA_BY_DIVISION, 'Par la méthode: (ARABE + FRANCAIS) / 2'),
    )
    annual_average_formula = models.CharField(max_length=1, 
        choices=FORMULA_CHOICES, default=FORMULA_BY_EDUCATION)
    year = models.ForeignKey(Year, on_delete=models.CASCADE)
    school = models.ForeignKey(School, on_delete=models.CASCADE)

    class Meta:
        unique_together = ['year', 'school']


class SubscriptionManager(models.Manager):
    def get_queryset(self):
        cached_data = cache.get('subscriptions') 
        if cached_data:
            return cached_data

        qs = super().get_queryset()
        cache.set('subscriptions', qs, timeout=60*2)
        return qs


class Subscription(custom_utils.TimeStampedModel):
    PLAN_TYPE_TEST = 'T'
    PLAN_TYPE_PAID = 'P'
    PLAN_TYPES_CHOICES = (
        (PLAN_TYPE_TEST, 'Test'),
        (PLAN_TYPE_PAID, 'Premium'),
    )

    PLAN_SCHOOL = 'S'
    PLAN_LEVEL = 'L'
    PLAN_CHOICES = (
        (PLAN_SCHOOL, 'Standard - Pour gérer une école'),
        (PLAN_LEVEL, 'Basic - Pour gérer une classe'),
    )
    plan_type = models.CharField(max_length=1, choices=PLAN_TYPES_CHOICES,
                                  default=PLAN_TYPE_PAID)
    plan = models.CharField(max_length=1, choices=PLAN_CHOICES,
                            default=PLAN_SCHOOL, verbose_name=_("Type d'abonnement: Ecole ou Classe"))
    school = models.ForeignKey(School, on_delete=models.CASCADE)
    year = models.ForeignKey(Year, on_delete=models.CASCADE)
    level = models.ForeignKey('GenericLevel', on_delete=models.PROTECT, 
                              null=True, blank=True, 
                              verbose_name=_('classe à gérer')
                            )
    objects = SubscriptionManager()

    class Meta:
        verbose_name = _('Abonnement Annuel')
        verbose_name_plural = _('Abonnements Annuels')

    def __str__(self):
        plan = self.get_plan_display().split('-')[0]
        return f"Abonnement {self.get_plan_type_display()} {plan} {self.year}"
    
    def save(self, *args, **kwargs):
        cache_key = 'subscriptions'
        if cache.get(cache_key):
            cache.delete(cache_key)
        return super().save(*args, **kwargs)


class SubscriptionPayment(custom_utils.TimeStampedModel):
    subscription = models.ForeignKey(Subscription, on_delete=models.CASCADE)
    amount = models.PositiveSmallIntegerField(_('montant'))
    
    class Meta:
        verbose_name = _('Versement pour abonnement')
        verbose_name_plural = _('Versements pour abonnements')

    def __str__(self):
        return f'Versement abonnement {self.subscription} - {self.amount}'
    

class GenericLevelManager(models.Manager):
    def for_school(self, user, year):
        qs = self.get_queryset()
        subscription = user.get_school_subscription(
            school_id=user.school_id, year_id=year.name)
        
        if subscription and subscription.plan ==  Subscription.PLAN_LEVEL:
            education = user.school.education
            if education == custom_utils.EDUCATION_FRENCH:
                qs = qs.filter(id=subscription.level.id)
            else:
                qs = qs.filter(id=subscription.level.id)
        qs = qs
        school = user.get_school()
        if school and school.cycle != custom_utils.CYCLE_BOTH:
            qs = qs.filter(cycle=school.cycle)
        return qs
    
    def get_queryset(self):
        cache_key = 'generic_levels'
        cached = cache.get(cache_key)
        if cached:
            return cached
        qs = super().get_queryset()
        cache.set(cache_key, qs, timeout=60 * 5)
        return qs


class GenericLevel(models.Model):
    CYCLE_PRIMARY = 'P'
    CYCLE_SECONDARY = 'S'
    CYCLE_CHOICES = (
        (CYCLE_PRIMARY, 'Primaire'),
        (CYCLE_SECONDARY, 'Secondaire'),
    )
    name = models.CharField(_('nom du niveau'), max_length=255, 
           unique=True)
    short_name = models.CharField(_('abbréviation'), max_length=10, 
                 unique=True, db_index=True)
    order = models.PositiveSmallIntegerField(_('ordre'))
    cycle = models.CharField(max_length=1, choices=CYCLE_CHOICES, 
            default=CYCLE_PRIMARY)
    objects = GenericLevelManager()
    
    class Meta:
        verbose_name = _('niveau')
        verbose_name_plural = _('niveaux')
        ordering = ['order']

    def __str__(self):
        return self.short_name


class EducationLevelMaxAverage(models.Model):
    year = models.ForeignKey(Year, on_delete=models.CASCADE)
    school = models.ForeignKey(School, on_delete=models.CASCADE)
    level = models.ForeignKey(GenericLevel, on_delete=models.CASCADE)
    max = models.PositiveSmallIntegerField(default=20)

    class Meta:
        verbose_name = _('moyenne maximale par niveau')
        verbose_name_plural = _('moyennes maximales par niveau')


class LevelManager(models.Manager):
    def for_user(self, user, school=None, year=None,
        education=custom_utils.EDUCATION_FRENCH,
        with_education=True, with_files=False,
        file_type=None, term=None, minimum_fields=True):
        school_id = None
        if not school:
            school = user.school
            school_id = user.school_id
        else:
            school_id = school.id

        if not year:
            year = school_utils.get_current_year()
        qs = super().get_queryset().filter(year=year, school__id=school_id)
        
        subscription = user.get_school_subscription(
            school_id=user.school_id, year_id=year.name)
        if subscription and subscription.plan ==  Subscription.PLAN_LEVEL:
            qs = qs.filter(generic_level=subscription.level.id)

        if with_education:
            qs = qs.filter(education=education)
        
        related_objs = ['generic_level', 'year', 'school']
        if with_files:
            qs = qs.select_related(*related_objs)

            # if term:
            #     qs = qs.filter(pdffile__term=term)
            qs = qs.annotate(
                is_clean=Subquery(
                    PDFFile.objects.filter(
                        level=OuterRef('pk'),
                        term=term, category=file_type).values('is_clean')[:1]
                ),
                file_path=Subquery(
                    PDFFile.objects.filter(
                        level=OuterRef('pk'),
                        term=term, category=file_type).values('path')[:1]
                ),
                file_id=Subquery(
                    PDFFile.objects.filter(
                        level=OuterRef('pk'),
                        term=term, category=file_type).values('id')[:1]
                ),
            )

            if file_type == PDFFile.CATEGORY_REPORT:
                 file_type == PDFFile.CATEGORY_REPORT_WITH_PREVIOUS_TERMS_DATA
                 qs = qs.annotate(
                    is_clean2=Subquery(
                        PDFFile.objects.filter(
                            level=OuterRef('pk'),
                            term=term, category=file_type).values('is_clean')[:1]
                    ),
                    file_path2=Subquery(
                        PDFFile.objects.filter(
                            level=OuterRef('pk'),
                            term=term, category=file_type).values('path')[:1]
                    ),
                    file_id2=Subquery(
                        PDFFile.objects.filter(
                            level=OuterRef('pk'),
                            term=term, category=file_type).values('id')[:1]
                    ),
                )
        else:
            qs = qs.select_related(*related_objs)
        
        if minimum_fields:
            qs = qs.only(
                'id', 'number', 'max',
                'generic_level__short_name', 
                'generic_level__order',
                'generic_level__cycle',
                'school__cycle', 
                'year__name')
        return qs.order_by('generic_level__order', 'number')


class Level(models.Model):
    year = models.ForeignKey(Year, on_delete=models.CASCADE)
    school = models.ForeignKey(School, on_delete=models.CASCADE)
    subschool = models.ForeignKey(Subschool, on_delete=models.SET_NULL, 
                                   null=True, blank=True, verbose_name='école')
    generic_level = models.ForeignKey(GenericLevel, on_delete=models.PROTECT, 
                    verbose_name=_('niveau'))
    number = models.CharField(_('code classe'), max_length=20)
    translation = models.CharField(max_length=255, null=True, blank=True)
    education = models.CharField(_('langue'), max_length=2, 
        choices=custom_utils.EDUCATION_CHOICES, 
        default=custom_utils.EDUCATION_FRENCH)
    max = models.PositiveSmallIntegerField(default=50, 
          verbose_name=_("capacité maximale (nbre délèves)"))
    objects = LevelManager()

    class Meta:
        verbose_name = _('classe')
        ordering = ['school', 'generic_level__order', 'number']
        unique_together = [
            ['year', 'school', 'generic_level', 'number', 'education']
        ]

    def get_name(self):
        return self.number

    def __str__(self):
        return self.get_name()
    
    def get_students_count(self):
        if self.education == custom_utils.EDUCATION_FRENCH:
            return self.enrollment_set.count()
        return self.enrollment_ar.count()

def new_identifier(student):
    if not student.origin:
        student.origin = school_utils.get_current_year()
    first_chars = str(student.origin.name)[2:4]
    return f'E-{first_chars}{str(student.id).zfill(6)}'


# Enrollment and Payment related models
class Student(custom_utils.TimeStampedModel):
    NATIONALITY_IVORY = '70'
    NATIONALITY_BURKINA = '75'
    NATIONALITY_GHANA = '76'
    NATIONALITY_GUINEE = '77'
    NATIONALITY_LIBERIA = '78'
    NATIONALITY_MALI = '79'
    NATIONALITY_BENIN = '80'
    NATIONALITY_TOGO = '81'
    NATIONALITY_NIGER = '82'
    NATIONALITY_NIGERIA = '83'
    NATIONALITY_MAURITANIA = '84'
    NATIONALITY_SENEGAL = '85'
    NATIONALITY_SIERRA_L = '86'
    NATIONALITY_AUTRES_AFR = '89'
    NATIONALITY_FRANCE = '90'
    NATIONALITY_AUTRES_EURO = '91'
    NATIONALITY_ASIA = '93'
    NATIONALITY_AMERICA = '94'
    NATIONALITY_OTHER = '95'
    NATIONALITY_UNKNOWN = '99'
 
    NATIONALITY_CHOICES = (
        (NATIONALITY_BENIN, 'Béninoise'),
        (NATIONALITY_BURKINA, 'Burkinabè'),
        (NATIONALITY_GHANA, 'Ghannéene'),
        (NATIONALITY_GUINEE, 'Guinéene'),
        (NATIONALITY_IVORY, 'Ivoirienne'),
        (NATIONALITY_LIBERIA, 'Libérienne'),
        (NATIONALITY_MALI, 'Malienne'),
        (NATIONALITY_MAURITANIA, 'Mauritanienne'),
        (NATIONALITY_NIGERIA, 'Nigérianne'),
        (NATIONALITY_NIGER, 'Nigérienne'),
        (NATIONALITY_SENEGAL, 'Sénégalaise'),
        (NATIONALITY_SIERRA_L, 'Sierra-Léonaise'),
        (NATIONALITY_AMERICA, 'Américaine'),
        (NATIONALITY_AMERICA, 'Française'),
        (NATIONALITY_ASIA, 'Asiatique'),
        (NATIONALITY_AUTRES_AFR, 'Autres pays africains'),
        (NATIONALITY_AUTRES_AFR, 'Autres pays européen'),
        (NATIONALITY_UNKNOWN, 'Sans Nationalité'),
    )
    identifier = models.CharField(
        _('identifiant'), max_length=10, null=True,
        unique=True, db_index=True)
    student_id = models.CharField(_('matricule'), max_length=9, 
        null=True, blank=True, db_index=True)
    last_name = models.CharField(_('nom en français'), max_length=255)
    first_name = models.CharField(_('prénoms en français'), max_length=255)
    full_name_ar = models.CharField(_('nom et prénoms (arabe)'), 
                   max_length=255, null=True, blank=True)
    gender = models.CharField(_('sexe'), max_length=1, 
             choices=custom_utils.GENDER_CHOICES)
    birth_day = models.PositiveSmallIntegerField(_('né le'),
        validators=[
            validators.MinValueValidator(1),
            validators.MaxValueValidator(31)
    ], null=True, blank=True)
    birth_month = models.PositiveSmallIntegerField(
                  _('mois'), choices=custom_utils.MONTH_CHOICES,
                  null=True, blank=True)
    birth_year = models.PositiveSmallIntegerField(_('année'),
        validators=[validators.MinValueValidator(1980)],
        null=True, blank=True)
    birth_place = models.CharField(
        _('lieu'), max_length=255, null=True, blank=True)
    nationality = models.CharField(_('nationalité'), 
                                   max_length=3, choices=NATIONALITY_CHOICES,
                                   default=NATIONALITY_IVORY)
    birth_place_ar = models.CharField(
                     _('lieu de naissance (arabe)'), 
                     max_length=255, null=True, blank=True)
    phone = models.CharField(
            _("contact de l'élève"), max_length=255, null=True, blank=True)
    
    # Parents
    father = models.CharField(_('père'), max_length=255, 
             null=True, blank=True)
    father_phone = models.CharField(_('contact du père'), max_length=255,
                   null=True, blank=True)
    mother = models.CharField(_('mère'), max_length=255, 
             null=True, blank=True)
    mother_phone = models.CharField(_('contact de la mère'), max_length=255,
                   null=True, blank=True)
    origin = models.ForeignKey(Year, on_delete=models.PROTECT, 
             verbose_name=_('origine'), null=True)
    school = models.ForeignKey(School, on_delete=models.PROTECT)

    # Certificate
    certificate_num = models.CharField(_("numéro de l'extrait"), 
                      max_length=10, null=True, blank=True)
    certificate_date = models.DateField(_('établi le'), null=True, blank=True)
    certificate_place = models.CharField(max_length=255, null=True, blank=True)

    # Files
    photo = CloudinaryField("photo d'identité", folder='school/students_photos/', 
            null=True, blank=True, transformation={
                **custom_utils.CLOUDINARY_TRANFORMATIONS
            })
    certificate_img = CloudinaryField("extrait de naissance", 
                      folder='school/students_certificates/',
                      null=True, blank=True)

    class Meta:
        verbose_name = _('élève')
        unique_together = [
            ['school', 'student_id']
        ]

    def __str__(self):
        return self.get_full_name()

    def get_phone(self):
        return self.father_phone or self.phone

    def get_full_name(self, education=custom_utils.EDUCATION_FRENCH):
        if education == custom_utils.EDUCATION_ARABIC:
            return f'{self.full_name_ar}'
        return f'{self.last_name} {self.first_name}'
    
    def birth_date(self):
        if self.birth_year and self.birth_day and self.birth_month:
            try:
                return date(self.birth_year, self.birth_month, self.birth_day)
            except:
                pass
        return ''
    
    def birth_date_str(self):
        birth_date = self.birth_date()
        if birth_date:
            return self.birth_date().strftime('%d/%m/%Y')
        return ''

    def government_photo(self):
        student_id = self.student_id
        ssl._create_default_https_context = ssl._create_unverified_context
        return f'https://agfne.sigfne.net/vas/picture-noprod/{student_id}'

    def blank_photo(self):
        return '/static/img/avatar.jpg'
    
    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        if not self.identifier:
            self.identifier = new_identifier(self)
            self.save(update_fields=['identifier'])


class EnrollmentManager(models.Manager):
    def for_user(self, user, school=None, year=None, pk=None):
        if not year: 
            year = school_utils.get_current_year()
        queryset = super().get_queryset().filter(year=year)

        # Filter queryset depending on user role
        role = user.role
        if role != custom_utils.ROLE_FOUNDER \
           and role != custom_utils.ROLE_ACCOUNTANT:
            queryset = queryset.filter(active=True)
            
        if school:
            queryset = queryset.filter(school=school)
        else:
            queryset = queryset.filter(school__id=user.school_id)
        
        queryset = custom_utils.apply_subscription_level_filter(
            user=user, year_name=year.name, plan_level=Subscription.PLAN_LEVEL,
            queryset=queryset)
        
        if pk:
            queryset = queryset.filter(pk=pk)
        return queryset.order_by('-created_at').annotate(
            is_second_cycle_fr=(
                Q(generic_level_fr__cycle=custom_utils.CYCLE_SECONDARY)
            )
        )

    def for_user_minimum(self, user, school=None, year=None, pk=None):
        return self.for_user(user, school=school, year=year, pk=pk)\
            .select_related(
                'student', 'level_fr', 'level_ar', 'generic_level_fr', 
                'generic_level_ar') \
            .annotate(
                is_second_cycle_fr=(
                    Q(generic_level_fr__cycle=custom_utils.CYCLE_SECONDARY)
                )
            ).only(
                'qualite', 'status', 'has_scolarship',
                'student__last_name', 'student__full_name_ar',
                'active', 'student__id', 'annexe_fees', 'certificate_num',
                'student__first_name', 'student__gender',
                'student__birth_day', 'student__birth_month',
                'student__birth_year', 'student__student_id', 
                'student__birth_place', 'student__birth_place_ar', 
                'student__photo', 'student__nationality',
                'student__father_phone', 'student__phone',
                'student__identifier','generic_level_fr__short_name', 
                'generic_level_ar__short_name', 
                'generic_level_fr__cycle', 'generic_level_ar__cycle',
                'level_fr__year__name', 'level_fr__number',
                'level_ar__year__name', 'level_ar__number',
            )
    
    def payments_total(self, user, year=None):
        if not year:
            year = school_utils.get_current_year()
        queryset = self.for_user(user, year=year)
        return queryset.aggregate(total=Sum('payment__amount'))['total']
        

class Enrollment(custom_utils.TimeStampedModel):
    LV2_ESPAGNOL = 'E'
    LV2_ALLEMAND = 'A'
    LV2_CHOICES = (
        (LV2_ESPAGNOL, 'Espagnol'),
        (LV2_ALLEMAND, 'Allemand'),
    )

    STATUS_AFF = 'Aff'
    STATUS_NAFF = 'Naff'
    STATUS_CHOICES = (
        (STATUS_AFF, 'Affecté'),
        (STATUS_NAFF, 'Non-Affecté'),
    )

    QUALITE_RED = 'Red'
    QUALITE_NON_RED = 'Nred'
    QUALITE_CHOICES = (
        (QUALITE_RED, 'Rédoublant'),
        (QUALITE_NON_RED, 'Non-Rédoublant'),
    )

    year = models.ForeignKey(Year, on_delete=models.PROTECT)
    school = models.ForeignKey(School, on_delete=models.CASCADE)
    student = models.ForeignKey(Student, on_delete=models.PROTECT)
    agent = models.ForeignKey('users.CustomUser', on_delete=models.SET_NULL, 
        null=True, verbose_name=_('inscrit par'))
    generic_level_fr = models.ForeignKey(
        GenericLevel, on_delete=models.PROTECT, null=True, 
        verbose_name=_('niveau'), 
        related_name='generic_level_enrollment_fr')
    generic_level_ar = models.ForeignKey(
        GenericLevel, on_delete=models.PROTECT, null=True,
        blank=True, related_name='generic_level_enrollment_ar', 
        verbose_name=_('arabe'))
    level_fr = models.ForeignKey(Level, on_delete=models.CASCADE, 
        verbose_name=_('classe'), null=True)
    level_ar = models.ForeignKey(Level, on_delete=models.CASCADE, 
        null=True, blank=True, related_name='enrollment_ar', 
        verbose_name=_('classe arabe'))
    enrollment_fees = models.PositiveIntegerField(_('frais inscription'), default=0)
    year_fees = models.PositiveIntegerField(_('scolarité'), default=0)
    annexe_fees = models.PositiveSmallIntegerField(_('annexe'), default=0)
    status = models.CharField(max_length=4, default=STATUS_NAFF,
                 choices=STATUS_CHOICES, null=True, blank=True, 
                 verbose_name=_('statut Aff/Naff'))
    qualite = models.CharField(max_length=4, default=QUALITE_NON_RED,
                 choices=QUALITE_CHOICES, null=True, blank=True, 
                 verbose_name=_('qualité Red/Nred'))
    active = models.BooleanField(_('actif'), 
        help_text=_("indique si l'élève est actif ou inactif. Un élève " + 
            "inactif n'apparaîtra pas dans les résultats et documents"), 
            default=True)
    debt = models.PositiveIntegerField(_('redevance'), default=0, null=True, blank=True)
    previous_level_name_fr = models.CharField(_('niveau précédent'),
        max_length=10, null=True, blank=True)
    previous_level_name_ar = models.CharField(_('niveau précédent arabe'),
        max_length=10, null=True, blank=True)
    previous_mga_fr = models.FloatField(_('MGA précédent'), null=True, blank=True)
    previous_mga_ar = models.FloatField(_('MGA arabe précédent'), null=True, blank=True)
    previous_decision_fr = models.CharField(_('Déc. Préc.'), max_length=10, default='NA', null=True)
    previous_decision_ar = models.CharField(_('Déc. Préc. Ar'), max_length=10, default='NA', null=True)
    lv2 = models.CharField(_('LV2'), choices=LV2_CHOICES, 
        max_length=1, null=True, blank=True)
    has_scolarship = models.BooleanField(_('boursier'), 
        help_text=_("indique si l'élève est boursier ou non."), default=False)
    locked = models.BooleanField(default=False)
    discount = models.PositiveSmallIntegerField(null=True, blank=True)
    online = models.BooleanField(_('Inscription en ligne'), default=False)
    cherifla = models.BooleanField(default=False)
    pcs = models.BooleanField(default=False)
    certificate_num = models.CharField('numéro du diplome arabe', 
                                       max_length=15, null=True, blank=True)
    subschool = models.ForeignKey(Subschool, on_delete=models.SET_NULL, null=True, blank=True)
    objects = EnrollmentManager()

    class Meta:
        verbose_name = _('inscription')
        unique_together = [['student', 'year', 'school']]
        ordering = ['-created_at']
    
    def __str__(self):
        return f'{self.student}'

    def get_qualite(self):
        return 'Oui' if self.qualite == self.QUALITE_RED else 'Non'
    
    def get_status(self):
        return 'Oui' if self.status == self.STATUS_AFF else 'Non'

    def get_aff(self):
        if self.generic_level_fr.cycle == custom_utils.CYCLE_SECONDARY:
            return self.status
        return '-'
    
    def get_has_scholarship(self):
        return 'Boursier' if self.has_scolarship else 'Non-Boursier'
    
    def get_fees_total(self):
        return self.enrollment_fees + self.year_fees + self.annexe_fees

    def government_photo(self):
        return self.student.government_photo()

    def get_payments_total(self):
        return self.payment_set.aggregate(
            total=Sum('inscription') + Sum('amount') + Sum('annexe')
        )['total'] or 0

    # def is_second_cycle_fr(self):
    #     if self.generic_level_fr:
    #         return self.generic_level_fr.cycle == custom_utils.CYCLE_SECONDARY
    #     return False

    def photo_url(self):
        student_id = self.student.student_id
        if self.student.photo:
            return self.student.photo.url
        elif student_id and str(student_id)[-1].isalpha() \
            and self.is_second_cycle_fr:
            return f'https://agfne.sigfne.net/vas/picture-noprod/{student_id}'
    
    def photo_url_or_blank_photo(self):
        url = self.photo_url()
        if not url:
            return self.student.blank_photo()[1:]
        return url 

class PaymentManager(models.Manager):
    def for_user(self, year=None, user=None):
        if not year:
            year = school_utils.get_current_year()
        queryset = super().get_queryset() \
            .prefetch_related('enrollment__student', 'enrollment__school') \
            .filter(enrollment__year=year) 
        if user and user.role == custom_utils.ROLE_ACCOUNTANT:
            queryset = queryset.filter(agent=user)        
        elif user and user.role == custom_utils.ROLE_FOUNDER:
            queryset = queryset.filter(enrollment__school=user.school)
        return queryset


class Payment(models.Model):
    TYPE_ANNEXE = 'A'
    TYPE_INSCRIPTION = 'I'
    TYPE_SCOLARITE = 'S'
    TYPE_CHOICES = (
        (TYPE_INSCRIPTION, "Frais d'inscription"),
        (TYPE_SCOLARITE, "Frais de scolarité"),
        (TYPE_ANNEXE, "Frais annexes"),
    )
    enrollment = models.ForeignKey(Enrollment, on_delete=models.CASCADE)
    agent = models.ForeignKey('users.CustomUser', 
            on_delete=models.SET_NULL, null=True)
    amount = models.IntegerField(_('scolarité'), default=0)
    inscription = models.PositiveIntegerField(_('inscription'), default=0)
    annexe = models.PositiveIntegerField(_('annexe'), default=0)
    payment_type = models.CharField(_('Type de versement'), 
                   max_length=1, choices=TYPE_CHOICES, null=True, blank=True)
    annexe_category = models.ForeignKey('PriceCategory', 
                      on_delete=models.CASCADE, 
                      null=True, blank=True, 
                      verbose_name=_('Rubrique annexe'))
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    objects = PaymentManager()

    def __str__(self):
        return f'{self.enrollment.student} -> {self.enrollment.year}' + \
               f' -> {self.amount}'

    def date_str(self):
        return self.created_at.strftime('%d/%m/%Y')

    def get_total(self):
        return self.amount + self.inscription + self.annexe

    class Meta:
        verbose_name = 'versement'
        ordering = ['-updated_at']


class Holiday(models.Model):
    HOLIDAY_TOUSSAINT = 'TST'
    HOLIDAY_NEW_YEAR = 'NYR'
    HOLIDAY_FEBRUARY = 'FBR'
    HOLIDAY_EASTER = 'EAS'
    HOLIDAY_RAMADAN = 'RMD'
    HOLIDAY_TABASKI = 'TBK'
    HOLIDAY_PEACE_DAY = 'PDY'
    HOLIDAY_END_OF_YEAR = 'EOY'
    HOLIDAY_OTHER = 'OTH'
    HOLIDAY_VALENTINE = 'VLT'
    HOLIDAY_CHOICES = [
        (HOLIDAY_RAMADAN, 'Fête de Ramadan'),
        (HOLIDAY_TABASKI, 'Fête de Tabaski'),
        (HOLIDAY_FEBRUARY, 'Congés de Février'),
        (HOLIDAY_TOUSSAINT, 'Fête de Toussaint'),
        (HOLIDAY_NEW_YEAR, 'Noel et du Nouvel An'),
        (HOLIDAY_PEACE_DAY, 'Fête National de la Paix'),
        (HOLIDAY_EASTER, 'Fête de Pâcques'),
        (HOLIDAY_END_OF_YEAR, 'Grandes vacances'),
        (HOLIDAY_VALENTINE, 'Saint Valentin'),
        (HOLIDAY_OTHER, 'Autres'),
    ]

    year = models.ForeignKey(Year, on_delete=models.CASCADE, 
        verbose_name=_('année scolaire'))
    name = models.CharField(_('congés'), choices=HOLIDAY_CHOICES, 
           max_length=255)
    start_date = models.DateField(_('Début'))
    end_date = models.DateField(_('Fin'))
    description = models.CharField(_('description'), max_length=255, 
        null=True, blank=True)

    def __str__(self):
        return self.get_name_display()

    class Meta:
        verbose_name = 'congé'
        unique_together = [['year', 'name']]


class LevelPricingManager(models.Manager):
    def for_school(self, user, year=None, education=None, status=None):
        # if not year:
        #     year = school_utils.get_current_year()
        pricing = super().get_queryset() \
            .select_related('generic_level') \
            .filter(school=user.school)
        if education:
            pricing = pricing.filter(education=education)
        if status and education == custom_utils.EDUCATION_FRENCH \
           and pricing.generic_level.cycle == GenericLevel.CYCLE_SECONDARY:
            pricing = pricing.filter(student_status=status)
        return pricing


class LevelPricing(models.Model):
    year = models.ForeignKey(Year, on_delete=models.PROTECT)
    school = models.ForeignKey(School, on_delete=models.PROTECT, 
             verbose_name=_('école'))
    generic_level = models.ForeignKey(GenericLevel, on_delete=models.PROTECT,
                    verbose_name=_('niveau'))
    inscription = models.PositiveIntegerField(
                  _("frais d'inscription"), default=0)
    scolarite = models.PositiveIntegerField(
                _("frais de scolarité"), default=0)
    annexe = models.PositiveIntegerField(
                _("frais annexes"), default=0)
    student_status = models.CharField(
                    max_length=4, choices=Enrollment.STATUS_CHOICES, 
                    null=True, blank=True,
                    verbose_name=_('statut Aff/Naff'))
    education = models.CharField(
        max_length=1, choices=custom_utils.EDUCATION_CHOICES,
        default=custom_utils.EDUCATION_FRENCH)
    objects = LevelPricingManager()

    class Meta:
        verbose_name = _("Frais d'écolage")
        ordering = ['generic_level__order']
        unique_together = [
            [
                'generic_level', 'school', 'year', 
                'student_status', 'education'
            ]
        ]

    def __str__(self):
        return f'{self.year} - {self.generic_level}'


class PriceCategoryManager(models.Manager):
    def for_school(self, user, year=None):
        if not year:
            year = school_utils.get_current_year()
        return super().get_queryset().filter(
            school=user.school,
            year=year
        )


class PriceCategory(models.Model):
    label = models.CharField(_('rubrique'), max_length=255)
    school = models.ForeignKey(School, on_delete=models.CASCADE)
    year = models.ForeignKey(Year, on_delete=models.CASCADE)
    objects = PriceCategoryManager()
    
    class Meta:
        unique_together = [['label', 'school', 'year']]
        verbose_name = _('Rubrique')

    def __str__(self):
        return self.label


class LevelExtraPrice(models.Model):
    TYPE_MANDATORY = 'M'
    TYPE_OPTIONAL = 'O'
    TYPE_CHOICES = (
        (TYPE_MANDATORY, _('Obligatoire')),
        (TYPE_OPTIONAL, _('Facultatif')),
    )
    pricing = models.ForeignKey(LevelPricing, on_delete=models.CASCADE)
    category = models.ForeignKey(PriceCategory, on_delete=models.CASCADE)
    price_type = models.CharField(max_length=1, choices=TYPE_CHOICES, 
                 default=TYPE_MANDATORY)
    price = models.PositiveSmallIntegerField(_('montant à payer'))

    class Meta:
        verbose_name = _('Frais annexe')

    def __str__(self):
        return f'Frais annexe -> {self.pricing} -> {self.price}'


class ExpenseManager(models.Manager):
    def for_year(self, user, year=None):
        if not year:
            year = school_utils.get_current_year()
        queryset = super().get_queryset().filter(
            school=user.school,
            year=year
        ).order_by('-created_at')

        if user and user.role == custom_utils.ROLE_ACCOUNTANT:
            queryset = queryset.filter(agent=user)
        return queryset

class Expense(custom_utils.TimeStampedModel):
    TYPE_EQUIPMENT = 'EQU'
    TYPE_CONSTRUCTION = 'CON'
    TYPE_RENOVATION = 'REN'
    TYPE_SALARY = 'SAL'
    TYPE_AID = 'AID'
    TYPE_OTHER = 'OTH'
    TYPE_CHOICES = (
        (TYPE_EQUIPMENT, 'Achat Matériel/Equipement'),
        (TYPE_CONSTRUCTION, 'Construction de Bâtiment'),
        (TYPE_RENOVATION, 'Renovation/Peinture etc.'),
        (TYPE_SALARY, 'Paiement des salaires'),
        (TYPE_AID, 'Aide financière'),
        (TYPE_OTHER, 'Autre dépenses'),
    )
    amount = models.PositiveIntegerField(_('montant dépensé'),
              validators=[validators.MinValueValidator(100)])
    expense_type = models.CharField(max_length=3, choices=TYPE_CHOICES, 
                verbose_name=_('catégorie'))
    commentaire = models.CharField(max_length=255, null=True, blank=True, 
                  verbose_name=_('commentaire'))
    agent = models.ForeignKey('users.CustomUser', on_delete=models.SET_NULL,
                              null=True)
    year = models.ForeignKey(Year, on_delete=models.CASCADE)
    school = models.ForeignKey(School, on_delete=models.CASCADE)
    objects = ExpenseManager()

    class Meta:
        verbose_name = _('dépense')


class TeacherManager(models.Manager):
    def for_school(self, school, education=None):
        queryset = self.get_queryset().filter(
            school=school, active=True)

        if education:
            queryset = queryset.filter(education=education)
        return queryset


class Teacher(custom_utils.TimeStampedModel):
    last_name = models.CharField(_('nom'), max_length=255)
    first_name = models.CharField(_('prénoms'), max_length=255)
    education = models.CharField(
        max_length=1, 
        choices=custom_utils.EDUCATION_CHOICES,
        default=custom_utils.EDUCATION_FRENCH)
    gender = models.CharField(_('sexe'), max_length=1, 
             choices=custom_utils.GENDER_CHOICES,
             default=custom_utils.GENDER_MALE)
    birth_day = models.PositiveSmallIntegerField(_('né le'),
        validators=[
            validators.MinValueValidator(1),
            validators.MaxValueValidator(31)
    ], null=True, blank=True)
    birth_month = models.PositiveSmallIntegerField(
                  _('mois'), choices=custom_utils.MONTH_CHOICES,
                  null=True, blank=True)
    birth_year = models.PositiveSmallIntegerField(_('année'),
        validators=[validators.MinValueValidator(1920)],
        null=True, blank=True)
    birth_place = models.CharField(_('lieu de naissance'), max_length=255,
                                   null=True, blank=True)
    phone = models.CharField(
        _('contacts'), max_length=21, 
        null=True, blank=True)
    id_number = models.CharField(
        _('N° CNI/Att'), max_length=50,
        null=True, blank=True)
    school = models.ForeignKey(School, on_delete=models.PROTECT)
    active = models.BooleanField(default=True)
    origin = models.ForeignKey(Year, on_delete=models.CASCADE)
    
    user = models.OneToOneField(
        settings.AUTH_USER_MODEL, 
        on_delete=models.SET_NULL, null=True, blank=True)
    objects = TeacherManager()
    
    class Meta:
        verbose_name = _('enseignant')
        permissions = [
            ('can_manage_level_grades', 'Can manage level grades',),
        ]

    def __str__(self):
        return f'{self.last_name} {self.first_name}'

    def get_levels(self):
        levels_list = []
        queryset = None

        if self.teacherlevel_set.exists():
            queryset = self.teacherlevel_set.all()

            for level in queryset.select_related('level'):
                levels_list.append(f'{level.level}: ' + level.get_subjects())
        return levels_list
    
    def get_active_year_levels(self):
        return self.teacherlevel2_set.filter(level__year__active=True) \
            .select_related('level') \
            .order_by('level__generic_level__order', 'level__number')

class TeacherLevel2(models.Model):
    teacher = models.ForeignKey(Teacher, on_delete=models.PROTECT)
    level = models.ForeignKey(Level, on_delete=models.PROTECT)
    is_main_teacher = models.BooleanField(
        _('Est professeur principal (PP)'), default=False)
    subjects = models.ManyToManyField(
        'exams.LevelSubject', blank=True)

    def __str__(self):
        return f"{self.teacher} {self.level}"
    
    def get_subjects(self):
        subjects = [str(subject) for subject in self.subjects.all()]
        return f"<{', '.join(subjects)}>"

    class Meta:
        unique_together = [
            ['teacher', 'level'],
        ]
        verbose_name = 'Attribution de classe'
        verbose_name_plural = 'Attributions de classe'


class TeacherLevel(models.Model):
    teacher = models.ForeignKey(Teacher, on_delete=models.PROTECT)
    level = models.ForeignKey(Level, on_delete=models.PROTECT)
    is_main_teacher = models.BooleanField(
        _('Est professeur principal (PP)'), default=False)
    subjects = models.ManyToManyField(
        'exams.LevelSubject', blank=True)

    def __str__(self):
        return f"{self.teacher} {self.level}"
    
    def get_subjects(self):
        subjects = [str(subject) for subject in self.subjects.all()]
        return f"<{', '.join(subjects)}>"

    class Meta:
        unique_together = [
            ['teacher', 'level'],
        ]
        verbose_name = 'Attribution de classe'
        verbose_name_plural = 'Attributions de classe'


class PDFFile(custom_utils.TimeStampedModel):
    CATEGORY_RESULT = 'RS'
    CATEGORY_REGISTER = 'RG'
    CATEGORY_DAILY_REGISTER = 'DR'
    CATEGORY_RESULT2 = 'R2'
    CATEGORY_REPORT = 'RP'
    CATEGORY_REPORT_WITH_PREVIOUS_TERMS_DATA = 'RW'
    CATEGORY_MARKING_SHEET = 'MS'
    CATEGORY_FICHE_TABLE = 'FT'
    CATEGORY_CLASS_LIST = 'CL'
    CATEGORY_CHOICES = (
        (CATEGORY_RESULT, 'Matrice des moyennes'),
        (CATEGORY_RESULT2, 'Matrice des moyennes simplifiée'),
        (CATEGORY_REPORT, 'Relevé de notes'),
        (CATEGORY_REPORT_WITH_PREVIOUS_TERMS_DATA, 'Relevé de notes avec données précédentes'),
        (CATEGORY_MARKING_SHEET, 'Fiche de notation'),
        (CATEGORY_FICHE_TABLE, 'Fiche de table'),
        (CATEGORY_CLASS_LIST, 'Liste de classe'),
        (CATEGORY_REGISTER, "Liste d'appel"),
        (CATEGORY_DAILY_REGISTER, "Liste d'appel journalier"),
    )
    path = models.CharField(max_length=255)
    is_clean = models.BooleanField(default=True)
    category = models.CharField(max_length=2, choices=CATEGORY_CHOICES)
    level = models.ForeignKey(Level, on_delete=models.CASCADE)
    term = models.ForeignKey(
        'exams.Term', on_delete=models.CASCADE, 
        null=True, blank=True)

    def __str__(self):
        return f'{self.get_category_display()} {self.level}'

    class Meta:
        verbose_name = 'Fichier PDF'
        verbose_name_plural = 'Fichiers PDF'


class MessageBalance(custom_utils.TimeStampedModel):
    school = models.OneToOneField(
        School, on_delete=models.CASCADE, 
        verbose_name=_('école'))
    balance = models.PositiveIntegerField(_('solde de SMS'))
    used = models.PositiveIntegerField(_('SMS utilisés'), default=0)

    class Meta:
        verbose_name = _('Solde de SMS')
        verbose_name_plural = _('Soldes de SMS')

    def __str__(self):
        return f'Solde SMS {self.school} - {self.balance}'


class MessageUpgrade(custom_utils.TimeStampedModel):
    school = models.ForeignKey(School, on_delete=models.CASCADE, verbose_name=_('école'))
    amount_added = models.PositiveIntegerField(_('SMS ajoutés'))
    notes = models.TextField(_('notes'), blank=True, null=True)

    class Meta:
        verbose_name = _('Ajout de SMS')
        verbose_name_plural = _('Ajout de SMS')

    def __str__(self):
        return f'Ajout de SMS: {self.school} - {self.amount_added} le {self.created_at}'
    
    def save(self, *args, **kwargs):
        if not self.pk:
            balance = MessageBalance.objects.get_or_create(
                defaults={'balance': 0, 'used': 0}, school=self.school
            )[0]
            balance.balance += self.amount_added
            balance.save(update_fields=['balance'])
        return super().save(*args, **kwargs)


class Message(custom_utils.TimeStampedModel):
    PAIMENT_NOTIFICATION = 'PM'
    SCHOOL_REGISTRATION_NOTIFICATION = 'SR'
    SMS_TYPES_CHOICES = (
        (PAIMENT_NOTIFICATION, 'Notification paiement'),
        (SCHOOL_REGISTRATION_NOTIFICATION, 'Notification enregistrement école'),
    )
    school = models.ForeignKey(School, on_delete=models.CASCADE, verbose_name=_('école'))
    year = models.ForeignKey(Year, on_delete=models.CASCADE, verbose_name=_('année'))
    student = models.ForeignKey(
        Enrollment, on_delete=models.CASCADE, 
        verbose_name=_('élève'), null=True, blank=True)
    content = models.TextField(_('contenu du SMS'))
    sms_count = models.PositiveSmallIntegerField(default=1)
    sms_type = models.CharField(
        max_length=2, choices=SMS_TYPES_CHOICES, 
        null=True, blank=True, default=SCHOOL_REGISTRATION_NOTIFICATION)
    to = models.CharField(max_length=15, validators=[validators.MinLengthValidator(10)])

    class Meta:
        verbose_name = _('SMS')
        verbose_name_plural = _('SMS')

    def __str__(self):
        return f'SMS pour {self.student} de {self.school} : {self.created_at}'
    
    def save(self, *args, **kwargs):
        if not self.pk:
            balance = MessageBalance.objects.get_or_create(
                defaults={'balance': 0, 'used': 0}, school=self.school
            )[0]
            if balance.balance >= self.sms_count:
                balance.used += self.sms_count
                balance.balance -= self.sms_count
            else:
                balance.balance = 0
            balance.save()
        return super().save(*args, **kwargs)


class StaffRole(models.Model):
    name = models.CharField(_('emploi'), max_length=255)
    code = models.CharField(_('code_emploi'), max_length=10, unique=True, 
                            help_text=_('code unique pour faciliter les importations'))
    choice_code = models.CharField(max_length=10, unique=True, null=True, blank=True)
    school = models.ForeignKey(School, on_delete=models.CASCADE, null=True, blank=True)

    class Meta:
        verbose_name = _('emploi du personnel')
        verbose_name_plural = _('emplois du personnel')

    def __str__(self):
        return self.name


class StaffManager(models.Manager):
    def with_payments(self, year):
        qs = self.get_queryset()
        return qs.annotate(
            january=Count('staffsalaryformonth', 
                filter=Q(staffsalaryformonth__status=True) & Q(staffsalaryformonth__month=custom_utils.MONTH_JANUARY)),
            february=Count('staffsalaryformonth',
                filter=Q(staffsalaryformonth__status=True) & Q(staffsalaryformonth__month=custom_utils.MONTH_FEBRUARY)),
            march=Count('staffsalaryformonth',
                filter=Q(staffsalaryformonth__status=True) & Q(staffsalaryformonth__month=custom_utils.MONTH_MARCH)),
            april=Count('staffsalaryformonth',
                filter=Q(staffsalaryformonth__status=True) & Q(staffsalaryformonth__month=custom_utils.MONTH_APRIL)),
            may=Count('staffsalaryformonth',
                filter=Q(staffsalaryformonth__status=True) & Q(staffsalaryformonth__month=custom_utils.MONTH_MAY)),
            june=Count('staffsalaryformonth',
                filter=Q(staffsalaryformonth__status=True) & Q(staffsalaryformonth__month=custom_utils.MONTH_JUNE)),
            july=Count('staffsalaryformonth',
                filter=Q(staffsalaryformonth__status=True) & Q(staffsalaryformonth__month=custom_utils.MONTH_JULY)),
            august=Count('staffsalaryformonth',
                filter=Q(staffsalaryformonth__status=True) & Q(staffsalaryformonth__month=custom_utils.MONTH_AUGUST)),
            september=Count('staffsalaryformonth',
                filter=Q(staffsalaryformonth__status=True) & Q(staffsalaryformonth__month=custom_utils.MONTH_SEPTEMBER)),
            october=Count('staffsalaryformonth',
                filter=Q(staffsalaryformonth__status=True) & Q(staffsalaryformonth__month=custom_utils.MONTH_OCTOBER)),
            november=Count('staffsalaryformonth',
                filter=Q(staffsalaryformonth__status=True) & Q(staffsalaryformonth__month=custom_utils.MONTH_NOVEMBER)),
            december=Count('staffsalaryformonth',
                filter=Q(staffsalaryformonth__status=True) & Q(staffsalaryformonth__month=custom_utils.MONTH_DECEMBER)),

        )


class Staff(models.Model):
    last_name = models.CharField(_('nom'), max_length=255)
    first_name = models.CharField(_('prénom'), max_length=255)
    phone = models.CharField(_('contact'), max_length=14)
    email = models.EmailField(_('email'), null=True, blank=True)
    education = models.CharField(
        max_length=1, 
        choices=custom_utils.EDUCATION_CHOICES,
        default=custom_utils.EDUCATION_FRENCH)
    gender = models.CharField(_('sexe'), max_length=1, 
             choices=custom_utils.GENDER_CHOICES,
             default=custom_utils.GENDER_MALE)
    birth_day = models.PositiveSmallIntegerField(_('né le'),
        validators=[
            validators.MinValueValidator(1),
            validators.MaxValueValidator(31)
    ], null=True, blank=True)
    birth_month = models.PositiveSmallIntegerField(
                  _('mois'), choices=custom_utils.MONTH_CHOICES,
                  null=True, blank=True)
    birth_year = models.PositiveSmallIntegerField(_('année'),
        validators=[validators.MinValueValidator(1920)],
        null=True, blank=True)
    birth_place = models.CharField(_('lieu de naissance'), max_length=255,
                                   null=True, blank=True)
    phone = models.CharField(_('contact principal'), max_length=10, null=True, blank=True, 
                             help_text=_('10 caractères sans espace'))
    phone2 = models.CharField(_('autre contacts'), max_length=30, null=True, blank=True)
    id_number = models.CharField(_('N° CNI/Att'), max_length=50,null=True, blank=True)
    photo = CloudinaryField("photo d'identité", folder='school/staff_photos/', 
            null=True, blank=True, transformation={
                **custom_utils.CLOUDINARY_TRANFORMATIONS
            })
    parent = models.CharField(_("personne à contacter en cas d'urgence"), max_length=255, null=True, blank=True)
    parent_phone = models.CharField(_("contact d'urgence"), max_length=255,
                   null=True, blank=True)
    salary = models.PositiveIntegerField(_('salaire'), default=75000)
    cnps = models.CharField(_('N° CNPS'), max_length=50, null=True, blank=True)
    date_enlisted = models.DateField(_('embauché le'), null=True, blank=True)
    role = models.ForeignKey(StaffRole, on_delete=models.PROTECT, verbose_name=_('emploi'))
    user = models.ForeignKey('users.CustomUser', on_delete=models.CASCADE, null=True, blank=True)
    teacher = models.ForeignKey(Teacher, on_delete=models.CASCADE, null=True, blank=True)
    school = models.ForeignKey(School, on_delete=models.CASCADE, verbose_name=_('école'))
    work_hours = models.PositiveSmallIntegerField(_("nbre d'heures de travail"), null=True, blank=True)
    objects = StaffManager()

    class Meta:
        verbose_name = _('Personnel')
        verbose_name_plural = _('Personnel')
        ordering = ['last_name', 'first_name']

    def __str__(self):
        return f'{self.first_name} {self.last_name}'

    def birth_date_str(self):
        try:
            date = datetime(self.birth_year, self.birth_month, self.birth_day)
            return date.strftime('%d/%m/%Y')
        except:
            return ''


class SalaryPaymentOptions(models.Model):
    OPERATION_ADDING = 1
    OPERATION_SUBSTRACTING = 2
    OPERATION_CHOICES = (
        (OPERATION_ADDING, 'Ajout'),
        (OPERATION_SUBSTRACTING, 'Retrait'),
    )

    OPTION_FOR_TEACHERS = 'E'
    OPTION_FOR_OTHERS = 'O'
    OPTION_FOR_ALL = 'A'
    OPTION_CHOICES = (
        (OPTION_FOR_TEACHERS, 'Enseignants uniquement'),
        (OPTION_FOR_OTHERS, 'Personnel Non-enseignant'),
        (OPTION_FOR_ALL, 'Tout le Personnel'),
    )
    name = models.CharField(_('rubrique'), max_length=255, help_text=_('Exemple: CNPS, CMU, Primes etc.'))
    amount = models.PositiveIntegerField(
        _('montant'), null=True, blank=True,
        help_text=_("Montant de la rubrique. Laisser vide si taux défini.")
    )
    rate = models.PositiveIntegerField(
        _('taux'), 
        validators=[validators.MinValueValidator(0), validators.MaxValueValidator(100)], 
        null=True, blank=True, 
        help_text=_("Taux de la rubrique. Laisser vide si montant défini.")
    )
    operation = models.PositiveSmallIntegerField(_('opération'), 
                    choices=OPERATION_CHOICES, 
                    default=OPERATION_ADDING, 
                    help_text=_("'Ajout' va ajouter le montant sur le salaire, 'Retrait' va le soustraire")) 
    option = models.CharField(_('Appliquable à'), max_length=1, choices=OPTION_CHOICES,
                default=OPTION_FOR_ALL)
    school = models.ForeignKey(School, on_delete=models.CASCADE, null=True, blank=True)
    active = models.BooleanField(_('actif'), default=True)

    def __str__(self):
        return self.name 


class StaffSalaryForMonth(models.Model):
    staff = models.ForeignKey(Staff, on_delete=models.CASCADE, verbose_name='employé')
    salary = models.PositiveIntegerField(_('salaire de base'))
    month = models.PositiveSmallIntegerField(_('mois'), choices=custom_utils.MONTH_CHOICES)
    year = models.ForeignKey(Year, on_delete=models.CASCADE)
    payment_date = models.DateField(_('date de paiement'))
    created_at = models.DateTimeField(_('date de création'), auto_now_add=True)
    agent = models.ForeignKey('users.CustomUser', on_delete=models.SET_NULL, null=True)
    status = models.BooleanField(_('payé'), default=False)
    gains = models.PositiveIntegerField(_('total des gains'), default=0)
    deductions = models.PositiveIntegerField(_('total des retenues'), default=0)
    net_pay = models.PositiveIntegerField(_('salaire NET'), default=0)

    class Meta:
        verbose_name = _('Décision de paie')
        verbose_name_plural = _('Décisions de paie')
        unique_together = [
            ['staff', 'month', 'year']
        ]

    def __str__(self):
        return f'Paiement de salaire: {self.staff} le {self.created_at}'

    def get_period(self):
        month, month_id = self.get_month_display().split(' ')
        month_id = int(month_id.replace('(' , '').replace(')', ''))
        year = self.year.name
        if month_id >= 9:
            year -= 1
        return f"{month} {year}"
    

class StaffSalaryForMonthOption(models.Model):
    salary = models.ForeignKey(StaffSalaryForMonth, on_delete=models.CASCADE)
    option = models.ForeignKey(SalaryPaymentOptions, on_delete=models.CASCADE)
    amount = models.PositiveIntegerField(_('montant'), null=True, blank=True)
    rate = models.PositiveIntegerField(_('taux'), null=True, blank=True)

    def __str__(self):
        return f'{self.option} pour {self.salary.staff}'


class StaffCard(custom_utils.TimeStampedModel):
    STATUS_PENDING = 'P'
    STATUS_MANUFACTURED = 'M'
    STATUS_SHIPPED = 'S'
    STATUS_CHOICES = (
        (STATUS_PENDING, 'EN COURS'),
        (STATUS_MANUFACTURED, 'IMPRIMEE'),
        (STATUS_SHIPPED, 'LIVREE'),
    )
    year = models.ForeignKey(Year, on_delete=models.CASCADE)
    matricule = models.CharField(_('matricule'), max_length=20, unique=True)
    last_name = models.CharField(_('nom'), max_length=255)
    first_name = models.CharField(_('prénoms'), max_length=255)
    gender = models.CharField(_('sexe'), max_length=255, choices=custom_utils.GENDER_CHOICES)
    birth_date = models.DateField(_('date de naissance'))
    birth_place = models.CharField(_('lieu de naissance'), max_length=255)
    phone = models.CharField(_("contact"), max_length=20)
    status = models.CharField(_('statut'), max_length=1, choices=STATUS_CHOICES, 
                              default=STATUS_PENDING)
    job = models.CharField(_('emploi'), max_length=255)
    school = models.ForeignKey(School, on_delete=models.CASCADE, null=True)
    authorization_number = models.CharField(_('N° autorisation'), max_length=20, null=True, blank=True)
    photo = CloudinaryField(
        'image', folder='cherifla/staff_photos/id_card/', 
        null=True,
        transformation={
            **custom_utils.CLOUDINARY_TRANFORMATIONS
        })

    class Meta:
        verbose_name = _('carte professionnel')
        verbose_name_plural = _('cartes professionnels')


    def __str__(self):
        return f'{self.last_name} {self.first_name}'

    def photo_name(self):
        if self.photo:
            return str(self.photo.url).split('/')[-1]

    def birth_date_str(self):
        return self.birth_date.strftime('%d/%m/%Y')
    
    def created_at_str(self):
        return self.date_created.strftime('%d/%m/%Y')
    

class SalaryPaymentDecision(custom_utils.TimeStampedModel):
    PAYMENT_BY_CASH = 'C'
    PAYMENT_BY_BANK = 'B'
    PAYMENT_BY_CHECK = 'K'
    PAYMENT_BY_MOBILE = 'M'
    PAYMENT_CHOICES = (
        (PAYMENT_BY_CASH, 'Espèces'),
        (PAYMENT_BY_MOBILE, 'Mobile Money'),
        (PAYMENT_BY_BANK, 'Virement bancaire'),
        (PAYMENT_BY_CHECK, 'Chèque'),
    )
    year = models.ForeignKey(Year, on_delete=models.CASCADE)
    month = models.PositiveSmallIntegerField(_('mois'), choices=custom_utils.MONTH_CHOICES)
    payment_date = models.DateField(_('date de paiement'))
    agent = models.ForeignKey('users.CustomUser', on_delete=models.SET_NULL, null=True)
    status = models.BooleanField(_('payé'), default=True)
    decision_number = models.CharField(_('N° décision'), max_length=20, null=True, blank=True)
    school = models.ForeignKey(School, on_delete=models.CASCADE, null=True, blank=True)
    payment_method = models.CharField(
        _('méthode de paiement'), choices=PAYMENT_CHOICES, default=PAYMENT_BY_CASH,  
        max_length=255, null=True, blank=True
    )

    class Meta:
        verbose_name = _('Paramètre des décisions de paie')
        verbose_name_plural = _('Paramètres des décisions de paie')
        unique_together = [
            ['month', 'year', 'decision_number']
        ]

    def __str__(self):
        return f'Décision N° {self.decision_number} pour {self.get_month_display()} {self.year}'