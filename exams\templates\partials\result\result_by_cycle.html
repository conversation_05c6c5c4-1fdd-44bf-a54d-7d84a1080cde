{% load widget_tweaks %}
<div class="row" hx-get="{% url 'exams:results' %}?type={{type}}&lang={{lang}}&option={{ request.GET.option }}"
     hx-trigger="saved from:body"
     hx-target="#table_container"
     hx-include="this">
    <div class="col">
        <!-- Main Card -->
        <div class="card shadow-sm mb-4">

            <!-- Card Body -->
            <div class="card-body">
                <!-- Instructions Panel -->

                <!-- Period Selection Form -->
                <form action="" method="post" class="mb-4">
                    <div class="form-row d-flex justify-content-center">
                        <div class="col-md-6 col-lg-4 form-group">
                            <label for="term" class="font-weight-bold">
                                <span data-feather="calendar" class="feather-16 align-middle mr-1"></span>
                                Sélectionnez une période
                            </label>
                            <div class="input-group">
                                <select name="term" id="term" class="form-control"
                                        hx-get="{% url 'exams:results_by_cycle' %}?type={{type}}&lang={{lang}}&option={{ request.GET.option }}"
                                        hx-target="#table_container"
                                        hx-indicator="#loading-indicator">
                                    <option value="">-- Choisir une période --</option>
                                    {% for term in terms %}
                                    <option value="{{ term.id }}">{{ term }}</option>
                                    {% endfor %}

                                    {% if request.GET.option != 'bulletins' %}
                                    <option value="annual">RESULTATS ANNUELS</option>
                                    {% endif %}
                                </select>
                                <div class="input-group-append">
                                    <span class="input-group-text bg-primary text-white">
                                        <span id="loading-indicator" class="htmx-indicator spinner-border spinner-border-sm" role="status"></span>
                                    </span>
                                </div>
                            </div>
                        </div>

                        {% if request.GET.option == 'bulletins' %}
                        <div class="col-md-6 col-lg-5 form-group d-flex align-items-end">
                            <div class="card bg-light w-100" style="overflow-x: scroll">
                                <div class="card-body py-2">
                                    <h6 class="card-title mb-2">Modèles de bulletins disponibles</h6>
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-outline-primary">
                                            <span data-feather="file-text" class="feather-16 align-middle mr-1"></span>
                                            Bulletins Simples
                                        </button>
                                        <button type="button" class="btn btn-outline-info">
                                            <span data-feather="file-text" class="feather-16 align-middle mr-1"></span>
                                            Bulletins Complets
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </form>

                <!-- Results Container -->
                <div class="results-container">
                    <div id="table_container" class="table-responsive">
                        <!-- Table will be loaded here via HTMX -->
                         {% include 'partials/content_placehoder.html' %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize feather icons
        if (typeof feather !== 'undefined') {
            feather.replace();
        }

        // Add event listener to term select
        const termSelect = document.getElementById('term');
        if (termSelect) {
            termSelect.addEventListener('change', function() {
                if (this.value) {
                    // Show loading state
                    document.getElementById('loading-indicator').style.display = 'inline-block';
                }
            });
        }
    });
</script>