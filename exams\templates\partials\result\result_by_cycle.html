{% load widget_tweaks %}
<div class="row" hx-get="{% url 'exams:results' %}?type={{type}}&lang={{lang}}&option={{ request.GET.option }}" 
     hx-trigger="saved from:body" 
     hx-target="#table_container"
     hx-include="this">
    <div class="col">
        <div class="tile">
            <form action="" method="post" class="mb-1">
                <div class="form-row">
                    <div class="col-md-6 col-lg-3 form-group">
                        <label for="term">* Sélectionnez une période</label>
                        <select name="term" id="term" class="form-control" 
                                hx-get="{% url 'exams:results_by_cycle' %}?type={{type}}&lang={{lang}}&option={{ request.GET.option }}"
                                hx-target="#table_container">
                            <option value="">--------</option>
                            {% for term in terms %}
                            <option value="{{ term.id }}">{{ term }}</option>
                            {% endfor %}
                            <option value="annual">RESULTATS ANNUELS</option>
                        </select>
                    </div>
                </div>
            </form>
            <form method="post" class="table-responsive" id="table_container" 
                  action="{{ request.path }}?option={{ request.GET.option }}">
            </form>
        </div>
    </div>
</div>