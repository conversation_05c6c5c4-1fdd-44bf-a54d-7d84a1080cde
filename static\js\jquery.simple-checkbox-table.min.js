/**
 * @file jquery.simple-checkbox-table
 * @version 1.0.0
 * <AUTHOR>
 * @license MIT
 */

!function(e,d){"use strict";d.fn.simpleCheckboxTable=function(t){var c=d.extend({},e,t);return this.each(function(t,e){var n=d(e);n.find("thead th:nth-child(1) input[type='checkbox']").on("change",function(){d(this).is(":checked")?n.find("tbody td:nth-child(1) input[type='checkbox']:not(:disabled):not(:checked)").prop("checked",!0).trigger("change"):n.find("tbody td:nth-child(1) input[type='checkbox']:not(:disabled):checked").prop("checked",!1).trigger("change")}).end().find("tbody tr").on("click",function(){var t=d(this).find("td:nth-child(1) input[type='checkbox']:not(:disabled)");t.prop("checked",!t.is(":checked")).trigger("change")}).end().find("tbody td:nth-child(1) input[type='checkbox']").on("change",function(){var t=0===d(this).closest("tbody").find("td:nth-child(1) input[type='checkbox']:not(:disabled):not(:checked)").length;n.find("thead th:nth-child(1) input[type='checkbox']").prop("checked",t),c.onCheckedStateChanged.call(n,d(this))}).trigger("change").end().find("tbody td a,input[type='checkbox']").on("click",function(t){t.stopPropagation()}).end()})}}({onCheckedStateChanged:function(){}},jQuery);