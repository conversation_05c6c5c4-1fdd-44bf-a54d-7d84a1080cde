from time import sleep
from celery import shared_task
from celery_progress.backend import ProgressRecorder
from school.models import Level, Year, Enrollment, PDFFile
from exams.grades_utils import update_level_term_result
from exams.models import (
    SchoolTerm, LevelSubject, SchoolTerm, Grade, TermResult,
    Term)
from users.models import CustomUser
from django.db.models import Prefetch, OuterRef, Subquery, Count, Q
from main.utils import (
    EDUCATION_FRENCH, CYCLE_PRIMARY,  CYCLE_BOTH,
    CYCLE_SECONDARY, EDUCATION_ARABIC)
from users.models import CustomUser
from . import reports, views
from tablib import Dataset
from main import utils as main_utils

import os

@shared_task(bind=True)
def go_to_sleep(self, duration):
    recorder = ProgressRecorder(self)
    total = 10
    for i in range(total):
        sleep(duration)
        recorder.set_progress(i + 1, total, f'Progression: {i} sur {total}')
    return 'Done'


@shared_task(bind=True)
def update_levels_results_task(self, user_id, year_id, term_id, education):
    recorder = ProgressRecorder(self)

    user = CustomUser.objects.get(id=user_id)
    year = Year.objects.get(id=year_id)
    term = SchoolTerm.objects.get(id=term_id)
    generic_term_id = term.term_id
    queryset = Level.objects.for_user(
        user=user, year=year, education=term.education,
        with_education=True).filter(generic_level__schoolterm__term__id=generic_term_id).distinct()
    total = queryset.count()

    for i, level in enumerate(queryset):
        term_obj = SchoolTerm.objects.filter(
            level__id=level.generic_level_id,
            year__id=term.year_id, term__id=generic_term_id,
            school__id=user.school_id).first()
        update_level_term_result(user, level, term_obj)
        recorder.set_progress(i + 1, total, f'{round(i * 100 / total)} %')


@shared_task(bind=True)
def create_grades_for_new_subject(self, school_id, year_id,
                                  generic_level_id, subject_id):
    # Creates empty grade objects for students when a new subject is added
    subject = LevelSubject.objects.select_related('subject').get(pk=subject_id)
    education = subject.subject.education
    enrollments = Enrollment.objects.filter(
            school__id=school_id, year__id=year_id)

    if education == EDUCATION_FRENCH:
        enrollments = enrollments.filter(generic_level_fr__id=generic_level_id).only('id')
    else:
        enrollments = enrollments.filter(generic_level_ar__id=generic_level_id).only('id')

    terms = SchoolTerm.objects.filter(year__id=year_id, level__id=generic_level_id,
                                      school__id=school_id)

    objs_to_create = []
    for term in terms:
        if Grade.objects.filter(school_term=term).exists():
            for enrollment in enrollments:
                objs_to_create.append(
                    Grade(school_term=term, grade=None, subject=subject,
                          enrollment=enrollment)
                )
    if objs_to_create:
        Grade.objects.bulk_create(objs_to_create)


@shared_task(bind=True)
def generate_fiche_table(self, user_id, year_id, education):
    recorder = ProgressRecorder(self)

    user = CustomUser.objects.get(id=user_id)
    year = Year.objects.get(id=year_id)
    file_type = PDFFile.CATEGORY_FICHE_TABLE
    levels = Level.objects.for_user(
        user=user, year=year, education=education,
        with_education=True, with_files=True,
        file_type=file_type)
    total = levels.count()

    for i, level in enumerate(levels):
        if not level.is_clean:
            queryset = Enrollment.objects.for_user(user=user, year=year)
            if education == EDUCATION_FRENCH:
                queryset = queryset.filter(level_fr=level)
            else:
                queryset = queryset.filter(level_ar=level)
            doc = reports.FicheTable()
            doc.add_content(queryset=queryset, level=level)

            media_path = os.path.join('/app/media/')
            if not os.path.exists(media_path):
                os.mkdir(media_path)

            pdf_path = os.path.join('/app/media/pdf/')
            if not os.path.exists(pdf_path):
                os.mkdir(pdf_path)

            filename = f"Fiches de table {year}" + str(level) + " " + \
                        str(level.get_education_display()) + \
                        f' [{user.school_id}]'
            filepath = os.path.join(pdf_path, f'{filename}.pdf')
            doc.output(filepath)
            PDFFile.objects.update_or_create(
                defaults={'path':filepath, 'is_clean':True},
                level=level, category=file_type
            )
        recorder.set_progress(i + 1, total, f'{round(i * 100 / total)} %')


@shared_task(bind=True)
def generate_reports(self, user_id, year_id, term_id,
                     education, level_id=None,
                     file_type=PDFFile.CATEGORY_REPORT):
    recorder = ProgressRecorder(self)

    user = CustomUser.objects.get(id=user_id)
    year = Year.objects.get(id=year_id)
    generic_term = Term.objects.get(pk=term_id)
    levels = Level.objects.for_user(
        user=user, year=year, education=education,
        with_education=True, with_files=True,
        file_type=file_type, term=generic_term)

    if level_id:
        levels = levels.filter(pk=level_id)

    if education == EDUCATION_FRENCH:
        levels = levels.annotate(
            students=Count('enrollment', distinct=True)
        )
    else:
        levels = levels.annotate(
            students=Count('enrollment_ar', distinct=True)
        )

    if generic_term.cycle != CYCLE_BOTH:
        levels = levels.filter(generic_level__cycle=generic_term.cycle)

    total = levels.count()
    print('Start - Generating reports task ....', 'term', generic_term, 'education',
          education, 'year', year, 'levels', levels.count(), file_type)

    # Pull data from db
    for i, level in enumerate(levels):
        term = SchoolTerm.objects.get(
            term__id=term_id, year__id=year_id,
            level__id=level.generic_level.id,
            school__id=user.school_id, education=generic_term.education)

        if level.students > 0:
            # queryset = Enrollment.objects.for_user(user=user, year=year)
            term_is_first = views.is_first_term(user=user, level=level, term=term)
            term_is_last = views.is_last_term(user=user, level=level, term=term)

            # Skip reports if is first term for level
            if file_type == PDFFile.CATEGORY_REPORT_WITH_PREVIOUS_TERMS_DATA and term_is_first:
                continue

            queryset = views.annotate_enrollments_with_students_infos(
                user=user, year=year, term=term)

            queryset = views.annotate_enrollment_qs_with_term_grades_data(
                queryset=queryset, user=user, term=term, level=level,
                term_is_first=term_is_first, term_is_last=term_is_last,
                report_type=file_type)

            if education == EDUCATION_FRENCH:
                queryset = queryset.filter(level_fr=level)
                # queryset = queryset.order_by('student__last_name', 'student__first_name')
            else:
                queryset = queryset.filter(level_ar=level)
                queryset = queryset.order_by('student__full_name_ar')
            doc = None

            # Use appropriate report class and pass data to .add_content() method
            cycle = level.generic_level.cycle
            if education == EDUCATION_FRENCH and cycle == CYCLE_PRIMARY:
                doc = reports.PrimaryReportFr()
            elif education == EDUCATION_FRENCH and cycle == CYCLE_SECONDARY:
                doc = reports.SecondCycleReport()
            elif education == EDUCATION_ARABIC and cycle == CYCLE_PRIMARY:
                doc = reports.PrimaryReportAr()
            elif education == EDUCATION_ARABIC and cycle == CYCLE_SECONDARY:
                doc = reports.SecondCycleReportAr()

            if education == EDUCATION_FRENCH and cycle == CYCLE_SECONDARY:
                doc.add_content(queryset=queryset, term=term, year=year, annual_report=term_is_last)
            elif cycle == CYCLE_SECONDARY:
                doc.add_content(
                    user=user, queryset=queryset, term=term,
                    level=level, is_last_term=term_is_last,
                    report_type=file_type)
            else:
                doc.add_content(
                    user=user, queryset=queryset, term=term,
                    level=level, report_type=file_type,
                    is_last_term=term_is_last)

            # Select or create appropriate path and generate report
            media_path = os.path.join('/app/media')
            if not os.path.exists(media_path):
                os.mkdir(media_path)

            pdf_path = os.path.join('/app/media/pdf/')
            if not os.path.exists(pdf_path):
                os.mkdir(pdf_path)

            filename = ''
            if file_type == PDFFile.CATEGORY_REPORT:
                filename = f"Bulletins de notes {year} {level} {term} {level.get_education_display()} [{user.school_id}]"
            else:
                filename = f"Bulletins de notes {year} {level} {term} {level.get_education_display()} Modele 2 [{user.school_id}]"

            filepath = os.path.join(pdf_path, f'{filename}.pdf')
            doc.output(filepath)
            PDFFile.objects.update_or_create(
                defaults={'path':filepath, 'is_clean':True},
                level=level, category=file_type, term=generic_term
            )
        recorder.set_progress(i + 1, total, f'{round(i * 100 / total)} %')


@shared_task(bind=True)
def all_grades_import_task(self, user_id, year_id, file_content, file_name, education):
    recorder = ProgressRecorder(self)

    user = CustomUser.objects.get(id=user_id)
    year = Year.objects.get(id=year_id)

    # Process the file content
    dataset = Dataset().load(file_content, format=main_utils.infer_format(file_name))
    fields = ['matricule', 'niveau', 'rang']
    valid = True

    # Validate headers
    for field in fields:
        if not field in dataset.headers:
            valid = False
            break

    print(valid, 'fields', fields, 'dataset headers', dataset.headers)
    if not valid:
        return {'status': 'error', 'message': "L'en-tête du fichier est incorrecte."}

    # Get necessary data
    ids = dataset['matricule']
    level_name = dataset['niveau'][0]
    term_code = dataset.headers[dataset.headers.index('rang') - 1]

    # Get term
    term_qs = SchoolTerm.objects.filter(
        school=user.school,
        term__code=term_code,
        education=education,
        year=year,
        level__short_name=level_name
    )
    print(f'School {user.school}, term code {term_code}, education {education}, year {year}, level name {level_name} ')
    if not term_qs.exists():
        return {'status': 'error', 'message': "L'en-tête du fichier est incorrecte."}

    # Get enrollments
    enrollments = Enrollment.objects.for_user_minimum(
        user=user, year=year
    ).filter(
        Q(student__student_id__in=ids) | Q(student__identifier__in=ids)
    )

    if education == EDUCATION_FRENCH:
        enrollments = enrollments.filter(generic_level_fr__short_name=level_name)
    else:
        enrollments = enrollments.filter(generic_level_ar__short_name=level_name)

    # Get level subjects
    level_subjects = LevelSubject.objects.for_school_all(
        user=user, education=education,
        level_name=level_name, year=year
    ).only('subject__code', 'subject__id')

    term = term_qs.first()

    # Prepare subjects dictionaries
    subjects_dict = {}
    subjects_max = {}
    for subject in level_subjects:
        subject_id = f'subject{subject.subject_id}'
        subjects_dict[str(subject.subject.code)] = subject
        subjects_max[str(subject.subject.code)] = subject.max
        enrollments = enrollments.annotate(
            **{
                subject_id: Count('grade',
                    filter=Q(grade__school_term=term) & \
                            Q(grade__subject=subject))
            }
        )

    # Process imported data
    imported_data = {}
    for row in dataset:
        grades = {}
        for i, col in enumerate(row):
            header = dataset.headers[i]
            if dataset.headers[i] in subjects_dict:
                grades[header] = col
        imported_data[row[dataset.headers.index('matricule')]] = grades

    # Prepare objects for bulk operations
    objs_to_create = []
    objs_to_update = []
    enrollments_count = enrollments.count()

    # Set total for progress tracking
    total = enrollments.count()

    if enrollments.exists():
        for enrollment_index, enrollment in enumerate(enrollments):
            student = enrollment.student
            student_id = student.student_id or student.identifier

            if student_id in imported_data:
                grades = imported_data[student_id]
                for subject in grades:
                    grade = grades[subject]
                    data_valid = (type(grade) == int or type(grade) == float or grade == '' or not grade)

                    if str(grade).isnumeric():
                        data_valid = int(grade) <= subjects_max[subject] and int(grade) >= 0

                    if not data_valid:
                        continue

                    subject = subjects_dict[subject]
                    result_exists = bool(enrollment.__dict__.get(f'subject{subject.subject_id}'))

                    qs = enrollment.grade_set.filter(
                        school_term=term,
                        subject=subject
                    )
                    if result_exists:
                        obj = qs.first()
                        obj.grade = grade
                        objs_to_update.append(obj)
                    else:
                        obj = Grade(
                            grade=grade, updated_by=user,
                            enrollment=enrollment,
                            school_term=term, subject=subject
                        )
                        objs_to_create.append(obj)

            # Update progress
            recorder.set_progress(enrollment_index + 1, total, f'Importation des notes: {enrollment} {enrollment_index + 1} sur {total}')

        # Bulk create and update
        Grade.objects.bulk_create(objs_to_create)
        Grade.objects.bulk_update(objs_to_update, fields=['grade'])

        # Get levels to update
        levels_set = set()
        education_code = 'fr'
        if education == EDUCATION_ARABIC:
            education_code = 'ar'

        for item in enrollments.values(f'level_{education_code}'):
            levels_set.add(item[f'level_{education_code}'])

        levels = Level.objects.filter(pk__in=list(levels_set))

        # Update results for each level
        total_levels = levels.count()
        for i, level in enumerate(levels):
            recorder.set_progress(i + 1, total_levels, f'Actualisation des moyennes: {i + 1} sur {total_levels}')
            update_level_term_result(user, level, term)

            try:
                generate_reports.delay(
                    user_id=user.id,
                    year_id=year.id,
                    education=level.education,
                    term_id=term.term_id,
                    level_id=level.id
                )
            except:
                pass

            try:
                generate_reports.delay(
                    user_id=user.id,
                    year_id=year.id,
                    education=level.education,
                    term_id=term.term_id,
                    level_id=level.id,
                    file_type=PDFFile.CATEGORY_REPORT_WITH_PREVIOUS_TERMS_DATA)
            except:
                pass

    return {'status': 'success', 'message': 'Importation terminée avec succès'}