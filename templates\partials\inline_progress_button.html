<style>
.progress-button {
    position: relative;
    overflow: hidden;
    min-width: 120px;
    transition: all 0.3s ease;
}

.progress-button .progress-overlay {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: linear-gradient(90deg, rgba(40, 167, 69, 0.2), rgba(32, 201, 151, 0.2));
    transition: width 0.6s ease;
    z-index: 1;
}

.progress-button .button-content {
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
}

.progress-button .progress-text {
    font-size: 0.75rem;
    margin-left: 0.5rem;
}

.progress-button.completed {
    background-color: #28a745 !important;
    border-color: #28a745 !important;
    color: white !important;
}

.progress-button.error {
    background-color: #dc3545 !important;
    border-color: #dc3545 !important;
    color: white !important;
}

.download-link {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}
</style>

<div class="progress-button-container">
    {% if task_id %}
    <button class="btn btn-outline-{% if report_type == 'RP' %}primary{% else %}info{% endif %} progress-button"
            id="progress-btn-{{ level_id }}-{{ report_type }}"
            disabled>
        <div class="progress-overlay" style="width: 0%;"></div>
        <div class="button-content">
            <span class="spinner-border spinner-border-sm mr-2" role="status"></span>
            <span class="button-text">Génération...</span>
            <span class="progress-text">0%</span>
        </div>
    </button>
    {% else %}
    <button class="btn btn-outline-danger progress-button" disabled>
        <div class="button-content">
            <span data-feather="alert-circle" class="feather-16 align-middle mr-1"></span>
            <span class="button-text">Erreur</span>
        </div>
    </button>
    {% endif %}
</div>

<script>
$(document).ready(function() {
    {% if task_id %}
    var progressUrl = "{% url 'celery_progress:task_status' task_id %}";
    var buttonId = '#progress-btn-{{ level_id }}-{{ report_type }}';
    var levelId = '{{ level_id }}';
    var termId = '{{ term }}';
    var reportType = '{{ report_type }}';
    var startTime = Date.now();
    var isCompleted = false;

    // Initialize feather icons
    if (typeof feather !== 'undefined') {
        feather.replace();
    }

    // Custom progress handler for inline button
    function updateInlineProgress(progressBarElement, progressBarMessageElement, progress) {
        console.log('Progress update:', progress); // Debug log

        var percentage = Math.round(progress.percent || 0);
        var button = $(buttonId);
        var progressOverlay = button.find('.progress-overlay');
        var buttonText = button.find('.button-text');
        var progressText = button.find('.progress-text');

        // Update progress overlay
        progressOverlay.css('width', percentage + '%');
        progressText.text(percentage + '%');

        // Update button text based on progress
        if (progress.description) {
            buttonText.text(progress.description);
        } else if (progress.current && progress.total) {
            buttonText.text(`${progress.current}/${progress.total}`);
        } else {
            buttonText.text('En cours...');
        }
    }

    // Success handler for inline button
    function onInlineSuccess(progressBarElement, progressBarMessageElement, result) {
        console.log('Task completed successfully:', result); // Debug log
        isCompleted = true;
        var button = $(buttonId);
        var buttonContent = button.find('.button-content');

        // Hide spinner and progress text
        button.find('.spinner-border').hide();
        button.find('.progress-text').hide();

        // Mark as completed
        button.addClass('completed');
        button.find('.button-text').text('Terminé');

        // Check for generated file and replace with download link
        setTimeout(function() {
            checkForGeneratedFileAndReplace();
        }, 2000);
    }

    // Error handler for inline button
    function onInlineError(progressBarElement, progressBarMessageElement, excMessage) {
        isCompleted = true;
        var button = $(buttonId);

        // Hide spinner and progress text
        button.find('.spinner-border').hide();
        button.find('.progress-text').hide();

        // Mark as error
        button.addClass('error');
        button.find('.button-text').text('Erreur');

        // Re-enable button for retry
        setTimeout(function() {
            button.prop('disabled', false);
            button.removeClass('error');
            button.find('.button-text').text('Réessayer');
            button.find('.button-content').prepend('<span data-feather="refresh-cw" class="feather-16 align-middle mr-1"></span>');
            if (typeof feather !== 'undefined') {
                feather.replace();
            }
        }, 3000);
    }

    // Function to check for generated file and replace button with download link
    function checkForGeneratedFileAndReplace() {
        fetch('{% url "exams:get_generated_file_link" %}?level_id=' + levelId + '&term_id=' + termId + '&report_type=' + reportType)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.download_url) {
                    // Replace button with download link
                    var container = $(buttonId).parent();
                    var buttonClass = reportType === 'RP' ? 'btn-outline-primary' : 'btn-outline-info';
                    var buttonText = reportType === 'RP' ? 'Bulletins' : 'Bulletins Complets';

                    container.html(`
                        <a href="${data.download_url}" class="btn ${buttonClass} download-link">
                            <span data-feather="download" class="feather-16 align-middle mr-1"></span>
                            ${buttonText}
                        </a>
                    `);

                    // Re-initialize feather icons
                    if (typeof feather !== 'undefined') {
                        feather.replace();
                    }
                } else {
                    // File not ready yet, keep the completed button
                    console.log('File not ready yet');
                }
            })
            .catch(error => {
                console.error('Error checking file:', error);
            });
    }

    // Initialize progress tracking
    if (typeof CeleryProgressBar !== 'undefined') {
        CeleryProgressBar.initProgressBar(progressUrl, {
            onProgress: updateInlineProgress,
            onSuccess: onInlineSuccess,
            onError: onInlineError,
            progressBarElement: $(buttonId).find('.progress-overlay')[0],
            progressBarMessageElement: $(buttonId).find('.button-text')[0]
        });
    } else {
        console.error('CeleryProgressBar not found. Make sure celery-progress is loaded.');
        // Fallback: simulate progress for demo
        setTimeout(function() {
            onInlineSuccess(null, null, null);
        }, 5000);
    }
    {% endif %}
});
</script>
