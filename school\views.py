import os, re, json, requests
from datetime import date, datetime
from typing import Any
import cloudinary.uploader
from django.conf import settings
from django.core.files.storage import FileSystemStorage
from django.core.cache import cache
from django.db import models, connection, transaction
from django.db.models import Integer<PERSON>ield, Case, Value, When, CharField
from django.db.models.functions import Concat, Coalesce, Cast
from django.db.models.aggregates import Sum, Count
from django.db.models.query import Q , F
from django.db.models import Subquery, OuterRef, Case, When, BooleanField
from django.db.transaction import atomic
from django.contrib.auth import decorators, mixins
from django.contrib.auth.models import Group
from django.http import FileResponse, HttpResponse, HttpResponseRedirect
from django.http.response import HttpResponseRedirect
from django.shortcuts import render
from django.urls import reverse
from django.utils import timezone
from django.views import generic
from django.utils.functional import cached_property
import cloudinary
from formtools.wizard.views import SessionWizardView
from .resources import (
    StudentResource, 
    LevelResource, 
    StudentResourceForCertificate,
    DFARessource
)
from tablib import Databook, Dataset
from main.sms import send_sms
from sweetify import sweetify
from xlrd import open_workbook
from users.models import CustomUser
import openpyxl

from school import reports, school_utils
from . import navs

from users.models import CustomUser
from . import models, forms, school_utils, mixins
from main import utils as main_utils
from exams.models import LevelSubject, SchoolTerm, TermResult
from .tasks import create_default_subjects_and_terms_task, create_levels_for_school, send_sms_notification 

def get_session_year(request):
    selected_year = request.session.get(f'{request.user.id}')
    years = None
    if cache.get('years'):
        years = cache.get('years')
    else:
        years = models.Year.objects.all()
    return years.filter(name=selected_year).first()


def hide_fields(fields_list, fields):
    for field in fields_list:
        if field in fields:
            classes_applied = fields[field].widget.attrs.get('class', '')
            classes_applied += ' d-none'
            fields[field].widget.attrs['class'] = classes_applied
            fields[field].help_text = 'd-none'

def disable_fields(fields_list, fields):
    for field in fields_list:
        if field in fields:
            fields[field].widget.attrs['readonly'] = True


def hide_and_disable_fields(user, context, fields=None, creation=False):
    french_fields_to_hide = ['generic_level_fr', 'level_fr']
    arabic_fields_to_hide = ['generic_level_ar', 'level_ar']
    french_fields_to_disable = ['first_name', 'last_name', 'birth_place']
    arabic_fields_to_disable = ['full_name_ar', 'birth_place_ar']
    accountant_fields = [
        'enrollment_fees', 'year_fees', 'annexe_fees',
        'enrollment_fee1', 'year_fee1', 'annexe_fee1'
    ]
    education = user.education
    fields = fields or context['form'].fields
    if education and education == main_utils.EDUCATION_FRENCH and not creation:
        hide_fields(fields_list=arabic_fields_to_hide, fields=fields)
        disable_fields(fields_list=arabic_fields_to_disable, fields=fields)
    elif education and education == main_utils.EDUCATION_ARABIC and not creation:
        hide_fields(fields_list=french_fields_to_hide, fields=fields)
        disable_fields(fields_list=french_fields_to_disable, fields=fields)

    if not user.has_perm('school.add_payment'):
        hide_fields(accountant_fields, fields)


def update_payments_data(request, enrollment, enrollment_fee, annexe_fee, year_fee, payment_date, notify_parent=False):
    user = request.user
    print(type(payment_date))
    if user.has_perm('school.add_payment'):
        # Save new payment
        if (enrollment_fee + year_fee + annexe_fee) > 0:
            payment = models.Payment.objects.create(
                enrollment=enrollment,
                agent=user, amount=year_fee, 
                inscription=enrollment_fee, 
                annexe=annexe_fee, 
                created_at=payment_date
            )

            if notify_parent:
                try:
                    send_sms_notification.delay(request.session.session_key, enrollment.id, payment.id)
                except:
                    pass

        # Update previous payments
        payments_dict = {}
        print(request.POST.keys())
        for key in request.POST.keys():
            if key.startswith('inscription_') or key.startswith('amount_') or (key.startswith('annexe_') or (key.startswith('date_')) and not key.startswith('annexe_fee')):
                name, id = key.split('_')
                if not payments_dict.get(id):
                    payments_dict[id] = {name: request.POST.get(key)}
                else:
                    new_dict = payments_dict[id]
                    new_dict[name] = request.POST.get(key)
        
        objs_to_update = []
        for key in payments_dict:
            if key != 'fee1':
                payment = models.Payment.objects \
                    .filter(enrollment__school=user.school, enrollment__year=enrollment.year, pk=key).first()
                payment.inscription = payments_dict[key]['inscription']
                payment.amount = payments_dict[key]['amount']
                payment.annexe = payments_dict[key]['annexe']
                payment.created_at = datetime.strptime(payments_dict[key]['date'], '%d/%m/%Y')
                objs_to_update.append(payment)
        models.Payment.objects.bulk_update(objs_to_update, fields=['inscription', 'amount', 'annexe', 'created_at'])

class BaseView(generic.View):
    """ Uses some techniques to avoid repetition and decrease queries count """
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.year = None

    def get_year(self):
        """ Returns the selected school year"""
        if not self.year:
            self.year = get_session_year(self.request)
        return self.year

class BaseHTMXRequestsView(BaseView, generic.View):
    """ Handles some htmx stuff like using partials if request is htmx """
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        if not bool(self.request.htmx):
            context['template_name'] = self.template_name
        return context

    def get_template_names(self):
        if bool(self.request.htmx):
            return [self.template_name]
        return ['full_template.html']
    

class BaseSortView(generic.ListView):
    pass


class LandingPageView(generic.TemplateView):
    template_name = 'landing.html'

    def get_context_data(self, **kwargs: Any):
        context = super().get_context_data(**kwargs)
        schools = models.School.objects.only('name', 'logo')
        context['schools'] = schools
        return context


class StudentsListViewBase(mixins.SortableListMixin,
                             BaseHTMXRequestsView, 
                             generic.ListView
                            ):

    # Add allowed sort fields
    allowed_sort_fields = [
        'student__last_name',
        'student__student_id', 
        'student__birth_place',
        'student__nationality',
        'level_fr',
        'level_ar',
        'debt',
        'amount',
        'paid', 
        'remaining',
        'age',
    ]

    # Specify which sort fields are annotations
    annotated_sort_fields = [
        'debt',
        'amount', 
        'paid',
        'remaining'
    ]
    
    # Set default sorting
    default_sort_fields = 'student__last_name'
    default_sort_direction = 'asc'

    def get_paginate_by(self, queryset):
        return self.request.GET.get('per_page', 10)

    def get_queryset(self):
        user = self.request.user
        education = self.request.GET.get('education')
        status_enrolled = self.request.GET.get('statut', '')    
        search = self.request.GET.get('search')
        fields_to_exclude = [
            'page', 'statut', 'search', 'gender', 
            'birth_year', 'per_page', 'cycle', 'edit_mode',
            'level_filter', 'action', 'csrfmiddlewaretoken', 
            'sort', 'amount', 'paid', 'remaining', 
            'education' 
        ]
        filter_params = {
            key: value[0] for key, value in dict(self.request.GET).items() \
                if (value[0] and not key in fields_to_exclude) and not key.startswith('check-')
        }

        gender = self.request.GET.get('gender') or ''
        cycle = self.request.GET.get('cycle') or ''
        birth_year = self.request.GET.get('birth_year') or ''
        
        current_year = datetime.today().year
        
        queryset = models.Enrollment.objects.for_user_minimum(
                        user, year=self.get_year() 
                    )
        
        queryset = queryset.annotate(
            full_name=Concat('student__last_name', Value(' '), 
                'student__first_name', output_field=CharField()),
            age=current_year - F('student__birth_year')
        )

        if status_enrolled == 'inscrits':
            queryset  = queryset.filter(active=True)
        elif status_enrolled == 'non-inscrits':
            queryset  = queryset.filter(active=False)

        queryset = queryset.filter(**filter_params)

        # Tells if filter was applied
        self.filter_applied = False
        for key, value in filter_params.items():
            if value:
                self.filter_applied = True
                break
        
        if cycle:
            self.filter_applied = True
            
        if not self.filter_applied and (gender or search):
            self.filter_applied = True

        if cycle:
            field_name = 'generic_level_'
            cycle_value = main_utils.CYCLE_PRIMARY if main_utils.CYCLE_PRIMARY in cycle else main_utils.CYCLE_SECONDARY
            if 'A' in cycle:
                field_name += 'ar'
            else:
                field_name += 'fr'
            
            queryset = queryset.filter(**{f'{field_name}__cycle': cycle_value})
        if gender:
            queryset = queryset.filter(student__gender=gender)
        if birth_year:
            queryset = queryset.filter(student__birth_year=birth_year)
            self.filter_applied = True

        if search:
            queryset = queryset.filter(
                Q(student__student_id__icontains=search) |
                Q(student__identifier__icontains=search) |
                Q(full_name__icontains=search) | 
                Q(student__full_name_ar__icontains=search))
        
        aggregated = queryset.aggregate(
            boys=Count('student__gender', filter=Q(student__gender=main_utils.GENDER_MALE)),
            girls=Count('student__gender', filter=Q(student__gender=main_utils.GENDER_FEMALE)),
        )
        self.result_boys = aggregated.get('boys', 0)
        self.result_girls = aggregated.get('girls', 0)

        # Apply ordering from SortableListMixin
        # ordering = self.get_ordering()
        # if ordering:
        #     queryset = queryset.order_by(*ordering)
        # else:
        #     # Default ordering if no valid sort field
        #     queryset = queryset.order_by('student__last_name', 'student__first_name')
            
        return queryset.annotate(
                selected=Case(
                    When(
                        pk__in=[
                            int(key.replace('check-', '')) 
                            for key in self.request.GET.keys() 
                            if key.startswith('check-')
                        ],
                        then=Value(True)
                    ),
                    default=Value(False),
                    output_field=BooleanField()
                )
            ) \
            .only(
                'qualite', 'status', 'student__last_name', 'student__full_name_ar',
                'active', 'annexe_fees', 'debt', 'enrollment_fees', 'year_fees',
                'previous_level_name_fr', 'previous_level_name_ar',
                'student__first_name', 'student__gender',
                'student__student_id', 'student__id', 
                'student__photo', 
                'student__birth_day', 'student__birth_month', 'student__birth_year',
                'student__identifier','generic_level_fr__short_name', 
                'generic_level_ar__short_name', 
                'generic_level_fr__cycle', 'generic_level_ar__cycle',
                'level_fr__number', 'level_ar__number', 
                'student__birth_place', 'student__birth_place_ar',
                'student__nationality',
                'created_at',
            )
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user = self.request.user
        context['per_page'] = self.get_paginate_by(self.request)
        context['page_title'] = 'Gestion des élèves'
        context['result_boys'] = self.result_boys
        context['result_girls'] = self.result_girls
        context['cycle'] = self.request.GET.get('cycle')
        context['is_arabic_school'] = user.school.education == main_utils.EDUCATION_ARABIC
        context['sort_field'] = getattr(self, 'sort_field', '')
        context['sort_direction'] = getattr(self, 'sort_direction', '')
        subscription = user.get_school_subscription(user.school_id, self.get_year().name)
        context['school_plan'] = subscription.plan if subscription else models.Subscription.PLAN_LEVEL
        if self.request.GET.get('search'):
            context['search'] = self.request.GET.get('search')
        return context
    

class HomePageView(mixins.LoginRequiredMixin, BaseHTMXRequestsView, generic.TemplateView):
    template_name = 'general_stats.html'

    @cached_property
    def current_year(self) -> int:
        return school_utils.get_current_year()

    @cached_property
    def user_queryset(self):
        return models.Enrollment.objects.for_user(
            user=self.request.user,
            year=self.current_year
        ).select_related('student', 'level_fr', 'level_fr__generic_level')

    def get_student_stats(self):
        """Calculate student statistics by gender and cycle"""
        return self.user_queryset.aggregate(
            students=Count('id'),
            boys=Count('id', filter=Q(student__gender=main_utils.GENDER_MALE)),
            girls=Count('id', filter=Q(student__gender=main_utils.GENDER_FEMALE)),
            students_primary=Count('id', 
                filter=Q(generic_level_fr__cycle=models.GenericLevel.CYCLE_PRIMARY)),
            students_secondary=Count('id', 
                filter=Q(generic_level_fr__cycle=models.GenericLevel.CYCLE_SECONDARY)),
            boys_primary=Count('id', 
                filter=Q(student__gender=main_utils.GENDER_MALE, 
                        generic_level_fr__cycle=models.GenericLevel.CYCLE_PRIMARY)),
            boys_secondary=Count('id', 
                filter=Q(student__gender=main_utils.GENDER_MALE, 
                        generic_level_fr__cycle=models.GenericLevel.CYCLE_SECONDARY)),
            girls_primary=Count('id', 
                filter=Q(student__gender=main_utils.GENDER_FEMALE, 
                        generic_level_fr__cycle=models.GenericLevel.CYCLE_PRIMARY)),
            girls_secondary=Count('id', 
                filter=Q(student__gender=main_utils.GENDER_FEMALE, 
                        generic_level_fr__cycle=models.GenericLevel.CYCLE_SECONDARY))
        )

    def get_payment_stats(self):
        """Get payment statistics based on user role"""
        user = self.request.user
        current_date = timezone.now().date()
        
        today_filter = (Q(payment__created_at__date=current_date) & 
                       (Q() if user.role == main_utils.ROLE_FOUNDER else Q(payment__agent=user)))
        
        base_payments = self.user_queryset.aggregate(
            # Regular payments
            inscription_regular=Sum('payment__inscription', 
                filter=Q(payment__payment_type=models.Payment.TYPE_INSCRIPTION)),
            scolarite_regular=Sum('payment__amount', 
                filter=Q(payment__payment_type=models.Payment.TYPE_SCOLARITE)),
            annexe_regular=Sum('payment__annexe', 
                filter=Q(payment__payment_type=models.Payment.TYPE_ANNEXE)),
            
            # Legacy payments
            inscription_legacy=Sum('payment__inscription', 
                filter=Q(payment__payment_type__isnull=True)),
            scolarite_legacy=Sum('payment__amount', 
                filter=Q(payment__payment_type__isnull=True)),
            annexe_legacy=Sum('payment__annexe', 
                filter=Q(payment__payment_type__isnull=True)),
            
            # Today's payments
            inscription_today=Sum('payment__inscription', filter=today_filter),
            scolarite_today=Sum('payment__amount', filter=today_filter),
            annexe_today=Sum('payment__annexe', filter=today_filter)
        )

        return {
            'inscription': (base_payments['inscription_regular'] or 0) + 
                         (base_payments['inscription_legacy'] or 0),
            'scolarite': (base_payments['scolarite_regular'] or 0) + 
                        (base_payments['scolarite_legacy'] or 0),
            'annexe': (base_payments['annexe_regular'] or 0) + 
                     (base_payments['annexe_legacy'] or 0),
            'inscription_today': base_payments['inscription_today'] or 0,
            'scolarite_today': base_payments['scolarite_today'] or 0,
            'annexe_today': base_payments['annexe_today'] or 0,
            'total_paid': sum((base_payments[k] or 0) for k in 
                ['inscription_regular', 'inscription_legacy',
                 'scolarite_regular', 'scolarite_legacy',
                 'annexe_regular', 'annexe_legacy']),
            'total_paid_today': sum((base_payments[k] or 0) for k in 
                ['inscription_today', 'scolarite_today', 'annexe_today'])
        }

    def handle_file_upload(self, file, field_name: str) -> bool:
        """Handle photo/logo upload to Cloudinary"""
        if not file:
            return False
            
        if field_name == 'photo':
            user = self.request.user
            cloudinary_photo = cloudinary.uploader.upload_image(
                file, 
                transformations=main_utils.CLOUDINARY_TRANFORMATIONS
            )
            user.photo = cloudinary_photo
            user.save(update_fields=['photo'])
        elif field_name == 'logo':
            school = self.request.user.school
            cloudinary_logo = cloudinary.uploader.upload_image(
                file,
                transformations=main_utils.CLOUDINARY_TRANFORMATIONS
            )
            school.logo = cloudinary_logo
            school.save(update_fields=['logo'])
        return True

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user = self.request.user
        
        # Get all stats
        stats = {
            **self.get_student_stats(),
            **self.get_payment_stats(),
            'teachers_count': models.Teacher.objects.for_school(
                school=user.school,
                education=user.education
            ).count(),
        }
        context.update({
            'data': stats,
            'has_both_cycles': user.school.cycle == main_utils.CYCLE_BOTH,
            'teachers_count': stats['teachers_count'],
            'ROLE_ACCOUNTANT': main_utils.ROLE_ACCOUNTANT,
            'ROLE_FOUNDER': main_utils.ROLE_FOUNDER,
            'ROLE_TEACHER': main_utils.ROLE_TEACHER,
            'EDUCATION_ARABIC': main_utils.EDUCATION_ARABIC,
            'enrollments': self.user_queryset[:10],
            'page_title': 'Accueil',
            'levels_count': user.school.level_set.filter(year=self.current_year).count(),
        })
        if user.role == main_utils.ROLE_TEACHER:
            related_field_name = 'enrollment'
            teacher = user.teacher
            if teacher.education == 'ar':
                related_field_name += '_ar'
            context['levels'] = teacher.teacherlevel2_set.filter(
                level__year=self.get_year()).annotate(students=Count(f'level__{related_field_name}', distinct=True))
        return context

    def post(self, request, *args, **kwargs):
        """Handle file uploads"""
        context = self.get_context_data(**kwargs)
        
        photo_uploaded = self.handle_file_upload(request.FILES.get('photo'), 'photo')
        logo_uploaded = self.handle_file_upload(request.FILES.get('logo'), 'logo')
        
        return self.render_to_response(context)


class StudentsListView(StudentsListViewBase):
    model = models.Enrollment
    template_name = 'partials/active_students/students_base.html'
    context_object_name = 'enrollments'
    permission_required = 'school.view_payment'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        year = self.get_year()
        context['partial_template'] = 'partials/student/students_list.html'
        context['EDUCATION_ARABIC'] = main_utils.EDUCATION_ARABIC
        context['year'] = str(year)
        context['page'] = self.request.GET.get('page') or ''
        if self.request.GET.get('search'):
            context['search'] = self.request.GET.get('search')
        level_fr = self.request.GET.get('level_fr')
        level_ar = self.request.GET.get('level_ar')

        if level_fr:
            context['level_fr'] = int(level_fr)
        elif level_ar:
            context['level_ar'] = int(level_ar)
        levels  = models.GenericLevel.objects.all()
        context['levels'] = levels

        status = self.request.GET.get('statut')
        if status == 'None' or not status:
            context['active_nav'] = None
        else:
            context['active_nav'] = status
        url = reverse('school:students')
        context['nav_items'] = navs.get_students_navs(url)
        context['statut_inscrit'] = status
        context['title'] = 'Inscriptions'
        context['subtitle'] = 'Gestion des incriptions'
        context['filter_form']  = forms.StudentsFilterForm(
            data=self.request.GET or None, 
            statut=status or '', user=self.request.user,
            year=self.get_year(),
            generic_level_ar=self.request.GET.get('generic_level_ar'),
            generic_level_fr=self.request.GET.get('generic_level_fr'))
        
        form = context['filter_form']
        if self.request.GET.get('generic_level_fr'):
            context['generic_level_fr_filter'] = models.GenericLevel.objects.get(
                pk=self.request.GET.get('generic_level_fr')).short_name
        if self.request.GET.get('generic_level_ar'):
            context['generic_level_ar_filter'] = models.GenericLevel.objects.get(
                pk=self.request.GET.get('generic_level_ar')).short_name

        if self.filter_applied:
            context['result_found'] = context['page_obj'].paginator.count
        
        current_date = date.today()
        total_paid_today = models.Payment.objects \
            .for_user(user=self.request.user, year=self.get_year()) \
            .aggregate(
                inscription_today=Sum(
                    'inscription', 
                    filter=Q(created_at__date=current_date)),
                scolarite_today=Sum(
                    'amount', 
                    filter=Q(created_at__date=current_date)),
                annexe_today=Sum(
                    'annexe', 
                    filter=Q(created_at__date=current_date), 
                    distinct=True),
            )
        context['total_paid_today'] = (
            (total_paid_today['inscription_today'] or 0) +
            (total_paid_today['scolarite_today'] or 0) +
            (total_paid_today['annexe_today'] or 0)
        )
        return context
    
    def get_queryset(self):
        queryset = super().get_queryset() \
            .annotate(
                paid=Sum('payment__amount') + Sum('payment__inscription') + Sum('payment__annexe'),
                amount=F('enrollment_fees') + F('year_fees') + F('annexe_fees'),
                remaining=F('amount') - Coalesce(F('paid'), 0, output_field=IntegerField()),
            )
        return self.sort(self.request.GET, queryset, ['student__last_name', 'student__first_name'])


class StudentDetailView(mixins.PermissionRequiredMixin, BaseHTMXRequestsView, 
                        generic.DetailView):
    model = models.Enrollment
    template_name = 'partials/navs_base.html'
    permission_required = 'school.view_student'

    def get_queryset(self):
        user = self.request.user
        year = get_session_year(self.request)
        return models.Enrollment.objects.for_user(user, year=year) \
            .select_related('year', 'generic_level_fr', 'generic_level_ar', 
                            'level_fr', 'level_ar', 
                            'level_fr__generic_level', 'level_ar__generic_level',
                            'student')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        enrollment = self.get_object()
        context['EDUCATION_ARABIC'] = main_utils.EDUCATION_ARABIC
        context['page_title'] = "Détails de l'élève"
        context['nav_items'] = navs.get_student_detail_navs(
            reverse('school:student_detail', args=[enrollment.pk]),
            self.request.user
        )
        context['title'] = f"{enrollment}"
        context['subtitle'] = "Informations détaillées sur l'élève"

        section = self.request.GET.get('section')
        if not section or section == 'identite':
            context['partial_template'] = 'partials/student/detail/identity.html'
        elif section == 'paiements':
            context['partial_template'] = 'partials/student/detail/payments.html'
            context['total_paid'] = enrollment.payment_set.aggregate(
                total=Sum('amount') + Sum('inscription') + Sum('annexe')
            )['total'] or 0
        elif 'moyennes' in section :
            context['partial_template'] = 'partials/student/detail/average.html'
            terms = None
            term_results = None
            results = {}
            education = main_utils.EDUCATION_FRENCH
            if 'ar' in section:
                education = main_utils.EDUCATION_ARABIC
                terms = SchoolTerm.objects.active(
                    user=self.request.user, 
                    level=enrollment.generic_level_ar,
                    education=main_utils.EDUCATION_ARABIC,
                    year=enrollment.year)
                term_results = enrollment.termresult_set.filter(school_term__education=main_utils.EDUCATION_ARABIC)
            else:
                terms = SchoolTerm.objects.active(
                    user=self.request.user, 
                    level=enrollment.generic_level_fr,
                    education=main_utils.EDUCATION_FRENCH,
                    year=enrollment.year)
                term_results = enrollment.termresult_set.filter(school_term__education=main_utils.EDUCATION_FRENCH)

            for term in terms:
                obj = term_results.filter(school_term=term).first()
                if obj:
                    results[
                        str(term)] = [
                                        obj.school_term.id,
                                        obj.average if obj.average or obj.average == 0 else '-', 
                                        obj.rank if obj.average and obj.average != 0 else 'NC'
                                    ]
                else:
                    results[str(term)] = ['-', 'NC']
            annual_result = enrollment.educationyearresult_set.filter(education=education).first()
            if annual_result:
                results['RESULTAT ANNUEL'] = [
                    '',
                    annual_result.average if annual_result.average or annual_result.average == 0 else '-', 
                    annual_result.rank if annual_result.average and annual_result.average != 0 else 'NC'
                ]
                context['dfa'] = annual_result.decision

            context['results'] = results

        context['active_nav'] = section
        return context

    def handle_no_permission(self):
        return HttpResponse(status=403, headers={'HX-Trigger': 'permission_denied'})


class StudentStatusChangeView(
    mixins.PermissionRequiredMixin, 
    generic.UpdateView, 
    BaseHTMXRequestsView):

    model = models.Enrollment   
    template_name = 'partials/confirm.html'
    permission_required = 'school.add_payment'
    fields = []

    def get_queryset(self):
        user = self.request.user
        return models.Enrollment.objects.for_user(user, year=self.get_year())

    def form_valid(self, form):
        obj = self.get_object()
        obj.active = not obj.active
        obj.save(update_fields=['active'])
        return HttpResponse(status=204, headers={'HX-Trigger': 'saved'})
    
    def handle_no_permission(self):
        return HttpResponse(status=403, headers={'HX-Trigger': 'permission_denied'})


def save_files(student, files):
    if files.get('3-photo'):
        student.photo = files.get('3-photo')
    elif files.get('1-photo'):
        student.photo = files.get('1-photo')
    elif files.get('2-photo'):
        student.photo = files.get('2-photo')
    if files.get('3-certificate_img'):
        student.certificate_img = files.get('3-certificate_img')

def student_form_view(request, pk=None):
    has_perm = request.user.has_perm('school.add_enrollment')
    user_can_view_payment = request.user.has_perm('school.view_payment')
    if not has_perm:
        return HttpResponse(status=403, headers={'HX-Trigger': 'permission_denied'})
    user = request.user
    year = get_session_year(request)
    enrollment = None
    student = None

    if pk:
        enrollment = models.Enrollment.objects.for_user(
            user, year=year, pk=pk).first()
        student = enrollment.student
    student_exists = bool(enrollment)

    student_infos_form = forms.StudentFormStepOneBasicInfos(
        user, data=request.POST or None, 
        instance=student)
    parents_form = forms.StudentFormStepTwoParents(
        request.POST or None, instance=student)
    level_form = forms.StudentFormStepThreePayment(
        user, year, data=request.POST or None, 
        instance=enrollment)
    files_form = forms.StudentFormStepFourPhotos(
        data=request.POST or None, files=request.FILES or None,
        instance=student)

    # Check if forms are valid
    form_valid = student_infos_form.is_valid() and \
        parents_form.is_valid()
    
    is_level_plan = False
    plan_generic_level = None
    first_level_ar = None
    first_level_fr = None
    level_data = None
    subscription = user.get_school_subscription(
        user.school_id, year.name)
    subscription_plan = subscription.plan

    if form_valid:
        if subscription_plan == models.Subscription.PLAN_LEVEL:
            is_level_plan = True
            plan_generic_level = subscription.level
            first_level_fr = models.Level.objects.for_user(
                user=user, year=year, education=main_utils.EDUCATION_FRENCH, 
            ).filter(generic_level=plan_generic_level).first()
            first_level_ar = models.Level.objects.for_user(
                user=user, year=year, education=main_utils.EDUCATION_ARABIC, 
            ).filter(generic_level=plan_generic_level).first()
            level_data = {
                'generic_level_fr': plan_generic_level,
                'level_fr': first_level_fr,
                'generic_level_ar': plan_generic_level,
                'level_ar': first_level_ar
            }
        else:
            form_valid = level_form.is_valid()
    
    # Save data if post req and form are valid
    if request.method == 'POST' and form_valid:
        student_data = student_infos_form.cleaned_data
        parents_data = parents_form.cleaned_data

        if not is_level_plan:
            level_data = level_form.cleaned_data

        files = request.FILES
        enrollment_fee = level_data.get('enrollment_fee1', 0)
        year_fee = level_data.get('year_fee1', 0)
        annexe_fee = level_data.get('annexe_fee1', 0)
        payment_date = level_data.get('date1')
        
        if 'enrollment_fee1' in level_data:
            del level_data['enrollment_fee1']
        if 'year_fee1' in level_data:
            del level_data['year_fee1']
        if 'annexe_fee1' in level_data:
            del level_data['annexe_fee1']
        if 'date1' in level_data:
            del level_data['date1']

        # Update data
        if student_exists:
            if request.user.has_perm('school.add_enrollment'):
               level_data['active'] = True
            enrollment_qs = models.Enrollment.objects.filter(id=enrollment.id)
            enrollment_qs.update(**level_data)
            id = enrollment.student.id
            queryset = models.Student.objects.filter(id=id)
            queryset.update(**student_data, **parents_data)
            student = queryset.first()
            save_files(student, files)
            student.save(update_fields=['photo', 'certificate_img'])

        else:
            # Save student data
            student = models.Student(
                origin=year,
                school=user.school, **student_data, **parents_data)
            save_files(student, files)
            student.save()

            # Save enrollment data

            enrollment = models.Enrollment(
                year=year,
                school=user.school, agent=user,
                **level_data
            )
            enrollment.student = student
            if user.has_perm('school.add_enrollment') and not enrollment.active:
                enrollment.active = True
            enrollment.save()

        update_payments_data(
            request, enrollment, enrollment_fee,
            annexe_fee, year_fee, payment_date,
            notify_parent=request.POST.get('sms_check') == 'on')
        status = {
            'status': 'success',
            'receipt_url': f"{reverse('school:student_payments_pdf', args=[enrollment.pk])}" 
        }
        if user_can_view_payment:
            print('Student saved')
            return HttpResponse(json.dumps(status), status=206, headers={'HX-Trigger': 'saved'})
        return HttpResponse(status=204, headers={'HX-Trigger': 'saved'})
    
    payments = None
    if enrollment:
        qs_fr = models.Level.objects.for_user(request.user, year=year)
        level_form.fields['level_fr'].queryset = qs_fr

        qs_ar = models.Level.objects.for_user(
            request.user, year=year, education=main_utils.EDUCATION_ARABIC)
        level_form.fields['level_ar'].queryset = qs_ar
        payments = enrollment.payment_set.order_by('created_at')
    
    context = {
        'student_form': student_infos_form,  
        'parents_form': parents_form,  
        'level_form': level_form,  
        'files_form': files_form,  
        'level_fr': request.GET.get('level_fr'),
        'level_ar': request.GET.get('level_ar'),
        'payments': payments,
        'max_year': datetime.now().year,
    }

    if student_exists:
        context['enrollment'] = enrollment

    hide_and_disable_fields(request.user, context, student_infos_form.fields, creation=not bool(pk))
    hide_and_disable_fields(request.user, context, level_form.fields, creation=not bool(pk))

    template_name = 'partials/student/custom_student_form.html'
    if subscription_plan == models.Subscription.PLAN_LEVEL:
        template_name = 'partials/student/student_form_minimal.html'

    context['blank_photo'] = '/static/img/avatar.jpg'
    return render(request, template_name, context)

class StudentFormWizardView(mixins.PermissionRequiredMixin, SessionWizardView):
    template_name = 'partials/student/student_form_wizard.html'
    file_storage = FileSystemStorage(location=os.path.join(settings.MEDIA_ROOT, 'photos'))
    form_list = [
        forms.StudentFormCustomStepOne,
        forms.StudentFormCustomStepTwo,
    ]
    permission_required = 'school.add_enrollment'

    def get_form_prefix(self, step=None, form=None):
        if step == '1':
            return '2'
        return super().get_form_prefix(step, form)

    def handle_no_permission(self):
        return HttpResponse(status=403, headers={'HX-Trigger': 'permission_denied'})

    @atomic()
    def done(self, form_list, **kwargs):
        # Get cleaned data
        year = get_session_year(self.request)
        student_data = form_list[0].cleaned_data
        form2_data = form_list[1].cleaned_data
        parents_data = {
            'father': form2_data.get('father'), 
            'mother': form2_data.get('mother')
        }

        del form2_data['father']
        del form2_data['mother']
        level_data = form2_data
        files = self.request.FILES
        enrollment_fee = level_data.get('enrollment_fee1') or 0
        year_fee = level_data.get('year_fee1') or 0
        annexe_fee = level_data.get('annexe_fee1') or 0
        user = self.request.user

        photo = level_data['photo']

        if 'enrollment_fee1' in level_data:
            del level_data['enrollment_fee1']
        if 'year_fee1' in level_data:
            del level_data['year_fee1']
        if 'annexe_fee1' in level_data:
            del level_data['annexe_fee1']

        if 'photo' in level_data:
            del level_data['photo']
            
        # Update data
        if self.kwargs.get('pk'):
            enrollment = models.Enrollment.objects.for_user(
                self.request.user, pk=self.kwargs.get('pk'), year=year
            )
            if self.request.user.has_perm('school.add_enrollment'):
               level_data['active'] = True

            if 'enrollment_fee1' in level_data:
                del level_data['enrollment_fee1']
            if 'year_fee1' in level_data:
                del level_data['year_fee1']
            if 'annexe_fee1' in level_data:
                del level_data['annexe_fee1']

            generic_level_fr = level_data['level_fr'].generic_level
            generic_level_ar = None
            if level_data['level_ar']:
                generic_level_ar = level_data['level_ar'].generic_level

            enrollment.update(
                **level_data, generic_level_fr=generic_level_fr, 
                generic_level_ar=generic_level_ar)
            id = enrollment.first().student.id
            queryset = models.Student.objects.filter(id=id)
            queryset.update(
                **student_data, **parents_data
            )
            student = queryset.first()
            save_files(student, files)
            student.save(update_fields=['photo', 'certificate_img'])

            if (enrollment_fee + year_fee + annexe_fee) > 0:
                payment_obj = models.Payment.objects.create(
                    enrollment=enrollment.first(),
                    agent=user,
                    amount=year_fee,
                    inscription=enrollment_fee,
                    annexe=annexe_fee
                )
                try:
                    send_sms_notification.delay(self.request.session.session_key, enrollment.id, payment_obj.id)
                except:
                    pass
            return HttpResponse(status=204, headers={'HX-Trigger': 'saved'})
       
        # Save student data
        user = self.request.user
        student = models.Student(
            origin=year,
            school=user.school,
            **student_data, **parents_data)
        save_files(student, files)
        student.save()

        # Save enrollment data

        enrollment = models.Enrollment(
            year=year, school=user.school, agent=user, **level_data
        )

        enrollment.generic_level_fr = level_data['level_fr'].generic_level
        if level_data['level_ar']:
            enrollment.generic_level_ar = level_data['level_ar'].generic_level

        enrollment.student = student
        if user.has_perm('school.add_enrollment') and not enrollment.active:
            enrollment.active = True
        enrollment.save()

        if (enrollment_fee + year_fee + annexe_fee) > 0:
            models.Payment.objects.create(
                enrollment=enrollment,
                agent=user, amount=year_fee,
                inscription=enrollment_fee,
                annexe=annexe_fee
                )

        sweetify.success(self.request, 'Fait', text='Enregistré', 
                         persistent=True)
        return HttpResponse(status=204, headers={'HX-Trigger': 'saved'})

    def get(self, request, *args, **kwargs):
        if not bool(request.htmx):
            url = reverse('school:students')
            return HttpResponseRedirect(url)
        if not self.kwargs.get('pk') and self.request.GET.get('reprise'):
            try:
                return self.render(self.get_form())
            except KeyError:
                return super().get(request, *args, **kwargs)
        return super().get(request, *args, **kwargs)
        
    def get_form_kwargs(self, step=None):
        kwargs = super().get_form_kwargs(step)
        if step == '1' or step == '0':
            kwargs['user'] = self.request.user
        if step == '1' and self.kwargs.get('pk'):
            kwargs['pk'] = self.kwargs.get('pk')
        if step == '1':
            kwargs['year'] = get_session_year(self.request)
        return kwargs
    
    def get_form_instance(self, step):
        pk = self.kwargs.get('pk')
        year = get_session_year(self.request)
        if pk:
            user = self.request.user
            queryset = models.Enrollment.objects.for_user(user=user, pk=pk, year=year)
            enrollment = None
            student = None
            exists = queryset.exists()
            if exists:
                enrollment = queryset.first()
                student = enrollment.student
            
            if exists and step == '1':
                return enrollment
            elif exists:
                return student
        return super().get_form_instance(step)
    
    def get_context_data(self, form, **kwargs):
        context  = super().get_context_data(form, **kwargs)
        step = self.steps.step0
        if step == 1:
            enrollment = self.get_form_instance(str(step))
            user = self.request.user
            year = get_session_year(self.request)
            
            qs_fr = None
            if enrollment and enrollment.grade_set.filter(
                subject__subject__education=main_utils.EDUCATION_FRENCH).exists():
                qs_fr = models.Level.objects.for_user(user, year=year)\
                    .filter(generic_level=enrollment.generic_level_fr)\
                    .annotate(students=Count('enrollment', 
                              filter=Q(enrollment__active=True), 
                              distinct=True)) \
                    .filter(
                        Q(students__lt=F('max')) | \
                        Q(enrollment__level_fr=enrollment.level_fr))
                context['form'].fields['level_fr'].queryset = qs_fr

            qs_ar = None    
            if enrollment and enrollment.grade_set.filter(
                subject__subject__education=main_utils.EDUCATION_ARABIC).exists():
                qs_ar = models.Level.objects.for_user(
                    user, year=year, education=main_utils.EDUCATION_ARABIC)\
                    .filter(generic_level=enrollment.generic_level_ar)
                context['form'].fields['level_ar'].queryset = qs_ar\
                    .annotate(students=Count('enrollment', 
                            filter=Q(enrollment__active=True),
                            distinct=True)) \
                    .filter(
                        Q(students__lt=F('max')) | \
                        Q(enrollment__level_ar=enrollment.level_ar))
            
            if enrollment and enrollment.year_fees > 0:
                if 'enrollment_fee1' in context['form'].fields:
                    del context['form'].fields['enrollment_fee1']
                if 'year_fee1' in context['form'].fields:
                    del context['form'].fields['year_fee1']
                if 'annexe_fee1' in context['form'].fields:
                    del context['form'].fields['annexe_fee1']

        hide_and_disable_fields(self.request.user, context, creation=not bool(self.kwargs.get('pk')))
        return context


class PaymentsListView(
    mixins.PermissionRequiredMixin, 
    BaseHTMXRequestsView, 
    generic.ListView):

    model = models.Payment
    template_name = 'partials/payment/payments_list.html'
    context_object_name = 'payments'
    permission_required = 'school.view_payment'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        nav_items = navs.get_payments_list_navs(reverse('school:payments'))
        form = forms.DateRangeForm()
        context['nav_items'] = nav_items
        context['form'] = form
        context['page_title'] = "Gestion des versements"

        period = self.request.GET.get('periode')
        if period == 'recents':
            context['active_nav'] = 'recent_payments'
        else:
            context['active_nav'] = 'day_payments'
            context['subtitle'] = "Liste des versements d'aujourd'hui"

        if not bool(self.request.htmx):
            context['template_name'] = self.template_name
        context['current_date'] = datetime.today().date().strftime('%d/%m/%Y')
        return context
    
    def get_queryset(self):
        year = self.get_year()
        user = None
        if self.request.user.role == main_utils.ROLE_ACCOUNTANT:
            user = self.request.user
            
        queryset = models.Payment.objects \
            .for_user(year, user) \
            .filter(enrollment__school=self.request.user.school)\
            .select_related(
                'enrollment', 'enrollment__student',
                'enrollment__level_fr',
                'enrollment__level_fr__generic_level',
                'enrollment__year'
                ) \
                .only(
                'enrollment__student__student_id', 
                'enrollment__student__first_name', 
                'enrollment__student__last_name',
                'enrollment__year__id',
                'enrollment__year__name',
                'enrollment__level_fr__number',
                'enrollment__level_fr__generic_level__name',
                'enrollment__level_fr__generic_level__short_name',
                'amount', 'created_at', 'payment_type'
                )
        
        period = self.request.GET.get('periode')
        if period == 'recents':
            return queryset[:100]
        else:
            current_date = datetime.today().date()
            queryset = queryset.filter(
                Q(enrollment__year=year), 
                Q(created_at__month=current_date.month), 
                Q(created_at__day=current_date.day)
            )
            return queryset
    
    def handle_no_permission(self):
        return HttpResponse(status=403, headers={'HX-Trigger': 'permission_denied'})


class PaymentsUpdateView(mixins.PermissionRequiredMixin, generic.UpdateView):
    model = models.Payment
    template_name = 'partials/payment/payment_update.html'
    permission_required = 'school.change_payment'
    fields = ['inscription', 'amount', 'annexe']

    def form_valid(self, form):
        form.save(True)
        return HttpResponse(status=204, headers={'HX-Trigger': 'saved'})

    def get_queryset(self):
        year = get_session_year(self.request)
        return models.Payment.objects.for_user(year=year, user=self.request.user)

    def handle_no_permission(self):
        return HttpResponse(status=403, headers={'HX-Trigger': 'permission_denied'})


class PaymentDeleteView(mixins.PermissionRequiredMixin, generic.DeleteView):
    model = models.Payment
    template_name = 'partials/confirm.html'
    permission_required = 'school.change_payment'

    def form_valid(self, form=None):
        self.get_object().delete()
        return HttpResponse(status=204, headers={'HX-Trigger': 'saved'})

    def get_queryset(self):
        year = get_session_year(self.request)
        return models.Payment.objects.for_user(year=year, user=self.request.user)
    
    def handle_no_permission(self):
        return HttpResponse(status=403, headers={'HX-Trigger': 'permission_denied'})
    

class PaymentCreateView(mixins.PermissionRequiredMixin, generic.CreateView):
    model = models.Payment
    form_class = forms.PaymentForm
    template_name = 'partials/payment/payment_edit.html'
    permission_required = 'school.add_payment'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.object_exists = False

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        student_id = self.request.GET.get('student_id')
        if student_id:
            kwargs['student_id'] = student_id
        return kwargs
    
    def get_context_data(self, **kwargs):
        year = get_session_year(self.request)
        context = super().get_context_data(**kwargs)
        context['EDUCATION_ARABIC'] = main_utils.EDUCATION_ARABIC
        user = self.request.user
        if self.object_exists:
            context['form'].fields['student_id'].widget.attrs['readOnly'] = True

        student_id = self.request.GET.get('student_id')
        if student_id:
            qs = models.Enrollment.objects.for_user(
                user, user.school, year=year) \
                .filter(
                    Q(student__student_id=student_id) | 
                    Q(student__identifier=student_id)) \
                .annotate(
                    total_to_pay=F('enrollment_fees') + F('year_fees') + F('annexe_fees'),
                    inscription_paid=Sum('payment__inscription', distinct=True),
                    scolarite_paid=Sum('payment__amount', distinct=True),
                    annexe_paid=Sum('payment__annexe', distinct=True),
                    total_paid=F('inscription_paid') + F('scolarite_paid') + F('annexe_paid'),
                    remaining=F('total_to_pay') - F('total_paid'),
                )
            enrollment = qs.first()
            context['payments'] = enrollment.payment_set.order_by('created_at')
            context['enrollment'] = enrollment  

        return context
    
    def get_initial(self):
        initial = super().get_initial()
        user = self.request.user
        student_id = self.request.GET.get('student_id')
        queryset = None
        year = get_session_year(self.request)
        if student_id:
            student_id = student_id.upper()
            queryset = models.Enrollment.objects.for_user(
                user, user.school, year=year) \
                .filter(
                    Q(student__student_id=student_id) | 
                    Q(student__identifier=student_id))

        if student_id and queryset.exists():
            self.object_exists = True
            enrollment = queryset.first()
            initial['student_id'] = student_id
            initial['last_name'] = enrollment.student.last_name
            initial['first_name'] = enrollment.student.first_name
            initial['level_fr'] = enrollment.level_fr
            initial['level_ar'] = enrollment.level_ar
        return initial

    def form_valid(self, form):
        cd = form.cleaned_data
        user = self.request.user
        year = get_session_year(self.request)
        student_id = self.request.POST.get('student_id')
        enrollment = models.Enrollment.objects.for_user(user, user.school, year=year) \
            .filter(
                Q(student__student_id=student_id) | 
                Q(student__identifier=student_id)).first()
        enrollment_fee = cd.get('enrollment_fee1', 0) or 0
        year_fee = cd.get('year_fee1', 0) or 0
        annexe_fee = cd.get('annexe_fee1', ) or 0
        print(cd.get('date1'))
        # payment_date = datetime.strptime(cd.get('date1'),)
        update_payments_data(
            self.request, enrollment, int(enrollment_fee), 
            int(annexe_fee), int(year_fee), cd.get('date1'),
            notify_parent=self.request.POST.get('sms_check') == 'on')
        status = {
            'status': 'success',
            'receipt_url': f"{reverse('school:student_payments_pdf', args=[enrollment.pk])}?template=2" 
        }
        return HttpResponse(json.dumps(status), status=206, headers={'HX-Trigger': 'saved'})

    def get(self, request, *args, **kwargs):
        if bool(self.request.htmx):
            return super().get(request, *args, **kwargs)
        return HttpResponseRedirect(reverse('school:payments', args=[2023]))
    
    def handle_no_permission(self):
        return HttpResponse(status=403, headers={'HX-Trigger': 'permission_denied'})
    

@decorators.login_required()
def get_scolarite_fee(request):
    # Read get params
    level_fr = request.GET.get('2-generic_level_fr') or 0
    level_ar = request.GET.get('2-generic_level_ar') or 0
    selected_level_fr = request.GET.get('2-level_fr') or 0
    selected_level_ar = request.GET.get('2-level_ar') or 0
    status = request.GET.get('2-status')
    current_enrollment_fees = request.GET.get('2-enrollment_fees') or 0
    current_year_fees = request.GET.get('2-year_fees') or 0
    current_annexe_fees = request.GET.get('2-annexe_fees') or 0
    if current_enrollment_fees:
        current_enrollment_fees = int(current_enrollment_fees)
    if current_year_fees:
        current_year_fees = int(current_year_fees)
    if current_annexe_fees:
        current_annexe_fees = int(current_annexe_fees)

    user = request.user
    year = get_session_year(request)
    
    # Get price
    inscription = current_enrollment_fees
    scolarite = current_year_fees
    annexe = current_annexe_fees
    pricing_data = None

    this_level_fr = level_fr
    this_level_ar = level_ar
    create_mode = request.GET.get('create_mode')

    if selected_level_fr:
        this_level_fr = models.Level.objects.get(pk=selected_level_fr).generic_level.id                
    
    if selected_level_ar:
        this_level_ar = models.Level.objects.get(pk=selected_level_ar).generic_level.id  
                      
    if create_mode or (not inscription or not scolarite or not annexe):
        if selected_level_fr and not selected_level_ar and not this_level_ar:
            this_level_ar = this_level_fr
        elif selected_level_ar and not selected_level_fr and not this_level_fr:
            this_level_fr = this_level_ar

        pricing_data = school_utils.get_fees(
            user, this_level_fr, this_level_ar, status, with_annexe=True
        )
    
    if not inscription:
        inscription = pricing_data['inscription']
    if not scolarite:
        scolarite = pricing_data['scolarite']
    if not annexe:
        annexe = pricing_data['annexe']

    # Filter levels 
    levels_fr = models.Level.objects.for_user(user, year=year)
    levels_ar = models.Level.objects \
        .for_user(user, year=year, education=main_utils.EDUCATION_ARABIC)
    
    if create_mode:
        if this_level_ar and not selected_level_ar:
            selected_level_ar = levels_ar.filter(generic_level=this_level_ar).first()
        elif this_level_fr and not selected_level_fr:
            selected_level_fr = levels_fr.filter(generic_level=this_level_fr).first()


    # Set form initial data
    form = forms.StudentFeesPartial(
        create_mode=create_mode,
        initial={
            'enrollment_fees': inscription, 
            'year_fees': scolarite,
            'annexe_fees': annexe,
            'generic_level_fr': this_level_fr,
            'generic_level_ar': this_level_ar,
            'level_fr': selected_level_fr,
            'level_ar': selected_level_ar,
        },
    )

    # Filter levels queryset
    form.fields['level_fr'].queryset = levels_fr
    form.fields['level_ar'].queryset = levels_ar

    # Prevent other users (computer scientist and teachers) from tampering with fees 
    if not request.user.has_perm('school.add_payment'):
        form.fields['enrollment_fees'].widget.attrs['readonly'] = True
        form.fields['year_fees'].widget.attrs['readonly'] = True
        form.fields['annexe_fees'].widget.attrs['readonly'] = True

    context = {'form': form}
    return render(request, 'partials/student/student_fees_form.html', context)
    

class LevelPricingListView(mixins.PermissionRequiredMixin, 
                           BaseHTMXRequestsView, generic.ListView):
    model = models.LevelPricing
    template_name = 'partials/pricing/pricing_base.html'
    context_object_name = 'pricing_list'
    permission_required = 'school.view_payment'

    def get_queryset(self):
        user = self.request.user
        year = get_session_year(self.request)
        education = self.request.GET.get('education')
        if education and education == 'ar':
            education = main_utils.EDUCATION_ARABIC
        elif education and education == 'fr':
            education = main_utils.EDUCATION_FRENCH
        queryset = models.LevelPricing.objects.for_school(user, education=education) \
            .select_related('generic_level', 'year', 'school') \
            .annotate(extra_fees=Sum('levelextraprice__price'))
            
        return queryset.only(
                'school__id', 'generic_level__id', 'generic_level__short_name',
                'year__id', 'student_status', 'scolarite', 'inscription'
            )
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['partial_template'] = 'partials/pricing/pricing_list.html'
        context['page_title'] = "Gestion des tarifs d'inscription"

        education = self.request.GET.get('education')
        if education:
            context['education'] = education

        if education == 'fr' or not education:
            context['active_nav'] = 'pricing_fr'
        elif education == 'ar':
            context['active_nav'] = 'pricing_ar'
        context['nav_items'] = navs.get_pricing_navs(
            self.request.user, reverse('school:pricing'),
            reverse('school:pricing_categories')
        )
        return context

    def handle_no_permission(self):
        return HttpResponse(status=403, headers={'HX-Trigger': 'permission_denied'})


class PricingCategoryListView(mixins.PermissionRequiredMixin, 
                              BaseHTMXRequestsView, generic.ListView, ):
    model = models.PriceCategory
    template_name = 'partials/pricing/pricing_base.html'
    context_object_name = 'categories'
    permission_required = 'school.view_payment'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['partial_template'] = 'partials/pricing/pricing_category_list.html'
        context['active_nav'] = 'category'
        context['page_title'] = "Gestion des groupes de tarifs"
        context['nav_items'] = navs.get_pricing_navs(
            self.request.user, reverse('school:pricing'),
            reverse('school:pricing_categories')
        )
        return context

    def get_queryset(self):
        user = self.request.user
        year = get_session_year(self.request)
        return models.PriceCategory.objects.for_school(user, year)

    def handle_no_permission(self):
        return HttpResponse(status=403, headers={'HX-Trigger': 'permission_denied'})


class PricingCategoryCreateView(mixins.PermissionRequiredMixin, 
                                generic.CreateView):
    model = models.PriceCategory
    fields = ['label']
    template_name = 'partials/pricing/pricing_category_form.html'
    permission_required = 'school.add_payment'

    def form_valid(self, form):
        obj = form.save(commit=False)
        obj.school = self.request.user.school
        obj.year = school_utils.get_current_year()
        obj.save()
        return HttpResponse(status=204, headers={'HX-Trigger': 'saved'})

    def handle_no_permission(self):
        return HttpResponse(status=403, headers={'HX-Trigger': 'permission_denied'})


class PricingCategoryUpdateView(mixins.PermissionRequiredMixin, 
                                generic.UpdateView):
    model = models.PriceCategory
    fields = ['label']
    template_name = 'partials/pricing/pricing_category_form.html'
    permission_required = 'school.add_payment'

    def form_valid(self, form):
        form.save(True)
        return HttpResponse(status=204, headers={'HX-Trigger': 'saved'})
    
    def handle_no_permission(self):
        return HttpResponse(status=403, headers={'HX-Trigger': 'permission_denied'})
    

class LevelPricingCreateView(mixins.PermissionRequiredMixin, 
                             generic.CreateView):
    model = models.LevelPricing
    form_class = forms.PricingForm
    template_name = 'partials/pricing/pricing_form.html'
    permission_required = 'school.add_payment'

    def form_valid(self, form):
        obj = form.save(False)
        obj.school = self.request.user.school
        obj.year = school_utils.get_current_year()
        obj = form.save()
        return HttpResponse(status=204, headers={'HX-Trigger': 'saved'})

    def handle_no_permission(self):
        return HttpResponse(status=403, headers={'HX-Trigger': 'permission_denied'})
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        education = self.request.GET.get('education')
        field = context['form'].fields['education']
        if education == 'ar':
            field.initial = main_utils.EDUCATION_ARABIC
            field.choices = ((main_utils.EDUCATION_ARABIC, 'Arabe'),)
        else:
            field.choices = ((main_utils.EDUCATION_FRENCH, 'Français'),)
        field.widget.attrs['readOnly'] = True
        return context
    
    def handle_no_permission(self):
        return HttpResponse(status=403, headers={'HX-Trigger': 'permission_denied'})


class CyclePricingCreateView(mixins.PermissionRequiredMixin, generic.CreateView):
    model = models.LevelPricing
    form_class = forms.CyclePricingForm
    template_name = 'partials/pricing/pricing_form.html'
    permission_required = 'school.add_payment'

    def form_valid(self, form):
        data = form.cleaned_data
        school = self.request.user.school
        user = self.request.user
        year = get_session_year(self.request)
        
        objs_to_create = []
        objs_to_update = []
        generic_levels = models.GenericLevel.objects.for_school(user, year) 
        cycle = data.get('cycle')
        if cycle == main_utils.CYCLE_PRIMARY:
            generic_levels = generic_levels.filter(cycle=main_utils.CYCLE_PRIMARY)
        else: 
            if cycle == '1er':
                generic_levels = generic_levels.filter(short_name__in=['6EME', '5EME', '4EME', '3EME'])
            elif cycle == '2nd':
                generic_levels = generic_levels.filter(short_name__in=['2NDE', '1ERE', 'TLE'])
            
            if data.get('education') == main_utils.EDUCATION_FRENCH:
                if data.get('student_status'):
                    generic_levels = generic_levels.filter(student_status=data.get('student_status'))

        pricing_qs = models.LevelPricing.objects.for_school(user, year, data.get('education'))
        for generic_level in generic_levels:
            pricing = pricing_qs.filter(
                generic_level=generic_level, 
                school=school, year=year).first()
            if pricing:
                pricing.scolarite = data.get('scolarite')
                pricing.inscription = data.get('inscription')
                pricing.annexe = data.get('annexe')
                objs_to_update.append(pricing)
            else:
                objs_to_create.append(models.LevelPricing(
                    school=school, year=year, generic_level=generic_level,
                    education=data.get('education'), 
                    scolarite=data.get('scolarite'), 
                    inscription=data.get('inscription'),
                    annexe=data.get('annexe'),
                    student_status=data.get('student_status')
                ))
        
        # Bulk create and bulk update
        models.LevelPricing.objects.bulk_create(objs_to_create)
        models.LevelPricing.objects.bulk_update(objs_to_update, ['scolarite', 'inscription', 'annexe'])
        return HttpResponse(status=204, headers={'HX-Trigger': 'saved'})

    def handle_no_permission(self):
        return HttpResponse(status=403, headers={'HX-Trigger': 'permission_denied'})
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        education = self.request.GET.get('education')
        field = context['form'].fields['education']
        if education == 'ar':
            field.initial = main_utils.EDUCATION_ARABIC
            field.choices = ((main_utils.EDUCATION_ARABIC, 'Arabe'),)
        else:
            field.choices = ((main_utils.EDUCATION_FRENCH, 'Français'),)
        field.widget.attrs['readOnly'] = True

        cycle = self.request.GET.get('cycle')
        cycle_field = context['form'].fields['cycle']
        if cycle == main_utils.CYCLE_PRIMARY:
            cycle_field.choices = ((main_utils.CYCLE_PRIMARY, 'Primaire'),)
            cycle_field.initial = main_utils.CYCLE_PRIMARY
        elif cycle == '1er':
            cycle_field.choices = (('1er', '1er Cycle (6ème -> 3ème)'),)
            cycle_field.initial = '1er'
        elif cycle == '2nd':
            cycle_field.choices = (('2nd', '2nd Cycle (2nde -> Tle)'),)
            cycle_field.initial = '2nd'
        cycle_field.widget.attrs['readonly'] = True
        return context
    
    def handle_no_permission(self):
        return HttpResponse(status=403, headers={'HX-Trigger': 'permission_denied'})


class LevelPricingUpdateView(mixins.PermissionRequiredMixin, 
                             generic.UpdateView):
    model = models.LevelPricing
    form_class = forms.PricingForm
    template_name = 'partials/pricing/pricing_form.html'
    permission_required = 'school.add_payment'

    def form_valid(self, form):
        form.save(True)
        return HttpResponse(status=204, headers={'HX-Trigger': 'saved'})
    
    def handle_no_permission(self):
        return HttpResponse(status=403, headers={'HX-Trigger': 'permission_denied'})


@decorators.login_required()
def extra_price_view(request, pk):
    user = request.user
    year = school_utils.get_current_year()
    queryset = models.LevelPricing.objects.for_school(
        user, year
    )
    instance = queryset.filter(id=pk).first()
    formset = forms.ExtraFeesInlineFormSet(
        request.POST or None, instance=instance)
    
    queryset = models.PriceCategory.objects.for_school(user, year)
    for form in formset:
        form.fields['category'].queryset = queryset

    if request.method == 'POST':
        if formset.is_valid():
            data = formset.save(False)
            for form in data:
                form.pricing = instance
                form.save()
            return HttpResponse(status=204, headers={'HX-Trigger': 'saved'})
    
    context = {'formset': formset} 
    return render(request, 'partials/pricing/extra_pricing_form.html', context)


class PaymentsSummaryListView(mixins.PermissionRequiredMixin,
                              BaseHTMXRequestsView, 
                              generic.ListView):
    model = CustomUser
    template_name = 'partials/report/reports_base.html'
    context_object_name = 'accountants'
    permission_required = 'school.view_payment'

    def get_queryset(self):
        year = self.get_year()
        queryset = CustomUser.for_user \
            .get_all(self.request.user, year, show_for_payments=True) \
            .only('last_name', 'first_name')
        
        period = self.request.GET.get('periode')
        if period == 'tout':
            return queryset.annotate(
                inscription=Sum('payment__inscription', 
                    filter=Q(payment__enrollment__year=year)),
                scolarite=Sum('payment__amount', 
                    filter=Q(payment__enrollment__year=year)),
                annexe=Sum('payment__annexe', 
                    filter=Q(payment__enrollment__year=year)),
                payments_total = Coalesce(F('inscription'), 0, output_field=IntegerField()) + \
                                 Coalesce(F('scolarite'), 0, output_field=IntegerField()) + \
                                 Coalesce(F('annexe'), 0, output_field=IntegerField())
            )
        else:
            current_date = datetime.today().date()
            return queryset.annotate(
                inscription=Sum('payment__inscription', 
                    filter=Q(payment__enrollment__year=year) &
                            Q(payment__created_at__month=current_date.month) & 
                            Q(payment__created_at__day=current_date.day)),
                scolarite=Sum('payment__amount', 
                    filter=Q(payment__enrollment__year=year) &
                            Q(payment__created_at__month=current_date.month) & 
                            Q(payment__created_at__day=current_date.day)),
                annexe=Sum('payment__annexe', 
                    filter=Q(payment__enrollment__year=year) &
                            Q(payment__created_at__month=current_date.month) & 
                            Q(payment__created_at__day=current_date.day)),
                payments_total = Coalesce(F('inscription'), 0, output_field=IntegerField()) + \
                                 Coalesce(F('scolarite'), 0, output_field=IntegerField()) + \
                                 Coalesce(F('annexe'), 0, output_field=IntegerField())
            )

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['partial_template'] = 'partials/report/payments_summary_list.html'
        context['payments_total'] = self.get_queryset().aggregate(
            total=Sum('payments_total')
        )['total']

        period = self.request.GET.get('periode')
        context['active_nav'] = 'day_payments'
        context['page_title'] = "Résumé des versements"

        if period == 'tout':
            context['active_nav'] = 'all_payments'
        
        nav_items = navs.get_payments_summary_navs(
            self.request.user,
            reverse('school:reports'), 
            reverse('school:period_reports'),
            reverse('school:level_reports'),
        )
        context['nav_items'] = nav_items
        return context

    def handle_no_permission(self):
        return HttpResponse(status=403, headers={'HX-Trigger': 'permission_denied'})


class PaymentsSummaryByLevelListView(mixins.PermissionRequiredMixin,
                              BaseHTMXRequestsView, 
                              generic.ListView):
    model = models.Level
    template_name = 'partials/report/reports_base.html'
    context_object_name = 'levels'
    permission_required = 'school.view_payment'

    def get_queryset(self):
        year = self.get_year()
        lang = self.request.GET.get('lang')
        if lang == 'fr':
            queryset = models.Level.objects.for_user(
                self.request.user, year=year, 
                education=main_utils.EDUCATION_FRENCH
            )
        elif lang == 'ar':
            queryset = models.Level.objects.for_user(
                self.request.user, year=year, 
                education=main_utils.EDUCATION_ARABIC
            )
        related_field_name = 'enrollment'
        if lang == 'ar':
            related_field_name += '_ar'
            
        return queryset.annotate(
                students=Count(related_field_name, distinct=True),
                inscription=Sum(related_field_name + '__payment__inscription'),
                scolarite=Sum(related_field_name + '__payment__amount'),
                annexe=Sum(related_field_name + '__payment__annexe'),
                total = Coalesce(F('inscription'), 0, output_field=IntegerField()) + \
                                 Coalesce(F('scolarite'), 0, output_field=IntegerField()) + \
                                 Coalesce(F('annexe'), 0, output_field=IntegerField())
                ) \
                .order_by('generic_level__order', 'number')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['partial_template'] = 'partials/report/report_by_level.html'

        lang = self.request.GET.get('lang')
        nav_items = navs.get_payments_summary_navs(
            self.request.user,
            reverse('school:reports'), 
            reverse('school:period_reports'),
            reverse('school:level_reports'),
        )
        context['nav_items'] = nav_items
        context['lang'] = lang
        related_field_name = 'enrollment'
        if lang == 'ar':
            related_field_name += '_ar'
        aggregate_result = self.get_queryset().aggregate(
                students=Count(related_field_name, distinct=True),
                total_paid=Sum(F('total')))
        context['students'] = aggregate_result['students'] or 0
        context['grand_total'] = aggregate_result['total_paid'] or 0
        context['page_title'] = "Résumé des versements par classe"

        if lang == 'fr':
            context['active_nav'] = 'classe'
        else:
            context['active_nav'] = 'classe_ar'
        return context

    def handle_no_permission(self):
        return HttpResponse(status=403, headers={'HX-Trigger': 'permission_denied'})


@decorators.login_required()
def period_payments_view(request):
    form = forms.DateRangeForm(request.POST or None)
    if bool(request.htmx):
        template_name = 'partials/report/reports_base.html'
    else:
        template_name = 'full_template.html'

    nav_items = navs.get_payments_summary_navs(
        request.user,
        reverse('school:reports'), 
        reverse('school:period_reports'),
        reverse('school:level_reports'),

    )
    partial_template = 'partials/report/payments_summary_list.html'
    context = {
        'form' : form, 
        'partial_template': partial_template, 
        'active_nav': 'period_payments',
        'template_name': 'partials/report/reports_base.html',
        'nav_items': nav_items,
        'page_title': 'Rapport périodique des versements'
    }

    if request.method == 'POST':
        if form.is_valid():
            # Create date objects
            start_date = request.POST['start']
            end_date = request.POST['end']
            date_range = [start_date, end_date]
            context['start'] = start_date
            context['end'] = end_date

            year = get_session_year(request)
            queryset = CustomUser.for_user \
                .get_all(request.user, year, show_for_payments=True) \
                .only('last_name', 'first_name')
            queryset = queryset.annotate(
                inscription=Sum('payment__inscription', filter=Q(payment__created_at__date__range=date_range)),
                scolarite=Sum('payment__amount', filter=Q(payment__created_at__date__range=date_range)),
                annexe=Sum('payment__annexe', filter=Q(payment__created_at__date__range=date_range)),
                payments_total = Coalesce(F('inscription'), 0, output_field=IntegerField()) + \
                                 Coalesce(F('scolarite'), 0, output_field=IntegerField()) + \
                                 Coalesce(F('annexe'), 0, output_field=IntegerField())
            )
            context['accountants'] = queryset
            context['payments_total'] = queryset.aggregate(
                total=Sum('payments_total'))['total']
            return render(request, template_name, context)
    return render(request, template_name, context)


class ExpensesListView(mixins.PermissionRequiredMixin, 
                       BaseHTMXRequestsView, generic.ListView):
    model = models.Payment
    template_name = 'partials/navs_base.html'
    context_object_name = 'expenses'
    permission_required = 'school.view_expense'

    def get_queryset(self):
        user = self.request.user
        year = self.get_year()
        queryset = models.Expense.objects.for_year(user, year)
        
        # Filter using GET param if provided
        category = self.request.GET.get('categorie')
        if category:
            queryset = queryset.filter(expense_type=category.upper())
        return queryset
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        category = self.request.GET.get('categorie')
        if category:
            context['selected_category'] = category
        context['active_nav'] = ''
        context['nav_items'] = navs.get_balance_nav(
            reverse('school:balance'), 
            reverse('school:expenses'))
        context['title'] = 'Dépenses'
        context['subtitle'] = "Suivi des dépenses de l'année scolaire"
        context['EXPENSE_TYPES'] = models.Expense.TYPE_CHOICES
        context['partial_template'] = 'partials/balance/expense_list.html'
        context['total'] = self.get_queryset().aggregate(
            total=Sum('amount', distinct=True))['total']
        return context
    
    def handle_no_permission(self):
        return HttpResponse(status=403, headers={'HX-Trigger': 'permission_denied'})
    

class ExpenseCreateView(mixins.PermissionRequiredMixin, 
                        generic.CreateView):
    model = models.Expense
    fields = ['expense_type', 'amount', 'commentaire']
    template_name = 'partials/balance/expense_form.html'
    permission_required = 'school.add_expense'

    def form_valid(self, form):
        data = form.save(False)
        user = self.request.user
        data.year = school_utils.get_current_year()
        data.agent = user
        data.school = user.school
        data.save()
        return HttpResponse(status=204, headers={'HX-Trigger': 'saved'})
    
    def handle_no_permission(self):
        return HttpResponse(status=403, headers={'HX-Trigger': 'permission_denied'})


class ExpenseUpdateView(mixins.PermissionRequiredMixin, 
                        generic.UpdateView):
    model = models.Expense
    fields = ['expense_type', 'amount', 'commentaire']
    template_name = 'partials/balance/expense_form.html'
    permission_required = 'school.add_payment'

    def form_valid(self, form):
        form.save(True)
        return HttpResponse(status=204, headers={'HX-Trigger': 'saved'})
    
    def handle_no_permission(self):
        return HttpResponse(status=403, headers={'HX-Trigger': 'permission_denied'})


@decorators.permission_required('school.view_payment')
def student_payments_report_pdf(request, pk):
    # Get school and user
    user = request.user
    year = get_session_year(request)

    # Get enrollment
    qs = models.Enrollment.objects.for_user(user, year=year, pk=pk) \
        .select_related('student') \
        .prefetch_related('payment_set')
    enrollment = qs.first()

    # Generate PDF document
    doc = None
    if request.GET.get('template') == '2':
        doc = reports.PaymentReport2(orientation='P', unit='mm')
    else:
        doc = reports.PaymentReport(orientation='P', unit='mm')
    doc.set_margins(1, 1, 1)
    doc.add_page()
    doc.school = user.school
    copies = request.GET.get('copies', 2)
    copies = int(copies)
    doc.add_content(enrollment, user, copies=copies)
    return doc.get_file_response(f'Releve de versement {user.id}', as_attachment=False)


@decorators.permission_required('school.view_payment')
def payments_list_pdf(request, period_start=None, period_end=None):
    # Get school and user
    user = request.user
    year = get_session_year(request)

    selected_user = None
    user_arg = request.GET.get('user_id')

    if user_arg:
        selected_user = CustomUser.for_user.get_all(user, show_for_payments=True).get(id=user_arg)

    queryset = models.Payment.objects.for_user(year=year, user=selected_user or user)
    start = request.GET.get('start')
    end = request.GET.get('end')

    payment_type = request.GET.get('type')
    report_type = request.GET.get('report_type')
    if report_type == models.Payment.TYPE_INSCRIPTION:
       queryset = queryset.filter(
           Q(payment_type=report_type) | Q(payment_type__isnull=True) & Q(inscription__gt=0)
        ) 
    elif report_type == models.Payment.TYPE_SCOLARITE:
       queryset = queryset.filter(
           Q(payment_type=report_type) | Q(payment_type__isnull=True) & Q(amount__gt=0)
        ) 
    elif report_type == models.Payment.TYPE_ANNEXE:
       queryset = queryset.filter(
           Q(payment_type=report_type) | Q(payment_type__isnull=True) & Q(annexe__gt=0)
        ) 

    period_not_defined = not period_start or not period_end
    if (payment_type == 'day_payments' or not payment_type) and period_not_defined:
        current_date = datetime.today().date().strftime('%Y-%m-%d')
        queryset = queryset.filter(
            created_at__date__range=[current_date, current_date])
    elif payment_type == 'period_payments':
        queryset = queryset.filter(
            created_at__date__range=[start, end])
        
    queryset = queryset.order_by(
        'created_at', 
        'enrollment__student__last_name', 
        'enrollment__student__first_name', 
        )
    
    # Generate PDF document
    doc = reports.PaymentsListReport(orientation='P', unit='mm')
    doc.set_margins(10, 10, 10)
    doc.add_page()
    doc.school = user.school
    doc.add_content(queryset=queryset, user=selected_user or user, payment_type=report_type)
    return doc.get_file_response(f'Liste des versements {user.id}')


@decorators.login_required()
def payments_list_pdf_by_class(request):
    # Get school and user
    user = request.user
    year = get_session_year(request)
    level_id = request.GET.get('level_id')
    level = models.Level.objects.filter(id=level_id).first()
    redden_names = True
    education = request.GET.get('education')
    related_field_name = 'enrollment'
    if education == 'ar':
        related_field_name += '_ar'
    queryset = models.Enrollment.objects.for_user(user, year=year)
    queryset = queryset.filter(Q(level_fr=level) | Q(level_ar=level)) \
        .select_related(
            'student', 'level_fr', 'level_ar', 'generic_level_fr', 
            'generic_level_ar', 'level_fr__generic_level', 
            'level_ar__generic_level')
    
    if not 'detailed' in request.GET:
        inscription_subquery = models.Payment.objects.filter(
            enrollment=OuterRef('pk')
        ).values('enrollment').annotate(
            total=Coalesce(Sum('inscription'), Value(0))
        ).values('total')

        scolarite_subquery = models.Payment.objects.filter(
            enrollment=OuterRef('pk')
        ).values('enrollment').annotate(
            total=Coalesce(Sum('amount'), Value(0))
        ).values('total')

        annexe_subquery = models.Payment.objects.filter(
            enrollment=OuterRef('pk')
        ).values('enrollment').annotate(
            total=Coalesce(Sum('annexe'), Value(0))
        ).values('total')

        # Total fees subquery
        fees_subquery = models.Enrollment.objects.filter(
            pk=OuterRef('pk')
        ).annotate(
            total=Coalesce(Sum('enrollment_fees'), Value(0)) +
                Coalesce(Sum('year_fees'), Value(0)) +
                Coalesce(Sum('annexe_fees'), Value(0))
        ).values('total')

        # Main query with annotations
        queryset = queryset.annotate(
            fees=Coalesce(Subquery(fees_subquery, output_field=IntegerField()), Value(0)),
            inscription=Coalesce(Subquery(inscription_subquery, output_field=IntegerField()), Value(0)),
            scolarite=Coalesce(Subquery(scolarite_subquery, output_field=IntegerField()), Value(0)),
            annexe=Coalesce(Subquery(annexe_subquery, output_field=IntegerField()), Value(0)),
            paid=F('inscription') + F('scolarite') + F('annexe'),
            remaining=F('fees') - F('paid')
        )
    else:
        # Payment type subqueries for regular payments
        inscription_regular = models.Payment.objects.filter(
            enrollment=OuterRef('pk'),
            payment_type=models.Payment.TYPE_INSCRIPTION
        ).values('enrollment').annotate(
            total=Coalesce(Sum('amount'), Value(0))
        ).values('total')

        scolarite_regular = models.Payment.objects.filter(
            enrollment=OuterRef('pk'),
            payment_type=models.Payment.TYPE_SCOLARITE
        ).values('enrollment').annotate(
            total=Coalesce(Sum('amount'), Value(0))
        ).values('total')

        annexe_regular = models.Payment.objects.filter(
            enrollment=OuterRef('pk'),
            payment_type=models.Payment.TYPE_ANNEXE
        ).values('enrollment').annotate(
            total=Coalesce(Sum('amount'), Value(0))
        ).values('total')

        # Legacy payment subqueries
        inscription_legacy = models.Payment.objects.filter(
            enrollment=OuterRef('pk'),
            payment_type__isnull=True
        ).values('enrollment').annotate(
            total=Coalesce(Sum('inscription'), Value(0))
        ).values('total')

        scolarite_legacy = models.Payment.objects.filter(
            enrollment=OuterRef('pk'),
            payment_type__isnull=True
        ).values('enrollment').annotate(
            total=Coalesce(Sum('amount'), Value(0))
        ).values('total')

        annexe_legacy = models.Payment.objects.filter(
            enrollment=OuterRef('pk'),
            payment_type__isnull=True
        ).values('enrollment').annotate(
            total=Coalesce(Sum('annexe'), Value(0))
        ).values('total')

        # Fees subquery
        fees_subquery = models.Enrollment.objects.filter(
            pk=OuterRef('pk')
        ).annotate(
            total=Coalesce(Sum('enrollment_fees'), Value(0)) +
                Coalesce(Sum('year_fees'), Value(0)) +
                Coalesce(Sum('annexe_fees'), Value(0))
        ).values('total')

        # Main query with annotations
        queryset = queryset.annotate(
            inscription_regular_amount=Coalesce(Subquery(
                inscription_regular, output_field=IntegerField()), Value(0)),
            scolarite_regular_amount=Coalesce(Subquery(
                scolarite_regular, output_field=IntegerField()), Value(0)),
            annexe_regular_amount=Coalesce(Subquery(
                annexe_regular, output_field=IntegerField()), Value(0)),
            
            inscription_legacy_amount=Coalesce(Subquery(
                inscription_legacy, output_field=IntegerField()), Value(0)),
            scolarite_legacy_amount=Coalesce(Subquery(
                scolarite_legacy, output_field=IntegerField()), Value(0)),
            annexe_legacy_amount=Coalesce(Subquery(
                annexe_legacy, output_field=IntegerField()), Value(0)),
            
            paid_inscription=F('inscription_regular_amount') + F('inscription_legacy_amount'),
            paid_scolarite=F('scolarite_regular_amount') + F('scolarite_legacy_amount'),
            paid_annexe=F('annexe_regular_amount') + F('annexe_legacy_amount'),
            
            paid=F('paid_inscription') + F('paid_scolarite') + F('paid_annexe'),
            fees=Coalesce(Subquery(fees_subquery, output_field=IntegerField()), Value(0)),
            remaining=F('fees') - F('paid')
        )

    queryset = queryset.order_by(
        'student__last_name', 
        'student__first_name', 
    )
    
    # Generate PDF document
    if not 'detailed' in request.GET:
        doc = reports.ClassPaymentsListPDF(orientation='P', unit='mm')
    else:
        doc = reports.ClassPaymentsListDetailedPDF(orientation='L', unit='mm')

    doc.set_margins(10, 10, 10)
    doc.add_page()
    doc.add_content(queryset=queryset, level=level, 
                    redden_girls_names=redden_names)
    return doc.get_file_response(f'Versements par classe {str(level)} [{user.id}]')


class LevelsListView(mixins.LoginRequiredMixin, 
                     BaseHTMXRequestsView, generic.ListView):
    model = models.Level
    template_name = 'partials/level/level_base.html'

    def get_queryset(self):
        user = self.request.user
        education = self.request.GET.get('education', 'fr')
        education_key = education[0].upper()
        queryset = models.Level.objects.for_user(
            user=user, education=education_key, year=self.get_year())
        related_field_name = 'enrollment'
        if education_key == main_utils.EDUCATION_ARABIC:
            related_field_name += '_ar'
        qs = queryset.annotate(
            students=Count(related_field_name, distinct=True),
            remaining=Case(
                When(max__gt=Count(related_field_name, distinct=True), 
                    then=F('max') - Count(related_field_name, distinct=True)),
                    default=Value(0), output_field=IntegerField()
                )
            ).order_by('generic_level', 'number')
        subschool_id = self.request.GET.get('subschool')
        if subschool_id:
            subschool = models.Subschool.objects.filter(id=subschool_id).first()
            
            if subschool and subschool.is_main_school:
                qs = qs.filter(Q(subschool=subschool) | Q(subschool__isnull=True))
            else:
                qs = qs.filter(subschool=subschool)
        return qs
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user = self.request.user
        url = reverse('school:levels')
        context['nav_items'] = navs.get_levels_navs(user=user, url=url)
        context['partial_template'] = 'partials/level/levels_list.html'
        education = self.request.GET.get('education', 'fr')
        context['active_nav'] = education
        context['page_title'] = 'Gestion des classes'
        context['main_school'] = models.Subschool.objects.filter(is_main_school=True).first()  
        subschools = self.request.user.school.subschool_set.all()
        subschools_count = subschools.count()
        if subschools_count >= 2:
            context['subschools'] = subschools
            context['subschools_count'] = subschools_count
        return context
    
    def handle_no_permission(self):
        return HttpResponse(status=403, headers={'HX-Trigger': 'permission_denied'})
    

class LevelCreateView(mixins.PermissionRequiredMixin, generic.CreateView):
    model = models.Level
    fields = ['education', 'generic_level', 'number', 'max', 'subschool']
    template_name = 'partials/level/level_form.html'
    permission_required = 'school.add_level'

    def get_form(self, form_class=None):
        form = super().get_form(form_class)
        if self.request.user.school.subschool_set.count() >= 2:
            form.fields['subschool'].queryset = self.request.user.school.subschool_set.all()
        return form

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        field = context['form'].fields['education']
        education = self.request.GET.get('education', 'fr')
        context['active_nav'] = education
        education = education[0].upper()
        if education == 'A':
            field.initial = main_utils.EDUCATION_ARABIC
            field.choices = ((main_utils.EDUCATION_ARABIC, 'Arabe'),)
        else:
            field.choices = ((main_utils.EDUCATION_FRENCH, 'Français'),)
        return context
    
    def form_valid(self, form):
        level = form.save(False)
        level.year = school_utils.get_current_year()
        level.school = self.request.user.school
        level.save()

        if level.school.education == main_utils.EDUCATION_ARABIC:
            qs = models.Level.objects.filter(
                number=level.number, 
                education=main_utils.get_other_education(level.education),
                year=level.year, school=level.school,
                generic_level=level.generic_level)
            
            if not qs.exists():
                models.Level.objects.create(
                number=level.number, 
                education=main_utils.get_other_education(level.education),
                year=level.year, school=level.school,
                max=level.max, generic_level=level.generic_level)

        return HttpResponse(status=204, headers={'HX-Trigger': 'saved'})

    def handle_no_permission(self):
        return HttpResponse(status=403, headers={'HX-Trigger': 'permission_denied'})
        

class LevelUpdateView(mixins.PermissionRequiredMixin, generic.UpdateView):
    model = models.Level
    fields = ['number', 'max']
    template_name = 'partials/level/level_form.html'
    permission_required = 'school.add_level'
    
    def form_valid(self, form):
        form.save(True)
        return HttpResponse(status=204, headers={'HX-Trigger': 'saved'})
    
    def get_queryset(self):
        user = self.request.user
        year = get_session_year(self.request)
        return models.Level.objects.for_user(
            user, year=year, with_education=False)
    
    def handle_no_permission(self):
        return HttpResponse(status=403, headers={'HX-Trigger': 'permission_denied'})
    

class ActiveStudentsListView(StudentsListViewBase):
    template_name = 'partials/active_students/students_base.html'
    model = models.Enrollment
    context_object_name = 'enrollments'
    permission_required = 'school.view_student'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['partial_template'] = 'partials/active_students/students_list.html'
        url = reverse('school:active_students')
        user = self.request.user
        education = self.request.GET.get('education')
        
        active_nav = 'all'
        
        # page = self.request.GET.get('page') or ''
        filter_form = forms.StudentsFilterForm(
            data=self.request.GET or None, 
            statut=active_nav, user=self.request.user,
            year=self.get_year(),
            generic_level_ar=self.request.GET.get('generic_level_ar'),
            generic_level_fr=self.request.GET.get('generic_level_fr'))
        context['nav_items'] = navs.get_active_students_navs(
            user, url, reverse('school:import'),
            export_url=reverse('school:custom_export'))
        context['active_nav'] = active_nav
        context['education'] = education
        context['PLAN_LEVEL'] = models.Subscription.PLAN_LEVEL
        context['filter_form'] = filter_form
        context['EDUCATION_ARABIC'] = main_utils.EDUCATION_ARABIC
        # context['page'] = page

        if self.request.GET.get('search'):
            context['search'] = self.request.GET.get('search')
        
        context['title'] = 'Gestion des inscrits'
        context['subtitle'] = 'Liste des élèves inscrits'
        context['icon'] = 'users'

        if self.filter_applied:
            context['result_found'] = context['page_obj'].paginator.count

        return context
    
    def get_queryset(self):
        return self.sort(self.request.GET, super().get_queryset(), [
            'student__last_name', 'student__first_name'
        ])

    def handle_no_permission(self):
        return HttpResponse(status=403, headers={'HX-Trigger': 'permission_denied'})


class ActiveStudentsLevelListView(
    mixins.LoginRequiredMixin,
    BaseHTMXRequestsView, generic.ListView):
    template_name = 'partials/active_students/students_base.html'
    model = models.Level
    context_object_name = 'levels'

    def get_queryset(self):
        user = self.request.user
        education = self.request.GET.get('education').upper()[0]
        related_field_name = 'enrollment'
        if education == main_utils.EDUCATION_ARABIC:
            related_field_name += '_ar'
        return models.Level.objects.for_user(
            user=user, education=education, year=self.get_year()
        ).annotate(students=Count(related_field_name, distinct=True))
    
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['partial_template'] = 'partials/active_students/level_list.html'
        education = self.request.GET.get('education')
        context['education'] = education
        context['active_nav'] = education
        url = reverse('school:students_level_list')
        context['nav_items'] = navs.get_levels_navs(
            user=self.request.user, url=url
        )
        context['title'] = 'Listes de classe'
        context['subtitle'] = 'Impression des listes de classe'
        context['icon'] = 'file-text'
        return context


class ActiveStudentsLevelStatisticsView(
    mixins.LoginRequiredMixin,
    BaseHTMXRequestsView, generic.ListView):
    template_name = 'partials/active_students/students_base.html'
    model = models.Level
    context_object_name = 'levels'

    def get_queryset(self):
        user = self.request.user
        education = self.request.GET.get('education').upper()[0]
        related_field_name = 'enrollment'
        if education == main_utils.EDUCATION_ARABIC:
            related_field_name += '_ar'
        queryset = models.Level.objects.for_user(
            user=user, education=education, year=self.get_year()
        )

        cycle = self.request.GET.get('cycle_filter')
        if cycle:
            queryset = queryset.filter(generic_level__cycle=cycle)

        if education == main_utils.EDUCATION_FRENCH:
            queryset = queryset.annotate(
                boys=Count(
                    related_field_name, 
                    filter=Q(enrollment__student__gender=main_utils.GENDER_MALE), 
                    distinct=True),
                girls=Count(
                    related_field_name, 
                    filter=Q(enrollment__student__gender=main_utils.GENDER_FEMALE), 
                    distinct=True),
            )
        else:
            queryset = queryset.annotate(
                boys=Count(
                    related_field_name, 
                    filter=Q(enrollment_ar__student__gender=main_utils.GENDER_MALE), 
                    distinct=True),
                girls=Count(
                    related_field_name, 
                    filter=Q(enrollment_ar__student__gender=main_utils.GENDER_FEMALE), 
                    distinct=True),
            )
        return queryset.annotate(
            students=F('boys') + F('girls')
        ).order_by('generic_level__order', 'number')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['partial_template'] = 'partials/active_students/level_statistics.html'
        education = self.request.GET.get('education')
        context['education'] = education
        context['active_nav'] = education
        url = reverse('school:levels_statistics')
        context['nav_items'] = navs.get_levels_stats_navs(
            user=self.request.user, url=url
        )
        aggregate_result = self.get_queryset().aggregate(
            total_boys=Sum(F('boys')) or 0,
            total_girls=Sum(F('girls')) or 0,
            total_students=Sum(F('students')) or 0,
        )
        context['aggregate_result'] = aggregate_result
        context['title'] = 'Effectifs des classes'
        context['subtitle'] = 'Effectifs des classes par sexe'
        context['icon'] = 'file-text'
        context['CYCLE_PRIMARY'] = main_utils.CYCLE_PRIMARY
        context['CYCLE_SECONDARY'] = main_utils.CYCLE_SECONDARY
        context['CYCLE_BOTH'] = main_utils.CYCLE_BOTH
        return context


def levels_export_view(request):
    checked_list = main_utils.get_checked_items_list(request)
    user = request.user
    year = get_session_year(request)
    
    cycle = request.GET.get('cycle_filter') or ''
    if cycle and cycle == main_utils.CYCLE_PRIMARY:
        cycle = ' AU PRIMAIRE'
    elif cycle:
        cycle = ' AU SECONDAIRE'

    education = main_utils.EDUCATION_FRENCH
    if checked_list:
        first_level = models.Level.objects.filter(pk=checked_list[0]).first()
        education = first_level.education

    resource = LevelResource()
    queryset = resource.get_queryset(
        user=user, year=year, levels_checked=checked_list, education=education)

    file_name = f'EFFECTIFS PAR GENRE{cycle}- ECOLEPRO'
    if request.GET.get('action') == 'excel_export':
        dataset = resource.export(queryset=queryset)
        response = HttpResponse(dataset.xlsx, content_type='application/vnd.ms-excel')
        response['Content-Disposition'] = f"attachment; filename={file_name}.xlsx"
        return response
    
    document = reports.LevelStatsReport()
    document.add_content(queryset=queryset, user=request.user, cycle=cycle)
    return document.get_file_response(filename=file_name)


@decorators.permission_required('users.can_import_students')
def import_view(request):
    template_name = 'partials/active_students/students_base.html'
    partial_template = 'partials/import/students_import.html'
    is_htmx = bool(request.htmx)
    url = reverse('school:import')
    nav_items = navs.get_active_students_navs(
        request.user, reverse('school:active_students'), 
        reverse('school:import'), export_url=reverse('school:custom_export'), 
        show_list=not 'nolist' in request.GET)
    form = forms.ImportForm(request.POST or None, files=request.FILES or None)
    aggregated = models.Enrollment.objects.for_user(user=request.user, year=get_session_year(request)).aggregate(
        boys=Count('student__gender', filter=Q(student__gender=main_utils.GENDER_MALE)),
        girls=Count('student__gender', filter=Q(student__gender=main_utils.GENDER_FEMALE)),
    )
    context = {
        'active_nav': 'import', 
        'nav_items': nav_items,
        'partial_template': partial_template,
        'template_name': template_name,
        'form': form,
        'required_cols': school_utils.REQUIRED_COLS,
        'all_cols': school_utils.STUDENTS_IMPORT_COLS,
        'title' : 'Importations',
        'subtitle' : "Importer des élèves à partir d'un fichier Excel",
        'icon' : 'corner-right-up',
        'page_title': 'Importations et exportation des élèves',
        'result_boys': aggregated.get('boys', 0),
        'result_girls': aggregated.get('girls', 0),
    }

    if request.method == 'POST':
        if form.is_valid():
            uploaded_file = request.FILES['file']
            active = form.cleaned_data['active']
            print('Importation des élèves en cours', str(request.user.school or ''))
            return import_students(uploaded_file, request.user, request, active)
            
    template_name = template_name if is_htmx else 'full_template.html'
    return render(request, template_name, context)

def import_student(student_data, students_dict, year, school):
    student_id = student_data['student_id']
    enrollment = None
    student = None
    if student_id in students_dict:
        # Update enrollment obj
        enrollment = students_dict[student_id]
        student = enrollment.student
        if not enrollment.has_level_fr:
            enrollment.generic_level_fr = student_data['generic_level_fr']
        if not enrollment.has_level_ar and student_data.get('generic_level_ar'):
            enrollment.generic_level_fr = student_data['generic_level_ar']
        if 'status' in student_data:
            enrollment.status = student_data['status']
        if 'qualite' in student_data:
            enrollment.qualite = student_data['qualite']

        # Update student obj
        student.last_name = student_data['last_name']
        student.first_name = student_data['first_name']
        student.gender = student_data['gender']
        student.birth_day = student_data['birth_day'] or None
        student.birth_month = student_data['birth_month'] or None
        student.birth_year = student_data['birth_year'] or None
        student.birth_place = student_data['birth_place'] or None

        if 'full_name_ar' in student_data:
            student.full_name_ar = student_data['full_name_ar']
        if 'location_ar' in student_data:
            student.location_ar = student_data['location_ar']
        if 'birth_place_ar' in student_data:
            student.birth_place_ar = student_data['birth_place_ar']
        if 'father' in student_data:
            student.father = student_data['father']
        if 'mother' in student_data:
            student.mother = student_data['mother']
        if 'father_phone' in student_data:
            student.father_phone = student_data['father_phone']
        print('Enrollment and student exist', enrollment, enrollment.pk, student, student.pk)
    else:
        # Create
        enrollment_fields = [
            'generic_level_fr',
            'status',
            'qualite',
        ]
        if 'generic_level_ar' in student_data:
            enrollment_fields.append('generic_level_ar')
        
        student_only_data = {key : value for key, value in student_data.items() if not key in enrollment_fields}
        enrollment_only_data = {key : value for key, value in student_data.items() if key in enrollment_fields}
        student = None
        student_exists = False
        if student_id:
            qs = models.Student.objects.filter(student_id=student_id, school=school)
            if qs.exists():
                student = qs.first()
                student_exists = True
                print('Student object exists', student, student_id)
                qs.update(**student_only_data)
        
        if not student_id or not student_exists:
            print('Student does not exist at all', student_id, student_data['last_name'], student_data['first_name'])
            student = models.Student(school=school, origin=year, **student_only_data)
            if not student_id:
                student.student_id = None
            student.save()
        enrollment = models.Enrollment(year=year, school=school, student=student, **enrollment_only_data)
    return enrollment, student, (student_id in students_dict)

def import_students(uploaded_file, user, request, active_status=False):
    with uploaded_file.open(mode='rb') as file:
        dataset = Dataset().load(file, format=main_utils.infer_format(uploaded_file.name))
        ids = dataset['matricule']
        for index, id in enumerate(ids):
            if id:
                ids[index] = id.strip()
        errors = []

        year = get_session_year(request)
        
        # Check columns headers
        headers_are_correct = True
        headers = dataset.headers
        students_to_update = []
        enrollments_to_update = []
        students_to_create = []
        enrollments_to_create = []

        for header in school_utils.REQUIRED_COLS:
            if not header in headers:
                headers_are_correct = False
                break
        
        if not headers_are_correct:
            sweetify.error(
                request, title="Entête de fichier incorrecte", 
                text=f"Veuillez vérifier l'en-tête du fichier à importer: {dataset.headers}",
                persistent=True
            )
            return HttpResponseRedirect(reverse('school:import'))
        
        # Retrieve and store existing objects
        queryset = models.Enrollment.objects.for_user(user=user, year=year) \
            .filter(Q(student__identifier__in=ids) | Q(student__student_id__in=ids)) \
            .select_related('student') \
            .annotate(
                has_level_fr=Q(level_fr__isnull=False),
                has_level_ar=Q(level_ar__isnull=False),
            )
        students_dict = {}
        student_id = ''
        for enrollment in queryset:
            student_id = enrollment.student.student_id or enrollment.student.identifier
            students_dict[student_id] = enrollment
        
        # Add levels to dict
        generic_levels = models.GenericLevel.objects.for_school(user, year)
        levels_dict = {}
        for level in generic_levels:
            levels_dict[level.short_name] = level

        imported_count = 0

        # Iterate through and populate dataset
        for i, row in enumerate(dataset):
            line_has_errors = False

            student = None
            matricule = row[dataset.headers.index('matricule')]
            if matricule:
                matricule = str(matricule).upper().strip()
            student_data = {
              'student_id': matricule if len(matricule) <= 9 else '',
              'last_name': row[dataset.headers.index('nom')],
              'first_name': row[dataset.headers.index('prenoms')],
              'gender': row[dataset.headers.index('sexe')],
              'birth_day': row[dataset.headers.index('jour')],
              'birth_month': row[dataset.headers.index('mois')],
              'birth_year': row[dataset.headers.index('annee')],
              'birth_place': row[dataset.headers.index('localite')],
              'generic_level_fr': levels_dict.get(row[dataset.headers.index('niveau')]),
            }
            if 'localite_ar' in dataset.headers: 
                student_data['birth_place_ar'] = row[dataset.headers.index('localite_ar')]
            if 'nom_complet_ar' in dataset.headers: 
                student_data['full_name_ar'] = row[dataset.headers.index('nom_complet_ar')]
            if 'pere' in dataset.headers: 
                student_data['father'] = row[dataset.headers.index('pere')]
            if 'mere' in dataset.headers: 
                student_data['mother'] = row[dataset.headers.index('mere')]
            if 'contact' in dataset.headers: 
                student_data['father_phone'] = row[dataset.headers.index('contact')]
            if 'niveau_ar' in dataset.headers: 
                student_data['generic_level_ar'] = levels_dict.get(row[dataset.headers.index('niveau_ar')])
            if 'statut' in dataset.headers: 
                student_data['status'] = levels_dict.get(row[dataset.headers.index('statut')])
            if 'qualite' in dataset.headers: 
                student_data['qualite'] = levels_dict.get(row[dataset.headers.index('qualite')])
            
            if student_id in students_dict:
                pass
            
            # Check if data are valid
            student_name = row[dataset.headers.index('nom')] + ' ' + row[dataset.headers.index('prenoms')] 
            if not row[dataset.headers.index('niveau')] in levels_dict:
                errors.append(f'Ligne {i + 1}: {student_name}: Niveau incorrecte;')
                line_has_errors = True
            
            if not student_data['last_name']:
                errors.append(f'Ligne {i + 1}: {student_name}: Le champ *nom ne peut être vide;')
                line_has_errors = True
            
            if not student_data['first_name']:
                errors.append(f'Ligne {i + 1}: {student_name}: Le champ *prenoms ne peut être vide;')
                line_has_errors = True
            
            if not student_data['gender'] == main_utils.GENDER_MALE and not student_data['gender'] == main_utils.GENDER_FEMALE:
                line_has_errors = True
                errors.append(f"Ligne {i + 1}: {student_name}: La valeur du champ *sexe doit être F ou M. Actuellement: {student_data['gender']};")

            if student_data['birth_year'] and str(student_data['birth_year']).isnumeric() and \
               student_data['birth_month'] and str(student_data['birth_month']).isnumeric() and \
               student_data['birth_day'] and str(student_data['birth_day']).isnumeric():
                try:
                    birth_date = datetime(
                        int(student_data['birth_year']), 
                        int(student_data['birth_month']), 
                        int(student_data['birth_day'])
                    )
                except:
                    errors.append(
                        f"Ligne {i + 1}: {student_name}: Date de naissance incorrecte: {int(student_data['birth_day'])}/{int(student_data['birth_month'])}/{int(student_data['birth_year'])};")
                    line_has_errors = True
                    continue
        
            if line_has_errors:
                continue
            else:
                # Update data
                enrollment_obj, student_obj, enrollment_exists = import_student(student_data, students_dict, year, user.school)
                if enrollment_exists:
                    enrollments_to_update.append(enrollment_obj)
                    students_to_update.append(student_obj)
                else:
                    student = student_obj
                    student.save()
                    enrollment_obj.student = student
                    enrollments_to_create.append(enrollment_obj)
                imported_count += 1

        models.Enrollment.objects.bulk_create(enrollments_to_create)
        models.Student.objects.bulk_update(
            students_to_update, fields=[
                'last_name', 'first_name', 'gender',
                'birth_day', 'birth_month', 'birth_year',
                'birth_place', 'full_name_ar', 'birth_place_ar',
                'father', 'mother', 'father_phone'
            ]
        )
        models.Enrollment.objects.bulk_update(
            enrollments_to_update, 
            fields=['generic_level_fr', 'generic_level_ar', 'status', 'qualite']
        )

        if errors:
            sweetify.info(
                request, f"Importation terminée. {imported_count} / {len(dataset)} lignes importées avec succès:", 
                text=f"Quelques erreurs ont été détectées: {errors}", persistent=True)
            return HttpResponseRedirect(reverse('school:import'))
        
        sweetify.success(
            request, title='Importation terminée', 
            text='Toutes les lignes ont été importées avec succès',
            persistent=True)
        print('Importation terminée')
        return HttpResponseRedirect(reverse('school:import'))
        
@decorators.login_required()
def download_student_import_model(request):
    file_path = os.path.join('static', 'docs')
    if not os.path.exists(file_path):
        os.makedirs(file_path)
  
    filename = ''
    filename = 'modele_importation_ecolepro.xls'
    if request.user.school.education == main_utils.EDUCATION_ARABIC:
        filename = 'modele_eleve_ecolepro_ar.xls'
    filename = os.path.join(file_path, f'{filename}')

    content_type = 'application/vnd.openxmlformats-officedocument.' +\
        'spreadsheetml.sheet'
    response = FileResponse(open(filename, 'rb'))
    response['Content-Disposition'] = \
        f'attachment; filename="importation_eleve_ecolepro.xls"'
    return response


@decorators.login_required()
def students_list_pdf(request, level_id):
    # Get school and user
    user = request.user
    year = get_session_year(request)

    level = models.Level.objects.filter(id=level_id).first()
    redden_names = 'filles_en_rouge' in request.GET
    queryset = models.Enrollment.objects.for_user_minimum(user, year=year) \
        .filter(Q(level_fr=level) | Q(level_ar=level))
        

    if level.education == main_utils.EDUCATION_FRENCH:
        queryset = queryset.order_by(
            'student__last_name', 
            'student__first_name', 
        )
    else:
        queryset = queryset.order_by('student__full_name_ar')
    
    # Generate PDF document
    doc = reports.ClassListPDF(orientation='P', unit='mm')
    doc.set_margins(10, 10, 10)
    doc.add_page()
    doc.add_content(queryset=queryset, level=level, 
                    redden_girls_names=redden_names,
                    show_ids='avec_identifiants' in request.GET)

    return doc.get_file_response(f'Liste de classe {str(level)} [{user.id}]')


@decorators.login_required()
def level_attribution_view(request):
    if request.method == 'POST':
        ids = main_utils.get_checked_items_list(request)
        
        year = get_session_year(request)
        level_id = request.POST.get('level_to_attribute')
        education = str(request.POST.get('education')).upper()[0]
        level = models.Level.objects.for_user(
            request.user, year=year, education=education) \
            .get(id=level_id)
        
        if not level:
            level_id = None
        queryset = models.Enrollment.objects.for_user(request.user, year=year) \
            .filter(id__in=ids) \
        
        if queryset.exists():
            if education == main_utils.EDUCATION_FRENCH:
                queryset.update(level_fr_id=level_id)
            else:
                queryset.update(level_ar_id=level_id)
        return HttpResponse(status=204, headers={'HX-Trigger': 'saved'})
    
    template_name = 'partials/active_students/students_base.html'
    partial_template = 'partials/active_students/students_list.html'
    education = request.GET.get('education')
    user = request.user

    selected_levels = None
    generic_level_id = request.GET.get('generic_level')
    year = get_session_year(request)
    if generic_level_id:
        selected_levels = models.Level.objects.for_user(
            user, year=year, education=str(education.upper())[0])\
            .filter(generic_level__id=generic_level_id)
    url = reverse('school:students_level_attribution')
    context = {
        'template_name': template_name,
        'partial_template': partial_template,
        'nav_items': navs.level_attribution_navs(user=user, url=url),
        'active_nav': education,
        'generic_levels': models.GenericLevel.objects.only('id', 'short_name'),
        'selected_levels': selected_levels,
        'education': education,
        'title': 'Attribution des classes',
        'subtitle': 'Attribuer des classes aux élèves',
    }

    if generic_level_id:
        context['selected_level'] = int(generic_level_id)
        queryset = models.Enrollment.objects.for_user_minimum(
            user=user, year=year
        ).filter(active=True)
        
        if str(education).upper()[0] == main_utils.EDUCATION_FRENCH: 
            queryset = queryset.filter(
                Q(generic_level_fr__id=generic_level_id))
        else:
            queryset = queryset.filter(
                Q(generic_level_ar__id=generic_level_id))
        context['enrollments'] = queryset

    if bool(request.htmx):
        return render(request, template_name, context)
    return render(request, 'full_template.html', context)


class LevelAttributionView(StudentsListViewBase):
    permission_required = 'school.add_level'
    template_name = 'partials/active_students/students_base.html'
    model = models.Enrollment
    context_object_name = 'enrollments'

    def get_paginate_by(self, queryset):
        return self.request.GET.get('per_page') or 50

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        level_navs = navs.level_attribution_navs(
            user=self.request.user, url=reverse('school:students_level_attribution'))
        context['nav_items'] = level_navs
        context['active_nav'] = self.request.GET.get('education')
        context['partial_template'] = 'partials/active_students/level_attribution.html'
        context['hide_add_btn'] = True
        context['title'] = 'Attribution de classes'
        context['subtitle'] = 'Attribuer des classes aux élèves plus rapidement'
        context['hide_stats'] = True
        # Filtering
        context['filter_form']  = forms.StudentsFilterForm(
            data=self.request.GET or None, 
            statut=self.request.GET.get('statut') or '', user=self.request.user,
            year=self.get_year(),
            generic_level_ar=self.request.GET.get('generic_level_ar'),
            generic_level_fr=self.request.GET.get('generic_level_fr'))
        
        form = context['filter_form']
        if self.request.GET.get('generic_level_fr'):
            context['generic_level_fr_filter'] = models.GenericLevel.objects.get(
                pk=self.request.GET.get('generic_level_fr')).short_name
        if self.request.GET.get('generic_level_ar'):
            context['generic_level_ar_filter'] = models.GenericLevel.objects.get(
                pk=self.request.GET.get('generic_level_ar')).short_name

        if self.filter_applied:
            context['result_found'] = context['page_obj'].paginator.count

        education_levels = models.Level.objects.for_user(
            self.request.user, self.request.user.school, self.get_year(),
            str(self.request.GET.get('education'))[0].upper())
        context['education_levels'] = education_levels
        context['subschools'] = list(self.request.user.school.subschool_set.all())
        return context
    
    def post(self, request, *args, **kwargs):
        ids = main_utils.get_checked_items_list(request)
        year = get_session_year(request)
        level_id = request.POST.get('action')
        level = models.Level.objects.get(id=level_id, school=request.user.school)
        education = level.education
        qs = models.Enrollment.objects.for_user(
            user=request.user, year=year).filter(pk__in=ids)
        
        if education == main_utils.EDUCATION_FRENCH:
            qs = qs.filter(generic_level_fr=level.generic_level)
            qs.update(level_fr=level)
        else:
            qs = qs.filter(generic_level_ar=level.generic_level)
            qs.update(level_ar=level)

        print('Level updated!')
        education = 'ar' if education == main_utils.EDUCATION_ARABIC else 'fr' 
        return HttpResponseRedirect(f"{reverse('school:students_level_attribution')}?education={education}")


def photos_view(request):
    template_name = 'partials/active_students/students_base.html'
    partial_template = 'partials/active_students/students_photos.html'
    education = request.GET.get('education')
    user = request.user
    url = reverse('school:students_photos')
    level = request.GET.get('level')
    year = get_session_year(request)

    context = {
        'template_name': template_name,
        'partial_template': partial_template,
        'nav_items': navs.students_photos_navs(user=user, url=url),
        'active_nav': education or '',
        'generic_levels': models.GenericLevel.objects.for_school(user, year).only('id', 'short_name'),
        'education': education,
        'short_name': request.GET.get('short_name'),
        'level': request.GET.get('level'),
        'title': 'Gestion des photos',
        'subtitle': 'Prises ou modifications des photos des élèves',
        'icon': 'camera'
    }

    if level:
        queryset = models.Enrollment.objects.for_user(
            user=user, year=year
        ).filter(Q(level_ar__id=level) | Q(level_fr__id=level))\
         .filter(active=True) \
         .select_related( \
            'student', 'level_fr', 'generic_level_fr', 
            'generic_level_ar', 'level_ar', 
            'level_fr__generic_level', 'level_ar__generic_level')
        
        if str(education).upper()[0] == main_utils.EDUCATION_FRENCH:
            queryset = queryset.order_by('student__last_name', 'student__first_name')
        else:
            queryset = queryset.order_by('student__full_name_ar')
        context['enrollments'] = queryset
    elif 'recents' in request.GET:
        context['enrollments'] =  models.Enrollment.objects.for_user(user=user, year=year).order_by('-updated_at', '-created_at')[:10]
        context['show_latest'] =  True
    if bool(request.htmx):
        if level:
            template_name = 'partials/active_students/photos_table.html'
        return render(request, template_name, context)
    return render(request, 'full_template.html', context)


def photo_import_view(request, student_id):
    form = forms.PhotoInputForm(request.POST or None, files=request.FILES or None)

    if request.method == 'POST':
        if form.is_valid():
            file = form.cleaned_data['photo']
            student = models.Student.objects.filter(id=student_id).first()
            student.photo = file
            student.save(update_fields=['photo'])
            return HttpResponse(status=204, headers={'HX-Trigger': 'saved'})
    
    context = {
            'student_id': student_id, 
            'form': form, 
            'url': reverse('school:photo_import', args=[student_id]),
        }
    return render(
        request, 'partials/active_students/photo_form.html', 
        context
    )


def sublevels_view(request):
    year = get_session_year(request)
    level = request.GET.get('short_name') or 0
    generic_level = models.GenericLevel.objects.get(
        id=(level or 0)
    )
    lang = request.GET.get('lang') or request.GET.get('education')
    lang = lang.upper()[0]

    levels = models.Level.objects.for_user(
        request.user, request.user.school, year,lang)\
        .filter(generic_level=generic_level)
    # subjects = models.LevelSubject.objects.for_school(
    #     request.user, generic_level.short_name, education=lang,
    #     year=year)
    
    # terms = models.SchoolTerm.objects.for_year(
    #     year, request.user, generic_level.cycle, lang)\
    #     .filter(active=True)
    form = forms.LevelForm(levels)
    context = {'form': form}
    return render(request, 'partials/level/level_input_form.html', context)


def get_photo_url(request, student_id):
    student = models.Student.objects.get(id=student_id)
    url = ''
    if student.photo:
        url = student.photo.url
    else:
        url = f'{ student.blank_photo() }'
    html = f"""
    <img data-original="{ url }"
        src="{ url }" 
        alt="1" 
        class="lazy border img-thumbnail rounded-circle">
    """
    return HttpResponse(html)


class BalanceNavView(BaseHTMXRequestsView, generic.TemplateView):
    template_name = 'partials/navs_base.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        list_type = self.request.GET.get('type')
        partial_template ='partials/balance/balance.html'
        title = 'Etat de la Caisse et Dépenses'
        subtitle = "Solde de la caisse et suivi des dépenses de " \
                   "l'année scolaire"
        payments_total = 0
        expenses = 0
        remaining = 0
        if list_type == 'depenses':
            partial_template ='partials/balance/expense_list.html'
        else:
            payments_total = models.Payment.objects.for_user(
                year=self.get_year(), user=self.request.user)\
                .aggregate(total=Sum('amount') + Sum('inscription') + Sum('annexe'))['total'] or 0
            expenses = models.Expense.objects.for_year(
                year=self.get_year(), user=self.request.user)\
                .aggregate(total=Sum('amount'))['total'] or 0
            remaining = payments_total - expenses
            context['total'] = payments_total
            context['expenses'] = expenses
            context['remaining'] = remaining

        context['partial_template'] = partial_template 
        context['nav_items'] = navs.get_balance_nav(
            reverse('school:balance'), 
            reverse('school:expenses'))
        context['active_nav'] = list_type
        context['type'] = list_type
        context['title'] = title
        context['icon'] = 'dollar-sign'
        context['subtitle'] = subtitle
        return context


class TeachersListView(mixins.PermissionRequiredMixin, 
                       BaseHTMXRequestsView, generic.ListView):
    model = models.Payment
    template_name = 'partials/navs_base.html'
    context_object_name = 'teachers'
    permission_required = 'school.view_teacher'

    def get_queryset(self):
        user = self.request.user
        lang = self.request.GET.get('lang', main_utils.EDUCATION_FRENCH)
        lang = str(lang)[0]
        return models.Teacher.objects.for_school(
            school=user.school, 
            education=lang
        ).prefetch_related(
            'teacherlevel2_set__subjects__subject',
            'teacherlevel2_set__level')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        lang = self.request.GET.get('lang', main_utils.EDUCATION_FRENCH)
        lang = str(lang)[0]
        context['active_nav'] = lang
        context['nav_items'] = navs.get_teachers_nav(
            self.request.user, reverse('school:teachers'))
        context['title'] = 'Enseignants'
        context['lang'] = lang
        context['subtitle'] = "Gestion des enseignants"
        context['icon'] = "users"
        context['partial_template'] = 'partials/teacher/teacher_list.html'
        context['APP_NAME'] = main_utils.APP_NAME
        return context
    
    def handle_no_permission(self):
        return HttpResponse(status=403, headers={'HX-Trigger': 'permission_denied'})


class TeacherCreateView(mixins.PermissionRequiredMixin, generic.CreateView):
    model = models.Teacher
    fields = [
            'last_name', 'first_name', 'gender',
            'birth_day', 'birth_month', 'birth_year',
            'id_number', 'phone', 'education'
        ]
    permission_required = 'school.add_teacher'
    template_name = 'partials/teacher/teacher_form.html'

    def form_valid(self, form):
        year = get_session_year(self.request)
        form = form.save(commit=False)
        form.school = self.request.user.school
        form.origin = year
        form.save()
        return HttpResponse(status=204, headers={'HX-Trigger': 'saved'})
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        education = main_utils.get_education(self.request)

        context['form'].fields['education'].choices = (
            (education, 'Français' if education == main_utils.EDUCATION_FRENCH else 'Arabe'),
        )
        return context


class TeacherUpdateView(mixins.PermissionRequiredMixin, generic.UpdateView):
    model = models.Teacher
    fields = [
            'last_name', 'first_name', 'gender',
            'birth_day', 'birth_month', 'birth_year',
            'id_number', 'phone'
        ]
    permission_required = 'school.add_teacher'
    template_name = 'partials/teacher/teacher_form.html'

    def form_valid(self, form):
        form.save(commit=True)
        return HttpResponse(status=204, headers={'HX-Trigger': 'saved'})


class TeacherLevelCreateView(mixins.PermissionRequiredMixin, generic.CreateView):
    model = models.TeacherLevel2
    fields = ['teacher', 'level', 'is_main_teacher', 'subjects']
    permission_required = 'school.add_teacher'
    template_name = 'partials/teacher/teacher_level_form.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        teacher_id = self.kwargs.get('pk') 
        qs = models.Teacher.objects.for_school(
            school=self.request.user.school
        ).filter(id=teacher_id)
        context['form'].fields['teacher'].queryset = qs
        context['form'].fields['teacher'].initial = qs.first()
        context['form'].fields['subjects'].queryset = LevelSubject.objects.none() 
        year = get_session_year(self.request)
        context['form'].fields['level'].queryset = models.Level.objects.for_user(
            self.request.user, year=year, education=qs.first().education
        )
        return context
    
    def form_valid(self, form):
        form.save(True)
        return HttpResponse(status=204, headers={'HX-Trigger': 'saved'})

    def form_invalid(self, form):
        return super().form_invalid(form)


class TeacherLevelUpdateView(mixins.PermissionRequiredMixin, generic.UpdateView):
    model = models.TeacherLevel2
    fields = ['teacher', 'level', 'is_main_teacher', 'subjects']
    permission_required = 'school.add_teacher'
    template_name = 'partials/teacher/teacher_level_form.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        obj = self.get_object()

        teacher_id = obj.teacher.id
        qs = models.Teacher.objects.filter(id=teacher_id)
        context['form'].fields['teacher'].queryset = qs
        context['form'].fields['teacher'].initial = obj.teacher
        level = models.Level.objects.get(
            school=self.request.user.school, id=obj.level.id)
        context['form'].fields['subjects'].queryset = \
            LevelSubject.objects.filter(
                school=level.school,
                subject__education=level.education,
                year=level.year,
                level=level.generic_level) 
        year = get_session_year(self.request)
        context['form'].fields['level'].queryset = models.Level.objects.for_user(
            self.request.user, year=year, education=level.education
        )
        return context
    
    def form_valid(self, form):
        form.save(True)
        return HttpResponse(status=204, headers={'HX-Trigger': 'saved'})

@decorators.permission_required('school.add_teacher')
def level_subjects_view(request):
    level_id = request.GET.get('level') or 0
    level = models.Level.objects.filter(
        school=request.user.school
    ).filter(id=level_id).first()
    form = forms.LevelSubjectsForm(request.user, level)
    context = {'form': form}
    return render(request, 'partials/level/level_subject_form.html', 
                  context)


class TeacherLevelDeleteView(mixins.PermissionRequiredMixin, generic.DeleteView):
    model = models.TeacherLevel2
    template_name = 'partials/confirm.html'
    permission_required = 'school.add_teacher'
    
    def form_valid(self, form):
        self.get_object().delete()
        return HttpResponse(status=204, headers={'HX-Trigger': 'saved'})


@decorators.permission_required('school.view_student')
def students_export_view(request):
    year = get_session_year(request)
    lang = request.GET.get('lang', main_utils.EDUCATION_FRENCH)
    lang_code = 'ar' if lang == main_utils.EDUCATION_ARABIC else 'fr'
    queryset = models.Enrollment.objects.for_user(
        user=request.user, year=year
    ).filter(active=True).select_related('student', 'level_ar__generic_level', 
                     'level_fr__generic_level')\
    .order_by(
        f'level_{lang_code}__generic_level__order', 
        'student__last_name', 'student__first_name'
    )
    
    workbook = openpyxl.Workbook()
    ws = workbook.active
    
    header = [
        'matricule', 'nom', 'sexe', 'niveau', 'classe', 'jour', 'mois', 'annee',
        'date_naissance', 'contact',
        'pere', 'mere'
    ]

    ws.column_dimensions['A'].width = 15
    ws.column_dimensions['B'].width = 40
    ws.column_dimensions['C'].width = 10
    ws.column_dimensions['D'].width = 15
    ws.column_dimensions['E'].width = 15
    ws.column_dimensions['F'].width = 10
    ws.column_dimensions['G'].width = 10
    ws.column_dimensions['H'].width = 10
    ws.column_dimensions['I'].width = 20
    ws.column_dimensions['J'].width = 15
    ws.column_dimensions['K'].width = 25
    ws.column_dimensions['L'].width = 25

    ws.append(header)
    for enrollment in queryset:
        student_id = enrollment.student.student_id
        item = [
            student_id if student_id and not '-' in student_id else enrollment.student.identifier,
        ]
        if lang == main_utils.EDUCATION_FRENCH:
            item.append(enrollment.student.get_full_name())
        else:
            item.append(str(enrollment.student.full_name_ar or \
                            enrollment.student.get_full_name()))

        item.append(enrollment.student.gender)

        if lang == main_utils.EDUCATION_FRENCH:
            item.append(str(enrollment.generic_level_fr))
            item.append(str(enrollment.level_fr))
        else:
            item.append(str(enrollment.generic_level_ar))
            item.append(str(enrollment.level_ar))

        item.append(enrollment.student.birth_day)
        item.append(enrollment.student.birth_month)
        item.append(enrollment.student.birth_year)
        item.append(enrollment.student.birth_date_str())
        item.append(enrollment.student.phone or enrollment.student.father_phone)
        item.append(enrollment.student.father)
        item.append(enrollment.student.mother)

        ws.append(item)
    
    file_path = os.path.join('static', 'docs')
    if not os.path.exists(file_path):
        os.makedirs(file_path)

    file_path = os.path.join(file_path, f'eleves{year.name} [{request.user.school.id}]')
    content_type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    response = HttpResponse(content_type=content_type)
    response['Content-Disposition'] = f'attachment; filename=ECOLEPRO_ELEVES{year.name}_{request.user.school.id}.xlsx'
    workbook.save(response)
    return response


class TeacherStudentsView(
    mixins.PermissionRequiredMixin, BaseHTMXRequestsView, 
    generic.TemplateView):
    
    template_name = 'partials/navs_base.html'
    permission_required = 'school.can_manage_level_grades'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        level_id = self.request.GET.get('classe')
        year = get_session_year(self.request)
        level = models.Level.objects.get(year=year, id=level_id)
        partial_template = 'partials/teacher/term_subject_select.html'

        terms = SchoolTerm.objects.for_year(
            year=level.year,
            user=self.request.user,
            level=level.generic_level,
            education=level.education
        )

        subjects = None
        if level and level.generic_level.cycle == main_utils.CYCLE_PRIMARY:
            subjects = LevelSubject.objects.filter(
                level=level.generic_level, year=level.year,
                active=True, subject__education=level.education,
                school=level.school).select_related('subject')
        else:
            subjects = models.TeacherLevel2.objects.filter(
                level=level, teacher=self.request.user.teacher
            ).first().subjects.all()

        context['partial_template'] = partial_template
        context['title'] = f'Classe de {level}'
        context['subtitle'] = f'Liste des élèves'
        context['level'] = level
        context['subjects'] = subjects
        context['terms'] = terms
        return context


class EnrollmentDeleteView(BaseHTMXRequestsView, generic.DeleteView):
    model = models.Enrollment
    template_name = 'partials/student/delete_confirm.html'

    def get_queryset(self):
        return models.Enrollment.objects.for_user(
            user=self.request.user, year=self.get_year())
        
    def form_valid(self, form=None):
        enrollment = self.get_object()
        payments_qs = models.Payment.objects.filter(enrollment=enrollment)
        if payments_qs.exists():
            payments_qs.delete()
        
        student = None
        student_enrollments__count = models.Enrollment.objects.filter(
            student__id=enrollment.student_id).count()
        student = enrollment.student
        enrollment.delete()

        # Delete student too if he has only one enrollment
        if student_enrollments__count == 1:
            student.delete()
            
        return HttpResponse(status=204, headers={'HX-Trigger': 'saved'})

def custom_export_view(request):
    if request.method == 'POST':
        education = str(request.POST.get('education')).upper()
        generic_level = request.POST.get('short_name')
        level = request.POST.get('level')
        resource = StudentResource(no_arabic=(education == main_utils.EDUCATION_FRENCH))
        queryset = resource.get_queryset(user=request.user, year=get_session_year(request))

        filename = 'ECOLEPRO_FICHIER_ELEVES'
        if not level and generic_level:
            if education == main_utils.EDUCATION_FRENCH:
                queryset = queryset.filter(generic_level_fr__id=generic_level)
                filename = str(queryset.first().generic_level_fr)
            else:
                queryset = queryset.filter(generic_level_ar__id=generic_level)
                filename = str(queryset.first().generic_level_ar)
            
        elif level:
            if education == main_utils.EDUCATION_FRENCH:
                queryset = queryset.filter(level_fr__id=level)

                if queryset.exists():
                    filename = str(queryset.first().level_fr)
            else:
                queryset = queryset.filter(level_ar__id=level)

                if queryset.exists():
                    filename = str(queryset.first().level_ar)

        if education == main_utils.EDUCATION_FRENCH:
            queryset = queryset.order_by('student__last_name', 'student__first_name')
        else:
            queryset = queryset.order_by('student__full_name_ar')

        dataset = resource.export(queryset=queryset)
        # dataset = resource.export()

        response = HttpResponse(dataset.xlsx, content_type='application/vnd.ms-excel')
        response['Content-Disposition'] = f"attachment; filename={filename}.xlsx"
        return response

    url = reverse('school:custom_export')
    context = {}
    nav_items = navs.get_active_students_navs(
            request.user, reverse('school:active_students'), reverse('school:import'),
            export_url=reverse('school:custom_export'), 
            show_list=not 'nolist' in request.GET)
    active_nav = 'export'

    description = main_utils.get_active_nav_item(nav_items, active_nav).get('description', '')
    aggregated = models.Enrollment.objects.for_user(user=request.user, year=get_session_year(request)).aggregate(
        boys=Count('student__gender', filter=Q(student__gender=main_utils.GENDER_MALE)),
        girls=Count('student__gender', filter=Q(student__gender=main_utils.GENDER_FEMALE)),
    )
    context['nav_items'] = nav_items
    context['active_nav'] = active_nav
    context['description'] = description
    context['title'] = 'Exporter vers Excel'
    context['subtitle'] = 'Exporter les données des élèves vers Excel'
    context['icon'] = 'arrow-down'
    context['partial_template'] = 'partials/active_students/excel_export.html'
    context['result_boys'] = aggregated.get('boys', 0)
    context['result_girls'] = aggregated.get('girls', 0)
        
    user = request.user
    year = get_session_year(request)
    context['generic_levels'] = models.GenericLevel.objects.for_school(user=user, year=year)
    template_name = 'partials/active_students/students_base.html'

    return render(request, template_name, context)


def student_page_action(request):
    action = request.POST.get('action')
    selected_items = main_utils.get_checked_items_list(request)
    
    year = get_session_year(request)
    user = request.user
    queryset = models.Enrollment.objects \
        .for_user_minimum(user=user, year=year) \
        .filter(pk__in=selected_items)
    
    if action == 'excel_export':    
        return export_selected_students(queryset)
    elif action == 'excel_export_for_certificate':
        return export_selected_students(queryset, StudentResourceForCertificate)
    elif action == 'excel_export_for_dfa':
        return export_for_dfa(queryset)

    return HttpResponse(str(queryset.count()) + ' élément(s) sélectionnés')

def export_selected_students(queryset, resource_class=StudentResource):
    """ Exports students data to excel. If no resource class is specified defaults to StudentResource"""
    resource = resource_class()
    dataset = resource.export(queryset=queryset)
    response = HttpResponse(dataset.xlsx, content_type='application/vnd.ms-excel')
    response['Content-Disposition'] = f"attachment; filename=ECOLEPRO_FICHIER_ELEVES.xlsx"
    return response

def export_for_dfa(queryset):
    """ Exports dfa data to excel."""
    resource = DFARessource()
    dataset = resource.export(queryset=queryset)
    response = HttpResponse(dataset.xlsx, content_type='application/vnd.ms-excel')
    response['Content-Disposition'] = f"attachment; filename=FICHIER_DFA.xlsx"
    return response


class SchoolCreationView(SessionWizardView):
    template_name = 'partials/school/school_form_wizard.html'
    file_storage = FileSystemStorage(location=os.path.join(settings.MEDIA_ROOT, 'photos'))
    form_list = [
        forms.TestSchoolFormStep1,
        forms.TestSchoolFormStep2,
        forms.TestSchoolFormStep3,
        forms.TestSchoolFormStep4,
        forms.ConfirmationForm,  # Add a new empty form for confirmation step
    ]

    def get_context_data(self, form, **kwargs):
        context = super().get_context_data(form, **kwargs)
        if self.steps.current != '0':
            context.update(
                {
                    'selected_cycle': self.get_cleaned_data_for_step('0').get('cycle'),
                    'selected_education': self.get_cleaned_data_for_step('0').get('education'),
                }
            )

        # Add data for the summary step
        if self.steps.current == '4':
            # Collect all data from previous steps for summary
            context['summary_data'] = {
                'school_data': self.get_cleaned_data_for_step('0'),
                'contact_data': self.get_cleaned_data_for_step('1'),
                'plan_data': self.get_cleaned_data_for_step('2'),
                'users_data': self.get_cleaned_data_for_step('3'),
            }
            context['is_summary_step'] = True
        elif self.steps.current == '3':
            context.update(
                {
                    'selected_plan': self.get_cleaned_data_for_step('2').get('plan'),
                    'selected_status': self.get_cleaned_data_for_step('0').get('status'),
                }
            )

        if not self.request.htmx:
            context['partial_template'] = 'partials/school/school_form_wizard.html'
        
        return context

    def get_template_names(self):
        if self.request.htmx:
            return 'partials/school/school_form_wizard.html'
        return 'login.html'

    @atomic()
    def done(self, form_list, **kwargs):
        # Extract data from forms (skipping the last confirmation form)
        school_data = form_list[0].cleaned_data
        school_data2 = form_list[1].cleaned_data
        plan_data = form_list[2].cleaned_data
        users_data = form_list[3].cleaned_data

        # Rest of the method stays the same
        # ...existing code...

        year = school_utils.get_current_year()

        # Founder 
        name = users_data.get('scientist')
        phone = users_data.get('scientist_phone')
        founder = models.Founder.objects.create(name=name, phone=phone)

        # School  
        school = models.School(**school_data, **school_data2)
        if school_data.get('education') == main_utils.EDUCATION_ARABIC:
            school.pricing_option = models.School.PRICING_BY_ARABIC
        else:
            school.pricing_option = models.School.PRICING_BY_FRENCH
        school.founder = founder
        school.save()

        # Subscription 
        plan = plan_data.get('plan')
        level = plan_data.get('level')

        models.Subscription.objects.create(
            school=school, plan=plan, 
            plan_type=models.Subscription.PLAN_TYPE_TEST,
            year=year,
            level=level,
        )

        # Create users

        # 1. Computer scientist or Teacher
        name = users_data.get('scientist')
        last_name = name.split(' ')[0]
        first_name = ' '.join(name.split(' ')[1:])
        phone = users_data.get('scientist_phone')
        role = main_utils.ROLE_FOUNDER

        if plan == models.Subscription.PLAN_LEVEL:
            role = main_utils.ROLE_TEACHER

        scientist_user = CustomUser(
            last_name=last_name, 
            first_name=first_name, 
            role=role,
            custom_password=phone)
        scientist_user, group_name = main_utils.create_user(
            scientist_user, phone, school, year, plan, username=phone)
        scientist_user.save()

        if plan == models.Subscription.PLAN_LEVEL:
            models.Teacher.objects.create(
                last_name=last_name, 
                first_name=first_name,
                school=school, origin=year,
                user=scientist_user)

        group, created = Group.objects.get_or_create(name=group_name)
        group.user_set.add(scientist_user)
        
        # 2. Founder
        # name = users_data.get('admin') or users_data.get('scientist')
        # last_name = name.split(' ')[0]
        # first_name = ' '.join(name.split(' ')[1:])
        # phone = users_data.get('admin_phone') or users_data.get('scientist_phone')
        # founder_user = CustomUser(
        #     last_name=last_name, first_name=first_name, 
        #     role=main_utils.ROLE_FOUNDER, custom_password=phone)
        # founder_user, group_name = main_utils.create_user(founder_user, phone, school, year)
        # founder_user.save()
        # group, created = Group.objects.get_or_create(name=group_name)
        # group.user_set.add(founder_user)

        # Send sms
        sms_phone = users_data.get('sms_phone')
        message = \
            f"""Bonjour, votre école a été enregistrée avec succès. Voici vos accès:

Administrateur: {scientist_user.username}, 
Mot de passe: {scientist_user.custom_password},

Site web: www.ecolepro.net 
Pour plus d'infos: 07 59 95 14 53 / 05 45 84 55 98
Cordialement, l'équipe d'EcolePro"""
        
        if plan == models.Subscription.PLAN_LEVEL:
            message = \
                f"""Bonjour, votre compte a été créé avec succès. Voici vos informations de connexion: 
Login: {scientist_user.username}, 
Mot de passe: {scientist_user.custom_password},
Site web: www.ecolepro.net Pour plus d'infos: 07 59 95 14 53 / 05 45 84 55 98

Cordialement,
l'équipe d'EcolePro"""

        school.sms_phone = f'{sms_phone}'
        school.sms_message = message
        school.sms_sent = send_sms(self.request.session.session_key, f'+225{sms_phone}', message)
        school.save(update_fields=['sms_phone', 'sms_message', 'sms_sent'])
        
        # Send myself a message
        message = f"""Une nouvelle école vient de s'enregistrer: {school.name} {school.location}. Contact: {school.sms_phone}"""
        send_sms(self.request.session.session_key, settings.ADMIN_PHONE, message)

        # Create levels in the bg
        create_default_subjects_and_terms_task.delay(school.id)
        create_levels_for_school.delay(school.id, plan, year.id, level.id if level else None)
        return HttpResponse(status=205, headers={'HX-Trigger': 'saved'})


class SchoolSettingsView(mixins.PermissionRequiredMixin, BaseHTMXRequestsView, generic.TemplateView):
    template_name = 'partials/navs_base.html'
    permission_required = 'exams.manage_all_grades'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        section = self.request.GET.get('section')
        partial_template = 'partials/settings/school_settings.html'
        navs_items = navs.get_settings_nav(f"{reverse('school:settings')}")
        
        context['partial_template'] = partial_template
        context['nav_items'] = navs_items
        context['active_nav'] = section
        context['title'] = 'Paramètres'
        context['subtitle'] = "Editer les informations de l'école et de l'utilisateur"
        
        school = self.request.user.school
        if section == 'ecole':
            form = forms.SettingsSchoolForm(instance=school)
            if school.cycle == main_utils.CYCLE_SECONDARY:
                del form.fields['IEP']
                del form.fields['secteur_p']
            elif school.cycle == main_utils.CYCLE_PRIMARY:
                del form.fields['name_secondary']

            if school.education == main_utils.EDUCATION_FRENCH:
                del form.fields['translation']

            context['form'] = form
            context['page_title'] = "Informations de l'école"
        elif section == 'direction':
            form = forms.SettingsSchoolDirectionForm(instance=school)
            if school.education == main_utils.EDUCATION_FRENCH:
                del form.fields['director_ar']
            if school.cycle == main_utils.CYCLE_PRIMARY:
                del form.fields['director_secondary']
            if school.cycle == main_utils.CYCLE_SECONDARY and \
                school.education == main_utils.EDUCATION_FRENCH:
                del form.fields['director_fr']
            context['form'] = form
            context['page_title'] = "Informations sur la Direction"
        elif section == 'headers':
            initials = {}
            education = school.education
            cycle = school.cycle if not school.cycle == main_utils.CYCLE_BOTH else main_utils.CYCLE_PRIMARY
            if not school.left_header and education == main_utils.EDUCATION_ARABIC:
                initials['left_header'] = reports.get_arabic_headers(cycle, school, 'L')
            elif not school.left_header and education == main_utils.EDUCATION_FRENCH:
                initials['left_header'] = reports.get_french_headers(cycle, school, 'L')

            if not school.right_header and education == main_utils.EDUCATION_ARABIC:
                initials['right_header'] = reports.get_arabic_headers(cycle, school, 'R', False)
            elif not school.right_header and education == main_utils.EDUCATION_FRENCH:
                initials['right_header'] = reports.get_french_headers(cycle, school, 'R', False)

            form = forms.SettingsSchoolHeaderForm(instance=school, initial=initials)
            context['form'] = form
            context['page_title'] = "Personnaliser les entêtes de fichiers"
        return context

    def post(self, request, *args, **kwargs):
        section = self.request.GET.get('section')
        school = request.user.school
        form = None

        if section == 'ecole':
            form = forms.SettingsSchoolForm(
                request.POST, instance=school,
                files=request.FILES or None)
        elif section == 'direction':
            form = forms.SettingsSchoolDirectionForm(
                request.POST, instance=school)
        elif section == 'headers':
            form = forms.SettingsSchoolHeaderForm(
                request.POST, instance=school)
    
        if form.is_valid():
            form.save(True)

        return HttpResponse(status=204, headers={'HX-Trigger': 'saved'})


class LevelBatchCreateWizard(SessionWizardView):
    """Two-step wizard for batch creation of levels."""
    template_name = 'partials/level/level_batch_wizard.html'
    form_list = [
        ('input', forms.LevelCreationForm),  # First step: collect level data
        ('confirm', forms.Form)              # Second step: confirmation (empty form)
    ]

    def get_form_kwargs(self, step=None):
        kwargs = super().get_form_kwargs(step)
        if step == 'input':
            kwargs['user'] = self.request.user
            kwargs['year'] = get_session_year(self.request)
        return kwargs

    def get_form(self, step=None, data=None, files=None):
        form = super().get_form(step, data, files)
        # Add subschool fields if school has subschools
        if step == 'input' and self.request.user.school.subschool_set.count() >= 2:
            form.fields['subschool'].queryset = self.request.user.school.subschool_set.all()
            form.fields['subschool'].widget.attrs['required'] = True
        return form
    
    def get_levels_preview(self):
        """Generate preview of levels that will be created"""
        if self.steps.current != 'confirm':
            return []

        education = self.request.GET.get('education', 'fr').upper()[0]

        # Get data from first step
        input_data = self.get_cleaned_data_for_step('input')
        if not input_data:
            return []
            
        levels_preview = []
        
        # Get all generic levels
        generic_levels = models.GenericLevel.objects.filter(
            id__in=[int(field.replace('num_levels_', '')) 
                   for field in input_data.keys() 
                   if field.startswith('num_levels_') and input_data[field] > 0], 
        ).annotate(
            levels=Count('level', 
                            filter=Q(level__school=self.request.user.school, 
                                    level__year=get_session_year(self.request), 
                                    level__education=education), 
                                    distinct=True)
        )
        
        # Build the preview data
        for generic_level in generic_levels:
            num_levels = input_data.get(f'num_levels_{generic_level.id}', 0)
            if num_levels and num_levels > 0:
                for i in range(generic_level.levels, generic_level.levels + num_levels):
                    print(str(generic_level), i)
                    levels_preview.append({
                        'generic_level': generic_level.name,
                        'number': i,
                        'name': f"{generic_level.name} {i if generic_level.cycle == main_utils.CYCLE_SECONDARY else chr(i + ord('A'))}",
                    })
        return levels_preview
    
    def get_context_data(self, form, **kwargs):
        context = super().get_context_data(form, **kwargs)
        
        education = self.request.GET.get('education', 'fr')
        context['education'] = education
        
        if self.steps.current == 'confirm':
            # Add preview data for confirmation step
            context['levels_preview'] = self.get_levels_preview()
            context['form_title'] = 'Confirmer la création des classes'
            context['form_description'] = 'Veuillez confirmer la création des classes suivantes:'
            
            # Get subschool if selected
            subschool_id = self.get_cleaned_data_for_step('input').get('subschool')
            if subschool_id:
                context['subschool'] = models.Subschool.objects.get(pk=subschool_id.id)
        else:
            # Input step
            context['form_title'] = 'Création de classes'
            context['form_description'] = 'Veuillez entrer le nombre de classes à créer par niveau'
            context['col_width'] = 'col-xl-4 col-6'
        
        return context
    
    def process_step(self, form):
        """Process each step before moving to next"""
        # This ensures form data is correctly processed between steps
        return self.get_form_step_data(form)
    
    def render_done(self, form, **kwargs):
        """Final processing before calling done()"""
        # Make sure we're properly rendering the done view
        final_forms = kwargs.pop('form_dict', {})
        return self.done(list(final_forms.values()), **kwargs)
    
    def done(self, form_list, **kwargs):
        """Process the form data and create the levels"""
        input_data = self.get_cleaned_data_for_step('input')
        if not input_data:
            # No valid data, redirect back to form
            return HttpResponseRedirect(reverse('school:levels') + f'?education={self.request.GET.get("education", "fr")}')
        
        # Get education from request
        education = self.request.GET.get('education', 'fr').upper()[0]
        year = get_session_year(self.request)
        school = self.request.user.get_school()
        school_education = school.education
        
        # Get subschool if selected
        subschool = input_data.get('subschool')
        
        # Create levels
        levels_to_create = []

        generic_levels = models.GenericLevel.objects.for_school(
            user=self.request.user, year=year
        ).annotate(
            levels=Count('level', 
                            filter=Q(level__school=school, 
                                    level__year=year, 
                                    level__education=education), 
                                    distinct=True)
        )
        objs_to_create = []
        french_levels = []
        arabic_levels = []
        subschool = None
        if input_data.get('subschool'):
            subschool = input_data.get('subschool')
        for generic_level in generic_levels:
            num_levels = input_data.get(f'num_levels_{generic_level.id}', 0)

            if num_levels > 0:
                for n in range(generic_level.levels + 1, generic_level.levels + num_levels + 1):
                    appendage = n
                    if generic_level.cycle == main_utils.CYCLE_PRIMARY:
                        appendage = chr(n + ord('A') - 1)
                    level_name = f'{generic_level} {appendage}'
                    french_levels.append(
                        models.Level(
                            education=main_utils.EDUCATION_FRENCH, number=level_name, 
                            year=year, school=school, max=75,
                            generic_level=generic_level, 
                            subschool=subschool
                        )
                    )
                    if school_education == main_utils.EDUCATION_ARABIC:
                        arabic_levels.append(
                            models.Level(
                                education=main_utils.EDUCATION_ARABIC, number=level_name, 
                                year=year, school=school, max=75,
                                generic_level=generic_level,
                                subschool=subschool
                            )
                        )

        objs_to_create = [*french_levels, *arabic_levels]
        models.Level.objects.bulk_create(objs_to_create, ignore_conflicts=True)
        return HttpResponseRedirect(reverse('school:levels') + f'?education={self.request.GET.get("education", "fr")}')
    
    

class SchoolSMSBalanceView(BaseHTMXRequestsView, generic.TemplateView):
    template_name = 'partials/navs_base.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        school = self.request.user.school
        balance, created = models.MessageBalance.objects.get_or_create(
                defaults={'balance': 0, 'used': 0}, school=school
        )
        context['nav_items'] = navs.sms_nav(reverse('school:sms_balance'))
        context['partial_template'] = 'partials/sms/balance.html'
        context['active_nav'] = 'balance'
        context['title'] = 'Notifications SMS'
        context['subtitle'] = 'Gestion du solde SMS et notifications'
        context['balance'] = balance
        return context


def fiche_scolarite_pdf(request, enrollment_id):
    year = get_session_year(request)
    enrollment = models.Enrollment.objects.for_user_minimum(
            request.user, year=year, pk=enrollment_id 
        ).first()
    pdf = reports.FicheScolarite()
    pdf.set_margins(5, 5, 5)
    pdf.add_page()
    # pdf.set_font('Helvetica', size=9)
    pdf.add_content(enrollment)
    return pdf.get_file_response('fiche_scolaire.pdf')

def transliterate_names_view(request):
    is_mobile = False
    last_name = request.GET.get('last_name')

    if request.GET.get('0-last_name'):
        last_name = request.GET.get('0-last_name')
        is_mobile = True
        
    first_name = request.GET.get('first_name')
    if request.GET.get('0-first_name'):
        first_name = request.GET.get('0-first_name')
        is_mobile = True

    birth_place = None
    field = request.GET.get('field')
    url = 'https://transliterate.qcri.org/en2ar/nbest/'
    translation = ''
    translations = []
    resp = None
    if field == 'birth_place_ar':
        birth_place = request.GET.get('birth_place')
        if request.GET.get('0-birth_place'):
            birth_place = request.GET.get('0-birth_place')
            is_mobile = True
        resp = requests.get(f"{url}{birth_place}").json()['results']
        for i in range(len(resp)):
            value = resp[str(i)]
            translations.append(value)
    else:
        last_name_resp = None
        if last_name:
            last_name_resp = requests.get(f'{url}{last_name}').json()['results']

        first_name_resp = None
        if first_name:
            first_name_resp = requests.get(f'{url}{first_name}').json()['results']
        
        for i in range(len(first_name_resp)):
            value = ''
            if last_name:
                value = last_name_resp[str(i)]
            if first_name:
                value = f"{value} {first_name_resp[str(i)]}"
            value = value.replace('ايكا', 'ايشا')
            translations.append(value)

    if is_mobile:
        field = f'0-{field}'
    return render(request, f'partials/student/transliterate_field.html', 
                  {'value': translations[0], 'field': field, 'translations': translations})
    
class StaffListView(mixins.PermissionRequiredMixin, 
                BaseHTMXRequestsView, generic.ListView):
    model = models.Staff
    template_name = 'partials/navs_base.html'
    context_object_name = 'staff_list'
    permission_required = 'school.view_staff'

    def get_queryset(self):
        user = self.request.user
        try:
            self.create_staff_from_data()
        except:
            pass
        return models.Staff.objects.filter(school=user.school)
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['active_nav'] = 'staff'
        context['nav_items'] = navs.get_staff_nav(
            reverse('school:staff'),
            roles_url=reverse('school:staff_roles'),
            payment_options_url=reverse('school:payment_options'),
            payments_url=reverse('school:staff_payments'),
        )
        context['title'] = 'Personnel'
        context['subtitle'] = "Gestion des employés"
        context['icon'] = "users"
        context['partial_template'] = 'partials/staff/staff_list.html'
        context['budget'] = self.get_queryset().aggregate(total=Sum('salary'))['total']
        return context

    def create_staff_from_data(self):
        """Create staff records from existing teachers and accountant users"""
        school = self.request.user.school

        # Get the default staff roles
        teacher_role = models.StaffRole.objects.get(
            code='enseignant',
        )
        
        accountant_role = models.StaffRole.objects.get(
            code='comptable', 
        )

        with transaction.atomic():
            # Create staff from teachers
            teachers = models.Teacher.objects.filter(
                school=school,
                active=True
            ).exclude(
                Q(staff__isnull=False) # Exclude teachers who already have staff records
            )
            
            staff_records = []
            for teacher in teachers:
                staff_records.append(models.Staff(
                    last_name=teacher.last_name,
                    first_name=teacher.first_name,
                    phone=teacher.phone,
                    gender=teacher.gender,
                    birth_day=teacher.birth_day,
                    birth_month=teacher.birth_month, 
                    birth_year=teacher.birth_year,
                    birth_place=teacher.birth_place,
                    id_number=teacher.id_number,
                    school=school,
                    role=teacher_role,
                    teacher=teacher,
                    user=teacher.user
                ))

            # Create staff from accountant users
            accountants = CustomUser.objects.filter(
                school=school,
                role=main_utils.ROLE_ACCOUNTANT,
                is_active=True
            ).exclude(
                Q(staff__isnull=False) # Exclude users who already have staff records
            )

            for accountant in accountants:
                staff_records.append(models.Staff(
                    last_name=accountant.last_name,
                    first_name=accountant.first_name,
                    email=accountant.email,
                    school=school,
                    role=accountant_role,
                    user=accountant
                ))

            # Bulk create all staff records
            if staff_records:
                models.Staff.objects.bulk_create(staff_records)
            print(f"Created {len(staff_records)} staff records. DONE")


class StaffCreateView(mixins.PermissionRequiredMixin, generic.CreateView):
    model = models.Staff
    form_class = forms.StaffForm
    template_name = 'partials/staff/staff_edit.html'
    permission_required = 'school.add_staff'

    def form_valid(self, form):
        user = self.request.user
        staff = form.save(False)
        staff.school = user.school
        staff.save()
        return HttpResponse(status=204, headers={'HX-Trigger': 'saved'})

    def form_invalid(self, form) -> HttpResponse:
        print(form.errors.as_text())
        return super().form_invalid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form_title'] = 'Ajouter un employé'
        context['blank_photo'] = '/static/img/avatar.jpg'
        return context
    

class StaffUpdateView(mixins.PermissionRequiredMixin, generic.UpdateView):
    model = models.Staff
    form_class = forms.StaffForm
    template_name = 'partials/staff/staff_edit.html'
    permission_required = 'school.change_staff'

    def form_valid(self, form):
        form.save(True)
        return HttpResponse(status=204, headers={'HX-Trigger': 'saved'})

    def get_queryset(self):
        return super().get_queryset().filter(school=self.request.user.school)
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form_title'] = f'Modifier employé: {self.get_object()}'
        context['blank_photo'] = '/static/img/avatar.jpg'
        return context

    def handle_no_permission(self):
        return HttpResponse(status=403, headers={'HX-Trigger': 'permission_denied'})


class StaffRoleListView(mixins.PermissionRequiredMixin, 
                BaseHTMXRequestsView, generic.ListView):
    model = models.StaffRole
    template_name = 'partials/navs_base.html'
    context_object_name = 'staff_roles'
    permission_required = 'school.view_staffrole'

    def get_queryset(self):
        user = self.request.user
        return models.StaffRole.objects.filter(
            Q(school__isnull=True) | Q(school=user.school)) \
            .annotate(staff_count=Count('staff', filter=Q(staff__school=user.school)))
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['active_nav'] = 'staff_roles'
        context['nav_items'] = navs.get_staff_nav(
            reverse('school:staff'),
            roles_url=reverse('school:staff_roles'),
            payment_options_url=reverse('school:payment_options'),
            payments_url=reverse('school:staff_payments'),
        )
        context['title'] = 'Emplois'
        context['subtitle'] = "Gestion des emplois/postes"
        context['icon'] = "grid"
        context['partial_template'] = 'partials/staff/staff_role_list.html'
        return context


class StaffRoleCreateView(mixins.PermissionRequiredMixin, generic.CreateView):
    model = models.StaffRole
    fields = ['name', 'code']
    template_name = 'partials/simple_form.html'
    permission_required = 'school.add_staffrole'

    def form_valid(self, form):
        user = self.request.user
        staffrole = form.save(False)
        staffrole.school = user.school
        staffrole.save()
        return HttpResponse(status=204, headers={'HX-Trigger': 'saved'})

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form_title'] = 'Nouvelle emploi/poste'
        return context
    

class StaffRoleUpdateView(mixins.PermissionRequiredMixin, generic.UpdateView):
    model = models.StaffRole
    fields = ['name', 'code']
    template_name = 'partials/simple_form.html'
    permission_required = 'school.change_staffrole'

    def form_valid(self, form):
        form.save(True)
        return HttpResponse(status=204, headers={'HX-Trigger': 'saved'})

    def get_queryset(self):
        return super().get_queryset().filter(school=self.request.user.school)
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form_title'] = f'Modifier emploi: {self.get_object()}'
        return context

    def handle_no_permission(self):
        return HttpResponse(status=403, headers={'HX-Trigger': 'permission_denied'})


class PaymentOptionsListView(mixins.PermissionRequiredMixin, 
                BaseHTMXRequestsView, generic.ListView):
    model = models.SalaryPaymentOptions
    template_name = 'partials/navs_base.html'
    context_object_name = 'payment_options'
    permission_required = 'school.view_salarypaymentoptions'

    def get_queryset(self):
        user = self.request.user
        return models.SalaryPaymentOptions.objects.filter(
            Q(school__isnull=True) | Q(school=user.school)
        )
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['active_nav'] = 'payment_options'
        context['nav_items'] = navs.get_staff_nav(
            reverse('school:staff'),
            roles_url=reverse('school:staff_roles'),
            payment_options_url=reverse('school:payment_options'),
            payments_url=reverse('school:staff_payments'),
        )
        context['title'] = 'Rubrique de Paie'
        context['subtitle'] = "Gestion rubriques de paie (qui figurent sur le bulletin de paie)"
        context['icon'] = "list"
        context['partial_template'] = 'partials/staff/payment_options.html'
        return context


class PaymentOptionCreateView(mixins.PermissionRequiredMixin, generic.CreateView):
    model = models.SalaryPaymentOptions
    fields = ['name', 'amount', 'rate', 'operation', 'option']
    template_name = 'partials/simple_form.html'
    permission_required = 'school.add_salarypaymentoptions'

    def form_valid(self, form):
        user = self.request.user
        option = form.save(False)
        option.school = user.school
        option.save()
        return HttpResponse(status=204, headers={'HX-Trigger': 'saved'})

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form_title'] = 'Nouvelle rubrique de paie'
        context['form_description'] = 'Vous pouvez définir le montant ou le taux en pourcentage (entre 1 et 100) de la rubrique, mais pas les 2 à la fois'
        return context
    

class PaymentOptionUpdateView(mixins.PermissionRequiredMixin, generic.UpdateView):
    model = models.SalaryPaymentOptions
    fields = ['name', 'amount', 'rate', 'operation', 'option']
    template_name = 'partials/simple_form.html'
    permission_required = 'school.change_salarypaymentoptions'

    def form_valid(self, form):
        form.save(True)
        return HttpResponse(status=204, headers={'HX-Trigger': 'saved'})

    def get_queryset(self):
        return super().get_queryset().filter(school=self.request.user.school)
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form_description'] = 'Vous pouvez définir le montant ou le taux en pourcentage (entre 1 et 100) de la rubrique, mais pas les 2 à la fois'
        context['form_title'] = f'Modifier Rubrique: {self.get_object()}'
        return context

    def handle_no_permission(self):
        return HttpResponse(status=403, headers={'HX-Trigger': 'permission_denied'})


class StaffSalaryForMonthListView(mixins.PermissionRequiredMixin, 
                BaseHTMXRequestsView, generic.ListView):
    model = models.Staff
    template_name = 'partials/navs_base.html'
    context_object_name = 'staff_list'
    permission_required = 'school.view_staffsalaryformonth'

    def get_queryset(self):
        user = self.request.user
        year = get_session_year(self.request)
        return models.Staff.objects.with_payments(year).filter(school=user.school)
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['active_nav'] = 'paiements'
        context['nav_items'] = navs.get_staff_nav(
            reverse('school:staff'),
            roles_url=reverse('school:staff_roles'),
            payment_options_url=reverse('school:payment_options'),
            payments_url=reverse('school:staff_payments'),
        )
        context['title'] = 'Paiement de Salaire'
        context['budget'] = self.get_queryset().aggregate(total=Sum('salary'))['total']
        context['subtitle'] = "Gestion des Paiements de Salaire"
        context['icon'] = "dollar-sign"
        context['partial_template'] = 'partials/staff/staff_payments.html'
        return context


class StaffSalaryPaymentCreateView(generic.CreateView):
    model = models.StaffSalaryForMonth
    fields = ['staff', 'month', 'salary', 'payment_date']
    template_name = 'partials/simple_form.html'
    permission_required = 'school.add_staffsalaryformonth'

    def form_valid(self, form):
        year = get_session_year(self.request)
        payment = form.save(False)
        payment.year = year
        payment.status = True
        payment.save()
        return HttpResponse(status=204, headers={'HX-Trigger': 'saved'})

    def get_context_data(self, **kwargs: Any):
        context = super().get_context_data(**kwargs)
        month = self.request.GET.get('mois')
        context['form_title'] = f'Paiement de Salaire'
        context['form'].fields['month'].initial = int(month)
        staff_qs = models.Staff.objects.filter(pk=self.request.GET.get('staff'))
        context['form'].fields['staff'].initial = staff_qs.first()
        context['form'].fields['staff'].queryset = staff_qs
        context['form'].fields['salary'].initial = staff_qs.first().salary
        return context


def staff_salary_report(request):
    school = request.user.school
    staff = models.Staff.objects.get(school=school, pk=request.GET.get('staff'))
    payment = models.StaffSalaryForMonth.objects.get(staff=staff, month=request.GET.get('month'))
    bulletin = reports.BulletinPaie(school)
    bulletin.add_content(staff, payment)
    return bulletin.get_file_response('Bulletin de paie')


class StudentEditListView(StudentsListViewBase):
    model = models.Enrollment
    template_name = 'partials/active_students/students_base.html'
    context_object_name = 'students'
    permission_required = 'school.change_enrollment'

    def get_paginate_by(self, queryset):
        return 50

    def get_queryset(self):
        queryset = super().get_queryset()
        level_filter = self.request.GET.get('level_filter')
        
        if level_filter:
            queryset = queryset.filter(level_fr__generic_level_id=level_filter)
        
        return queryset.order_by('student__last_name', 'student__first_name')

    def post(self, request, *args, **kwargs):
        """Handle bulk updates of student data"""
        user = request.user
        year = self.get_year()
        
        # Extract student data from POST
        students_data = {}
        for key, value in request.POST.items():
            if key.startswith('student['):
                # Parse student ID and field from form name e.g. student[1][first_name]
                student_id = key.split('[')[1].split(']')[0]
                field = key.split('[')[2].split(']')[0]
                
                if student_id not in students_data:
                    students_data[student_id] = {}
                students_data[student_id][field] = value

        # Bulk update students and enrollments
        with transaction.atomic():
            students_to_update = []
            for student_id, data in students_data.items():
                print('Student id', student_id, data)
                enrollment = models.Enrollment.objects.for_user(
                    user=user, 
                    year=year
                ).select_related('student').get(pk=student_id)
                
                # Update student fields
                student = enrollment.student
                student.first_name = data.get('first_name', student.first_name)
                student.last_name = data.get('last_name', student.last_name)
                student.gender = data.get('gender', student.gender)
                birth_day = data.get('birth_day')
                birth_month = data.get('birth_month')
                birth_year = data.get('birth_year')
                birth_date_valid = False
                if birth_day and birth_month and birth_year:
                    print('All defined')
                    try:
                        birth_date = datetime(int(birth_year), int(birth_month), int(birth_day))
                        birth_date_valid = True
                    except:
                        pass
                
                print(student.first_name, birth_date_valid)
                if birth_date_valid:
                    student.birth_day = birth_day
                    student.birth_month = birth_month
                    student.birth_year = birth_year
                
                new_matricule = data.get('new_matricule', student.student_id) or None
                if new_matricule and len(new_matricule) <= 9:
                    if new_matricule:
                        new_matricule = str(new_matricule).upper()
                    student.student_id = new_matricule
                students_to_update.append(student)
                print('Updated successfully')
            
            if students_to_update:
                models.Student.objects.bulk_update(students_to_update, 
                    fields=['first_name', 'last_name', 'gender', 
                            'birth_day', 'birth_month', 'birth_year', 
                            'student_id'])
        return HttpResponse(status=204, headers={'HX-Trigger': 'saved'})
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'Cartes'
        context['subtitle'] = "Gestion des badges du personnel"
        context['icon'] = "id-card"
        context['partial_template'] = 'partials/active_students/student_edit_list.html'
        context['edit_mode'] = True
        context['generic_levels'] = models.GenericLevel.objects.for_school(
            user=self.request.user, year=self.get_year()
        )
        context['title'] = 'Mode édition'
        context['subtitle'] = 'Modifier les informations des élèves plus facilement'
        return context


class StaffCardCreateView(generic.CreateView):
    model = models.StaffCard
    template_name = 'partials/simple_form.html'
    fields = [
        'photo', 'matricule', 'last_name', 'first_name', 'gender',
        'birth_date', 'birth_place', 'phone', 'job', 'authorization_number'
    ]

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form_title'] = 'Carte professionnelle'
        return context
    
    def form_valid(self, form):
        card = form.save(False)
        card.year = models.Year.objects.get(active=True)
        card.save()
        return HttpResponse(status=204, headers={'HX-Trigger': 'saved'})


class StaffCardUpdateView(generic.UpdateView):
    model = models.StaffCard
    template_name = 'partials/simple_form.html'
    fields = [
        'photo', 'matricule', 'last_name', 'first_name', 'gender',
        'birth_date', 'birth_place', 'phone', 'job', 'authorization_number'
    ]

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form_title'] = 'Carte professionnelle'
        return context
    
    def form_valid(self, form):
        form.save(True)
        return HttpResponse(status=204, headers={'HX-Trigger': 'saved'})
    

class SalaryPaymentDecisionCreateView(generic.CreateView):
    model = models.SalaryPaymentDecision
    template_name = 'partials/staff/paiement_decision_form.html'
    form_class = forms.PaymentDecisionForm

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        year = get_session_year(self.request)
        year_pk = year.pk
        context['form'].fields['year'].queryset = models.Year.objects.filter(pk=year_pk)
        context['form'].fields['year'].initial = year_pk
        print(context['form'].fields.keys())
        return context
    
    def form_valid(self, form):
        with transaction.atomic():
            form = form.save(False)
            form.school = self.request.user.school
            form.decision_number = models.SalaryPaymentDecision.objects.filter(
                school=form.school, year=form.year).count() + 1
            form.save()
            self.create_payment_for_month()
            return HttpResponse(status=204, headers={'HX-Trigger': 'saved'})
        return self.form_invalid(form)
    
    def create_payment_for_month(self):
        school = self.request.user.school
        queryset = models.Staff.objects.filter(school=school)
        options = []
        data = self.request.POST
        for key, value in data.items():
            if key.startswith('option_'):
                option = {}
                option_text, operation, option_type, option_id = key.split('_')
                option['amount'] = value
                option['id'] = option_id
                option_obj = models.SalaryPaymentOptions.objects.get(pk=option_id)
                option['option'] = option_obj
                option['type'] = option_type
                if operation.strip() == 'add':
                    option['operation'] = 'add'
                else:
                    option['operation'] = 'sub'

                options.append(option)

        salaries_obj = []
        options_obj = []
        for staff in queryset:
            gains_total = staff.salary
            deductions_total = 0
            payment = models.StaffSalaryForMonth(
                staff=staff, month=data.get('month'), year=get_session_year(self.request),
                salary=staff.salary, 
                    payment_date=datetime.strptime(data.get('payment_date'), "%d/%m/%Y"),
                    status=True)
            payment.save()
            for option in options:
                option_obj = models.StaffSalaryForMonthOption(
                    salary=payment, option=option['option'], 
                    amount=option['amount'] if option['type'] == 'amount' else None,
                    rate=option['amount'] if option['type'] == 'rate' else None,
                )
                if option['operation'] == 'add':
                    if option['type'] == 'amount':
                        gains_total += int(option_obj.amount)
                    elif option['type'] == 'rate' and options_obj.rate:
                        gains_total += (int(option_obj.rate) * staff.salary / 100)
                else:
                    if option['type'] == 'amount':
                        deductions_total += int(option_obj.amount)
                    elif option['type'] == 'rate' and options_obj.rate:
                        deductions_total += (int(option_obj.rate) * staff.salary / 100)
                options_obj.append(option_obj)
            payment.gains = gains_total
            payment.deductions = deductions_total
            payment.save(update_fields=['gains', 'deductions'])
        models.StaffSalaryForMonthOption.objects.bulk_create(options_obj)

    def form_invalid(self, form):
        print(form.errors.as_text())
        return super().form_invalid(form)

@decorators.login_required()
def subschool_levels_view(request):
    year = get_session_year(request)
    subschool_id = request.GET.get('subschool')
    education = request.GET.get('education', 'fr').upper()[0]
    
    queryset = models.Level.objects.for_user(
        request.user, 
        education=education, 
        year=year
    )
    
    # Filter by subschool
    if subschool_id:
        queryset = queryset.filter(subschool_id=subschool_id)
    
    queryset = queryset.order_by('generic_level__order', 'number')
    
    context = {
        'levels': queryset,
        'field_name': request.GET.get('input_name', 'level')
    }
    
    return render(request, 'partials/level/subschool_levels_select.html', context)
