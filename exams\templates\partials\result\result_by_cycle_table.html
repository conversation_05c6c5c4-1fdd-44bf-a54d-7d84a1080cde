{% load exams_tags %}

{% csrf_token %}
{% if term %}
<select name="selected-term" id="selected-term" class="d-none my-0">
    <option value="{{ term }}">Période</option>
</select>
{% endif %}
<!-- Results Summary Card -->
<!-- <div class="card mb-4 border-0 shadow-sm">
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <h5 class="card-title">
                    <span data-feather="calendar" class="feather-16 align-middle mr-1"></span>
                    Période: <span class="text-primary">{{ term_obj|default:"Période sélectionnée" }}</span>
                </h5>
                <p class="text-muted mb-0">
                    {% if request.GET.option == 'bulletins' %}
                    Génération des bulletins pour toutes les classes
                    {% else %}
                    Résultats et statistiques pour toutes les classes
                    {% endif %}
                </p>
            </div>
            <div class="col-md-6 text-md-right">
                <div class="btn-group">
                    <button type="button" class="btn btn-outline-secondary" onclick="$('#table').DataTable().search('').draw()">
                        <span data-feather="refresh-cw" class="feather-16 align-middle mr-1"></span>
                        Réinitialiser les filtres
                    </button>
                    <button type="button" class="btn btn-outline-primary" onclick="window.print()">
                        <span data-feather="printer" class="feather-16 align-middle mr-1"></span>
                        Imprimer
                    </button>
                </div>
            </div>
        </div>
    </div>
</div> -->

{% if request.GET.option == 'bulletins' %}
<!-- <div class="card mb-4 border-left-info shadow-sm">
    <div class="card-body">
        <div class="d-flex align-items-center">
            <div class="mr-3">
                <span data-feather="info" class="feather-24 text-info"></span>
            </div>
            <div>
                <h5 class="card-title mb-1">Génération des bulletins</h5>
                <p class="mb-0">Vous pouvez imprimer les bulletins par lot de 15 élèves pour optimiser le temps de génération, ou télécharger tous les bulletins d'une classe en une seule fois.</p>
            </div>
        </div>
    </div>
</div> -->
{% endif %}
<div class="table-responsive">
    <table class="table table-striped table-bordered table-hover table-sm shadow-sm" id="table">
        <thead>
            <tr class="bg-primary text-white">
                <!-- <th class="text-center" style="width: 50px;">#</th> -->
                <th class="text-center">Classe</th>
                {% if request.GET.option == 'resultats' %}
                <th class="text-center">Résultats Détaillés</th>
                <th class="text-center">Résultats Résumé</th>
                <th class="text-center">Effectifs</th>
                <th class="text-center">Notés</th>
                <!-- <th class="text-center" colspan="2">Garçons admis</th>
                <th class="text-center" colspan="2">Filles admises</th> -->
                <th class="text-center" colspan="2">Admis</th>
                {% endif %}
                {% if request.GET.option == 'bulletins' %}
                <!-- <th class="text-center">Par lot</th> -->
                <th class="text-center">Par classe</th>
                {% endif %}
            </tr>
            <tr class="text-center bg-light">
                <!-- <th class="text-center"></th> -->
                <th class="text-center"></th>
                {% if request.GET.option == 'resultats' %}
                <th class="text-center"></th>
                <th class="text-center"></th>
                <th class="text-center font-weight-bold"></th>
                <!-- <th class="text-center font-weight-bold">F</th>
                <th class="text-center font-weight-bold">T</th> -->
                <th class="text-center"></th>
                <!-- <th class="text-center font-weight-bold">Adm.</th>
                <th class="text-center font-weight-bold">%</th>
                <th class="text-center font-weight-bold">Adm.</th>
                <th class="text-center font-weight-bold">%</th> -->
                <th class="text-center font-weight-bold">Nbre</th>
                <th class="text-center font-weight-bold">%</th>
                {% else %}
                <!-- <th></th> -->
                <th></th>
                {% endif %}
            </tr>
        </thead>
    <tbody>
    {% for level in queryset %}
    <tr>
        <!-- <td class="align-middle text-center">{{ forloop.counter }}</td> -->
        <td class="align-middle font-weight-bold">{{ level }}</td>
        {% if request.GET.option == 'resultats' %}
        <td class="align-middle">
            <div class="dropdown">
                <button class="btn btn-outline-success dropdown-toggle w-100 {% if not level.term_id %} disabled {% endif %}" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <span data-feather="file-text" class="feather-16 align-middle mr-1"></span> Télécharger
                </button>
                <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                    <h6 class="dropdown-header">Résultats Détaillés</h6>
                    <a target="_blank" class="dropdown-item" href="{% url 'exams:period_results_pdf' %}?level={{ level.id }}&selected-term={{ level.term_id }}&generic_term={{ term }}&all">
                        <span data-feather="arrow-up" class="feather-16 align-middle mr-1"></span> Par ordre de mérite
                    </a>
                    <a target="_blank" class="dropdown-item" href="{% url 'exams:period_results_pdf' %}?level={{ level.id }}&selected-term={{ level.term_id }}&generic_term={{ term }}&ordering=alpha&all">
                        <span data-feather="list" class="feather-16 align-middle mr-1"></span> Par ordre alphabétique
                    </a>
                </div>
            </div>
        </td>
        <td class="align-middle">
            <div class="dropdown">
                <button class="btn btn-outline-info dropdown-toggle w-100 {% if not level.term_id %} disabled {% endif %}" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <span data-feather="file" class="feather-16 align-middle mr-1"></span> Télécharger
                </button>
                <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                    <h6 class="dropdown-header">Résultats Résumés</h6>
                    <a target="_blank" class="dropdown-item" href="{% url 'exams:period_results_pdf' %}?level={{ level.id }}&selected-term={{ level.term_id }}&generic_term={{ term }}&all&summarized">
                        <span data-feather="arrow-up" class="feather-16 align-middle mr-1"></span> Par ordre de mérite
                    </a>
                    <a target="_blank" class="dropdown-item" href="{% url 'exams:period_results_pdf' %}?level={{ level.id }}&selected-term={{ level.term_id }}&generic_term={{ term }}&ordering=alpha&all&summarized">
                        <span data-feather="list" class="feather-16 align-middle mr-1"></span> Par ordre alphabétique
                    </a>
                </div>
            </div>
        </td>
        {% endif %}
        {% if request.GET.option == 'resultats' %}
        <!-- <td class="align-middle text-center font-weight-bold">{{ level.boys_count|default:"-" }}</td>
        <td class="align-middle text-center font-weight-bold">{{ level.girls_count|default:"-" }}</td> -->
        <td class="align-middle text-center font-weight-bold">{{ level.students|default:"-" }}</td>
        <td class="align-middle text-center">
            {% if level.students and level.marked %}
                <span class="badge badge-pill badge-primary p-2">{{ level.marked }}</span>
            {% else %}
                <span class="badge badge-pill badge-light p-2">-</span>
            {% endif %}
        </td>
        <!-- <td class="align-middle text-center">
            {% if level.students and level.boys_admitted %}
                <span class="badge badge-pill badge-success p-2">{{ level.boys_admitted }}</span>
            {% else %}
                <span class="badge badge-pill badge-light p-2">-</span>
            {% endif %}
        </td>
        <td class="align-middle text-center">
            {% if level.boys_perc %}
                <div class="progress" style="height: 20px;">
                    <div class="progress-bar bg-success" role="progressbar" style="width: {{ level.boys_perc }}%;" aria-valuenow="{{ level.boys_perc }}" aria-valuemin="0" aria-valuemax="100">{{ level.boys_perc }}%</div>
                </div>
            {% else %}
                <span class="badge badge-pill badge-light p-2">-</span>
            {% endif %}
        </td>
        <td class="align-middle text-center">
            {% if level.students and level.girls_admitted %}
                <span class="badge badge-pill badge-info p-2">{{ level.girls_admitted }}</span>
            {% else %}
                <span class="badge badge-pill badge-light p-2">-</span>
            {% endif %}
        </td>
        <td class="align-middle text-center">
            {% if level.girls_perc %}
                <div class="progress" style="height: 20px;">
                    <div class="progress-bar bg-info" role="progressbar" style="width: {{ level.girls_perc }}%;" aria-valuenow="{{ level.girls_perc }}" aria-valuemin="0" aria-valuemax="100">{{ level.girls_perc }}%</div>
                </div>
            {% else %}
                <span class="badge badge-pill badge-light p-2">-</span>
            {% endif %}
        </td> -->
        <td class="align-middle text-center">
            {% if level.students and level.students_admitted %}
                <span class="badge badge-pill badge-primary p-2">{{ level.students_admitted }}</span>
            {% else %}
                <span class="badge badge-pill badge-light p-2">-</span>
            {% endif %}
        </td>
        <td class="align-middle text-center">
            {% if level.students_perc %}
                <div class="progress" style="height: 20px;">
                    <div class="progress-bar bg-primary" role="progressbar" style="width: {{ level.students_perc }}%;" aria-valuenow="{{ level.students_perc }}" aria-valuemin="0" aria-valuemax="100">{{ level.students_perc }}%</div>
                </div>
            {% else %}
                <span class="badge badge-pill badge-light p-2">-</span>
            {% endif %}
        </td>
        {% endif %}
        {% if request.GET.option == 'bulletins' %}
        <!-- <td class="align-middle">
            {% if level.range_of_15 %}
                <div class="card border-0 shadow-sm">
                    <div class="card-body p-2">
                        <h6 class="card-title mb-2">
                            <span data-feather="layers" class="feather-16 align-middle mr-1"></span>
                            Bulletins par lot
                        </h6>
                        <div class="btn-group-vertical w-auto">
                            <div class="btn-group mb-2">
                                {% for i in level.range_of_15|times %}
                                <a class="btn btn-sm btn-outline-warning" target="_blank" href="{% url 'exams:grade_action' %}?action=generate_report&level={{ level.id }}&lot={{ i|add:1 }}&all&generic_term={{ term }}&report_type=RP">
                                    <span data-feather="file-text" class="feather-16 align-middle mr-1"></span>
                                    Lot {{ i|add:1 }} (Modèle 1)
                                </a>
                                {% endfor %}
                            </div>

                            {% if not is_first_term %}
                            <div class="btn-group">
                                {% for i in level.range_of_15|times %}
                                <a class="btn btn-sm btn-outline-info" target="_blank" href="{% url 'exams:grade_action' %}?action=generate_report&level={{ level.id }}&lot={{ i|add:1 }}&all&generic_term={{ term }}&report_type=RW">
                                    <span data-feather="file-text" class="feather-16 align-middle mr-1"></span>
                                    Lot {{ i|add:1 }} (Modèle 2)
                                </a>
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            {% else %}
                <span class="badge badge-pill badge-light p-2">Aucun élève</span>
            {% endif %}
        </td> -->
        <td class="align-middle">
            <div class="card border-0 shadow-sm">
                <div class="card-body p-2" style="max-width: 300px;">
                    <div class="btn-group-vertical d-flex flex-row mx-2">
                        {% if level.is_clean and level.file_id or level.file_id2 %}
                            {% if level.file_id %}
                                <a class="btn btn-outline-warning" href="{% url 'exams:file_download' level.file_id %}">
                                    <span data-feather="file-text" class="feather-16 align-middle"></span>
                                    Modèle 1 (Par défaut)
                                </a>
                            {% endif %}

                            {% if level.file_id2 %}
                                <a class="btn btn-outline-info" href="{% url 'exams:file_download' level.file_id2 %}">
                                    <span data-feather="file-text" class="feather-16 align-middle"></span>
                                    Modèle 2 (Avec Rappels)
                                </a>
                            {% endif %}
                        {% else %}
                            <a class="btn btn-outline-warning" href="{% url 'exams:generate_reports_for_level' %}?level={{ level.id }}&generic_term={{ term }}&report_type=RP">
                                <span data-feather="download" class="feather-16 align-middle"></span>
                                Modèle 1
                            </a>
                            <a class="btn btn-outline-info" href="{% url 'exams:generate_reports_for_level' %}?level={{ level.id }}&generic_term={{ term }}&report_type=RW">
                                <span data-feather="download" class="feather-16 align-middle"></span>
                                Modèle 2
                            </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </td>
        {% endif %}
    </tr>
    {% endfor %}
    </tbody>
</table>
</div>

{% if request.GET.option == 'bulletins' %}
<!-- <div class="card mt-4 border-left-warning shadow-sm">
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h5 class="card-title">
                    <span data-feather="refresh-cw" class="feather-20 align-middle mr-2"></span>
                    Actualiser tous les bulletins
                </h5>
                <p class="card-text text-muted">
                    Si vous souhaitez générer tous les bulletins à la fois pour pouvoir les télécharger par classe, veuillez
                    cliquer sur le bouton "Actualiser les bulletins", puis patienter. Cette opération peut prendre du temps en fonction du nombre d'élèves.
                </p>
            </div>
            <div class="col-md-4 text-md-right">
                <a href="" class="btn btn-warning btn-lg"
                   hx-get="{% url 'exams:generate_reports' %}?lang={{ lang }}"
                   hx-target="#progress_container"
                   hx-indicator="#generate-spinner"
                   id="submit-btn">
                    <span data-feather="refresh-cw" class="feather-16 align-middle mr-1"></span>
                    <span id="generate-spinner" class="htmx-indicator spinner-border spinner-border-sm mr-1" role="status"></span>
                    Actualiser les bulletins
                </a>
            </div>
        </div>
        <div id="progress_container" class="mt-3"></div>
    </div>
</div> -->
{% endif %}

<script>
    // Initialize DataTable with better configuration
    $(document).ready(function() {
        $.fn.dataTable.ext.errMode = 'none';

        const table = $('#table').DataTable({
            lengthMenu: [[10, 25, 50, 100, -1], ['10', '25', '50', '100', 'Tous']],
            pageLength: 25,
            language: {
                search: "Rechercher:",
                lengthMenu: "Afficher _MENU_ classes",
                info: "Affichage de _START_ à _END_ sur _TOTAL_ classes",
                infoEmpty: "Aucune classe à afficher",
                infoFiltered: "(filtré à partir de _MAX_ classes au total)",
                paginate: {
                    first: "Premier",
                    last: "Dernier",
                    next: "Suivant",
                    previous: "Précédent"
                }
            },
            dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
                 '<"row"<"col-sm-12"tr>>' +
                 '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
            drawCallback: function() {
                htmx.process(document.body.querySelector('#table'));
                feather.replace();
            }
        });

        // Add a custom search delay to improve performance
        let searchTimeout;
        $('.dataTables_filter input').unbind().bind('input', function() {
            const self = this;
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(function() {
                table.search(self.value).draw();
            }, 300);
        });
    });

    // Initialize HTMX and Feather icons
    htmx.process(document.body);
    feather.replace();
</script>