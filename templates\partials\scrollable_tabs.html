{% if request.htmx %}
    <title>{% if page_title %} {{ page_title }} {% elif description %} {{ description }} {% elif subtitle %} {{ subtitle }} {% elif title %} {{ title }} {% else %} {{ item.description|default:'Gestion des Ecoles' }} {% endif %} | EcolePro</title>
{% endif %}

{% if nav_items %}
<div class="nav-tabs-container">
    <!-- Left scroll button -->
    <button class="scroll-button scroll-button-left" id="scrollLeft" aria-label="Scroll left">
        <i data-feather="chevron-left" class="feather-16"></i>
    </button>

    <!-- Scrollable tabs container -->
    <div class="nav-tabs-scroll">
        <ul class="nav nav-tabs">
            {% for item in nav_items %}
                <li class="nav-item">
                    <a class="nav-link {% if item.active_nav == active_nav %}active{% endif %}"
                        href="{{ item.url }}"
                        hx-get="{{ item.url }}"
                        hx-target="#app-content">
                        <span data-feather="{{ item.icon|default:'chevron-right' }}" class="feather-16 align-middle mr-1"></span>
                        <span>{{ item.description }}</span>
                    </a>
                </li>
            {% endfor %}
            {% include 'partials/indicator.html' %}
        </ul>
    </div>

    <!-- Right scroll button -->
    <button class="scroll-button scroll-button-right" id="scrollRight" aria-label="Scroll right">
        <i data-feather="chevron-right" class="feather-16"></i>
    </button>
</div>

<script>
    if (typeof(feather) !== "undefined") {
        feather.replace();
    }

    document.addEventListener('DOMContentLoaded', function() {
        // Use jQuery for better cross-browser compatibility
        const $scrollContainer = $('.nav-tabs-scroll');
        const $scrollLeftBtn = $('#scrollLeft');
        const $scrollRightBtn = $('#scrollRight');
        const scrollAmount = 200; // Pixels to scroll each time

        // Helper function to update button states
        function updateButtonStates() {
            // Check if we can scroll left
            if ($scrollContainer.scrollLeft() <= 0) {
                $scrollLeftBtn.addClass('disabled');
            } else {
                $scrollLeftBtn.removeClass('disabled');
            }

            // Check if we can scroll right
            const maxScrollLeft = $scrollContainer[0].scrollWidth - $scrollContainer.outerWidth();
            if ($scrollContainer.scrollLeft() >= maxScrollLeft - 5) { // 5px tolerance for rounding
                $scrollRightBtn.addClass('disabled');
            } else {
                $scrollRightBtn.removeClass('disabled');
            }
        }

        // Initial button state
        updateButtonStates();

        // Left scroll button click
        $scrollLeftBtn.on('click', function() {
            if (!$(this).hasClass('disabled')) {
                $scrollContainer.animate({
                    scrollLeft: $scrollContainer.scrollLeft() - scrollAmount
                }, 200, updateButtonStates);
            }
        });

        // Right scroll button click
        $scrollRightBtn.on('click', function() {
            if (!$(this).hasClass('disabled')) {
                $scrollContainer.animate({
                    scrollLeft: $scrollContainer.scrollLeft() + scrollAmount
                }, 200, updateButtonStates);
            }
        });

        // Update button states when scrolling manually
        $scrollContainer.on('scroll', function() {
            updateButtonStates();
        });

        // Update button states on window resize
        $(window).on('resize', function() {
            updateButtonStates();
        });

        // Make the active tab visible when page loads
        const $activeTab = $('.nav-link.active');
        if ($activeTab.length) {
            const $tabItem = $activeTab.parent();
            const containerWidth = $scrollContainer.width();
            const tabOffset = $tabItem.offset().left;
            const scrollLeft = $scrollContainer.scrollLeft();
            const containerOffset = $scrollContainer.offset().left;
            const tabWidth = $tabItem.width();

            // Calculate the target scroll position to center the tab
            const targetScrollLeft = scrollLeft + tabOffset - containerOffset - (containerWidth / 2) + (tabWidth / 2);

            // Animate scroll to make the active tab visible
            $scrollContainer.animate({
                scrollLeft: targetScrollLeft
            }, 200, updateButtonStates);
        }

        // Handle tab clicks to ensure the clicked tab is visible
        $('.nav-link').on('click', function() {
            const $this = $(this);
            const $tabItem = $this.parent();
            const containerWidth = $scrollContainer.width();
            const tabOffset = $tabItem.offset().left;
            const scrollLeft = $scrollContainer.scrollLeft();
            const containerOffset = $scrollContainer.offset().left;
            const tabWidth = $tabItem.width();

            // Calculate the target scroll position to center the tab
            const targetScrollLeft = scrollLeft + tabOffset - containerOffset - (containerWidth / 2) + (tabWidth / 2);

            // Animate scroll to make the active tab visible
            $scrollContainer.animate({
                scrollLeft: targetScrollLeft
            }, 200, updateButtonStates);
        });
    });
</script>
{% endif %}
