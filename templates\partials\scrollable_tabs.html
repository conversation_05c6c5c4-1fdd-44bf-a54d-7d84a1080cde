{% if request.htmx %}
    <title>{% if page_title %} {{ page_title }} {% elif description %} {{ description }} {% elif subtitle %} {{ subtitle }} {% elif title %} {{ title }} {% else %} {{ item.description|default:'Gestion des Ecoles' }} {% endif %} | EcolePro</title>
{% endif %}

{% if nav_items %}
<div class="nav-tabs-container">
    <!-- Left scroll button -->
    <button class="scroll-button scroll-button-left" id="scrollLeft">
        <i data-feather="chevron-left" class="feather-16"></i>
    </button>
    
    <!-- Scrollable tabs container -->
    <div class="nav-tabs-scroll">
        <ul class="nav nav-tabs">
            {% for item in nav_items %}
                <li class="nav-item">
                    <a class="nav-link {% if item.active_nav == active_nav %}active{% endif %}" 
                        href="{{ item.url }}" 
                        hx-get="{{ item.url }}" 
                        hx-target="#app-content">
                        <span data-feather="{{ item.icon|default:'chevron-right' }}" class="feather-16 align-middle"></span> 
                        {{ item.description }}
                    </a>
                </li>
            {% endfor %}
            {% include 'partials/indicator.html' %}
        </ul>
    </div>
    
    <!-- Right scroll button -->
    <button class="scroll-button scroll-button-right" id="scrollRight">
        <i data-feather="chevron-right" class="feather-16"></i>
    </button>
</div>

<script>
    if (typeof(feather) !== "undefined") {
        feather.replace();
    }
    
    document.addEventListener('DOMContentLoaded', function() {
        const scrollContainer = document.querySelector('.nav-tabs-scroll');
        const scrollLeftBtn = document.getElementById('scrollLeft');
        const scrollRightBtn = document.getElementById('scrollRight');
        const scrollAmount = 100; // Pixels to scroll each time
        
        // Helper function to update button states
        function updateButtonStates() {
            // Check if we can scroll left
            if (scrollContainer.scrollLeft <= 0) {
                scrollLeftBtn.classList.add('disabled');
            } else {
                scrollLeftBtn.classList.remove('disabled');
            }
            
            // Check if we can scroll right
            const maxScrollLeft = scrollContainer.scrollWidth - scrollContainer.offsetWidth;
            if (scrollContainer.scrollLeft >= maxScrollLeft - 5) { // 5px tolerance for rounding
                scrollRightBtn.classList.add('disabled');
            } else {
                scrollRightBtn.classList.remove('disabled');
            }
        }
        
        // Initial button state
        updateButtonStates();
        
        // Left scroll button click
        scrollLeftBtn.addEventListener('click', function() {
            if (!this.classList.contains('disabled')) {
                scrollContainer.scrollLeft -= scrollAmount;
                setTimeout(updateButtonStates, 100);
            }
        });
        
        // Right scroll button click
        scrollRightBtn.addEventListener('click', function() {
            if (!this.classList.contains('disabled')) {
                scrollContainer.scrollLeft += scrollAmount;
                setTimeout(updateButtonStates, 100);
            }
        });
        
        // Update button states when scrolling manually
        scrollContainer.addEventListener('scroll', function() {
            updateButtonStates();
        });
        
        // Update button states on window resize
        window.addEventListener('resize', function() {
            updateButtonStates();
        });
        
        // Make the active tab visible when page loads
        const activeTab = document.querySelector('.nav-link.active');
        if (activeTab) {
            const tabItem = activeTab.parentElement;
            const containerWidth = scrollContainer.offsetWidth;
            const tabOffset = tabItem.offsetLeft;
            const tabWidth = tabItem.offsetWidth;
            
            // Calculate the target scroll position to center the tab
            const targetScrollLeft = tabOffset - (containerWidth / 2) + (tabWidth / 2);
            
            // Set scroll to make the active tab visible
            scrollContainer.scrollLeft = targetScrollLeft;
            setTimeout(updateButtonStates, 100);
        }
    });
</script>
{% endif %}
