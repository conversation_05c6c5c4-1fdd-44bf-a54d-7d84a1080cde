{% load static %}
{% load pwa %}
{% load user_tags %}
<!DOCTYPE html>
<html lang="fr">
  <head>
    <meta name="description" content="EcolePro est une application web progressive de gestion des écoles laïques et islamiques">
    <meta name="keywords" content="EcolePro, application, gestion, école, moyennes">
    <!-- Open Graph Meta-->
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="EcolePro: Application de Gestion des Ecoles">
    <meta property="og:title" content="EcolePro: Gestion des Ecoles">
    <meta property="og:url" content="https://www.ecolepro.net/">
    <meta property="og:image" content="{% static 'img/favicon.png' %}">
    <meta property="og:description" content="EcolePro est une application de gestion des écoles en Côte d'Ivoire">
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{% block page_title %}Accueil | EcolePro{% endblock %}</title>
    <script src="https://cdn.jsdelivr.net/npm/pace-js@latest/pace.min.js"></script>
    <link rel="shortcut icon" href="{% static 'img/favicon.png' %}" type="image/x-icon">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/node-waves/0.7.6/waves.min.css">
    <link rel="stylesheet" type="text/css" href="{% static 'css/main.css' %}">
    <link rel="stylesheet" type="text/css" href="{% static 'css/dark-mode.css' %}">
    <link rel="stylesheet" type="text/css" href="{% static 'css/scrollable-tabs.css' %}">
    <link href="https://cdn.datatables.net/v/bs4/dt-1.13.8/rr-1.4.1/datatables.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">

    {% progressive_web_app_meta %}
  </head>
  <body class="app sidebar-mini rtl" id="body" hx-indicator="#htmx-indicator" hx-history-elt
    hx-on="
        htmx:beforeRequest: if (event.detail.target.id == '') $('#submit-btn').prop('disabled', true);
        htmx:afterRequest: Waves.attach('a.btn, li, .ripple', ['waves-light']); Waves.init(); if (event.detail.target.id == '') $('#submit-btn').prop('disabled', false);
    "
    >
    {% with PLAN_LEVEL=request.session.PLAN_LEVEL school_plan=request.session|get_user_plan:user %}
    <div class="loader-wrapper">
      <div class="loader m-auto"></div>
    </div>
    {% block body %}
    {% include 'partials/navigation.html' %}
    <main class="app-content margin-b-on-phone" id="app-content">
      {% block content %} {% endblock content %}
    </main>
    {% endblock body %}

    {% if user.is_authenticated %}
      {% if user.role != 'TC' or perms.school.add_student %}
        {% include 'partials/bottom-nav.html' with school_plan=school_plan PLAN_LEVEL=PLAN_LEVEL %}
      {% endif %}
    {% endif %}
    <div class="modal fade" tabindex="-1" id="modal"
        tabindex="-1">
      <div class="modal-dialog modal-lg" id="dialog" hx-target="this">
      </div>
    </div>
    <div class="modal fade" tabindex="-1" id="modal-xl"
        data-backdrop="static" data-keyboard="false"
        tabindex="-1" aria-labelledby="staticBackdropLabel">
      <div class="modal-dialog modal-xl" id="dialog-xl" hx-target="this">
      </div>
    </div>
    {% endwith %}
    {% include 'partials/offcanvas.html' %}
    {% include 'partials/indicator.html' with d_none=True %}
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.5.1/dist/jquery.slim.min.js"
	    integrity="sha384-DfXdz2htPH0lsSSs5nCTpuj/zy4C+OGpamoFVy38MVBnE+IbbVYUew+OrCXaRkfj"
	     crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"
            integrity="sha384-9/reFTGAW83EW2RDu2S0VKaIzap3H66lZH81PoYlFhbGU+6BZp6G7niu735Sk7lN"
            crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.min.js"
            integrity="sha384-+sLIOodYLS7CIrQpBjl+C7nPvqq+FbNUBDunl/OZv93DB7Ln/533i8e/mZXLi/P+"
            crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/feather-icons/dist/feather.min.js"></script>
    <script src="https://cdn.datatables.net/v/bs4/dt-1.13.8/rr-1.4.1/datatables.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/node-waves/0.7.6/waves.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.lazyload/1.9.1/jquery.lazyload.min.js" defer></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr" defer></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/fr.js" defer></script>
    <script src="{% static 'js/jquery.simple-checkbox-table.min.js' %}" defer></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="{% static 'celery_progress/celery_progress.js' %}" defer></script>
    <script src="{% static 'js/htmx.min.js' %}"></script>
    <script src="{% static 'js/main.js' %}"></script>
    <script src="{% static 'js/dark-mode.js' %}"></script>
    <script>
jQuery(document).ready(function($){
	$(document).on('click', '.pull-bs-canvas-right, .pull-bs-canvas-left', function(){
		$('body').prepend('<div class="bs-canvas-overlay bg-dark position-fixed w-100 h-100"></div>');
		if($(this).hasClass('pull-bs-canvas-right'))
			$('.bs-canvas-right').addClass('mr-0');
		else
			$('.bs-canvas-left').addClass('ml-0');
		return false;
	});

	$(document).on('click', '.bs-canvas-close, .bs-canvas-overlay', function(){
		var elm = $(this).hasClass('bs-canvas-close') ? $(this).closest('.bs-canvas') : $('.bs-canvas');
		elm.removeClass('mr-0 ml-0');
		$('.bs-canvas-overlay').remove();
		return false;
	});
});

$('#sidebarToggleBtn').on('click', function() {
  setTimeout(function() {
    if (document.body.classList.contains('sidenav-toggled')) {
      localStorage.setItem('sidebarStatus', 'toggled');
    } else {
      localStorage.setItem('sidebarStatus', 'shown');
    }
  }, 500)
})

if (window.innerWidth >= 768) {
    if (localStorage.getItem('sidebarStatus') === 'toggled') {
      document.body.classList.add('sidenav-toggled')
    }
}

    </script>
    {% block javascript %} {% endblock %}
    {% load sweetify %} {% sweetify %}
  </body>
</html>