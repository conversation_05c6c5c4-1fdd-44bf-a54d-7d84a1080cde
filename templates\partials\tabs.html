{% if request.htmx %}
    <title>{% if page_title %} {{ page_title }} {% elif description %} {{ description }} {% elif subtitle %} {{ subtitle }} {% elif title %} {{ title }} {% else %} {{ item.description|default:'Gestion des Ecoles' }} {% endif %} | EcolePro</title>
{% endif %}

{% if nav_items %}
<style>
.scrollable-tabs-container {
    position: relative;
    width: 100%;
    overflow: hidden;
    background: white;
    border-bottom: 1px solid #dee2e6;
}

.scrollable-tabs-wrapper {
    display: flex;
    align-items: center;
    position: relative;
}

.scroll-button {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid #dee2e6;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    opacity: 0;
    visibility: hidden;
}

.scroll-button:hover {
    background: #f8f9fa;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-50%) scale(1.05);
}

.scroll-button.visible {
    opacity: 1;
    visibility: visible;
}

.scroll-button-left {
    left: 8px;
}

.scroll-button-right {
    right: 8px;
}

.tabs-scroll-area {
    overflow-x: hidden;
    overflow-y: hidden;
    scroll-behavior: smooth;
    padding: 0 50px;
    margin: 0 -50px;
}

.nav-tabs-scrollable {
    display: flex;
    flex-wrap: nowrap;
    border-bottom: none;
    min-width: max-content;
    transition: transform 0.3s ease;
    padding: 0 50px;
}

.nav-tabs-scrollable .nav-item {
    flex-shrink: 0;
    white-space: nowrap;
    border: none;
}

.nav-tabs-scrollable .nav-link {
    border: 1px solid transparent;
    border-bottom: 3px solid transparent;
    padding: 12px 20px;
    margin-right: 4px;
    border-radius: 8px 8px 0 0;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #6c757d;
    font-weight: 500;
}

.nav-tabs-scrollable .nav-link:hover {
    border-color: #e9ecef #e9ecef #007bff;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    color: #007bff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
}

.nav-tabs-scrollable .nav-link.active {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    border-color: #007bff #007bff #007bff;
    border-bottom-color: #007bff;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 123, 255, 0.3);
}

.nav-tabs-scrollable .nav-link.active::before {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #28a745, #20c997);
    border-radius: 2px;
}

.nav-tabs-scrollable .nav-link .feather-16 {
    margin-right: 8px;
    transition: transform 0.3s ease;
}

.nav-tabs-scrollable .nav-link:hover .feather-16 {
    transform: scale(1.1);
}

.nav-tabs-scrollable .nav-link.active .feather-16 {
    transform: scale(1.1);
}

/* Fade edges for visual indication of scrollable content */
.tabs-scroll-area::before,
.tabs-scroll-area::after {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    width: 20px;
    pointer-events: none;
    z-index: 5;
    transition: opacity 0.3s ease;
}

.tabs-scroll-area::before {
    left: 50px;
    background: linear-gradient(to right, rgba(255, 255, 255, 0.8), transparent);
    opacity: 0;
}

.tabs-scroll-area::after {
    right: 50px;
    background: linear-gradient(to left, rgba(255, 255, 255, 0.8), transparent);
    opacity: 0;
}

.tabs-scroll-area.show-left-fade::before {
    opacity: 1;
}

.tabs-scroll-area.show-right-fade::after {
    opacity: 1;
}

/* Animation for tab switching */
@keyframes tabSlideIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.nav-tabs-scrollable .nav-link.active {
    animation: tabSlideIn 0.3s ease;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .tabs-scroll-area {
        padding: 0 40px;
        margin: 0 -40px;
    }

    .nav-tabs-scrollable {
        padding: 0 40px;
    }

    .scroll-button-left {
        left: 4px;
    }

    .scroll-button-right {
        right: 4px;
    }

    .nav-tabs-scrollable .nav-link {
        padding: 10px 16px;
        font-size: 14px;
    }
}
</style>

<div class="scrollable-tabs-container">
    <div class="scrollable-tabs-wrapper">
        <!-- Left scroll button -->
        <button class="scroll-button scroll-button-left" id="scroll-left" type="button">
            <span data-feather="chevron-left" class="feather-16"></span>
        </button>

        <!-- Right scroll button -->
        <button class="scroll-button scroll-button-right" id="scroll-right" type="button">
            <span data-feather="chevron-right" class="feather-16"></span>
        </button>

        <!-- Scrollable tabs area -->
        <div class="tabs-scroll-area" id="tabs-scroll-area">
            <ul class="nav nav-tabs-scrollable" id="scrollable-tabs">
                {% for item in nav_items %}
                    <li class="nav-item">
                        <a class="nav-link {% if item.active_nav == active_nav %}active{% endif %}"
                            href="{{ item.url }}"
                            hx-get="{{ item.url }}"
                            hx-target="#app-content">
                            <span data-feather="{{ item.icon|default:'chevron-right' }}" class="feather-16 align-middle"></span>
                            {{ item.description }}
                        </a>
                    </li>
                {% endfor %}
            </ul>
        </div>
    </div>
    {% include 'partials/indicator.html' %}
</div>

<script>
$(document).ready(function() {
    // Initialize scrollable tabs functionality
    function initScrollableTabs() {
        const scrollArea = document.getElementById('tabs-scroll-area');
        const tabsContainer = document.getElementById('scrollable-tabs');
        const leftButton = document.getElementById('scroll-left');
        const rightButton = document.getElementById('scroll-right');

        if (!scrollArea || !tabsContainer || !leftButton || !rightButton) {
            return;
        }

        let currentTranslateX = 0;
        const scrollStep = 200; // pixels to scroll per click

        // Check if scrolling is needed and update button visibility
        function updateScrollButtons() {
            const containerWidth = scrollArea.offsetWidth;
            const contentWidth = tabsContainer.scrollWidth;
            const maxScroll = contentWidth - containerWidth;

            // Show/hide buttons based on scroll need
            if (maxScroll <= 0) {
                leftButton.classList.remove('visible');
                rightButton.classList.remove('visible');
                scrollArea.classList.remove('show-left-fade', 'show-right-fade');
                return;
            }

            // Update button visibility
            leftButton.classList.toggle('visible', currentTranslateX < 0);
            rightButton.classList.toggle('visible', Math.abs(currentTranslateX) < maxScroll);

            // Update fade indicators
            scrollArea.classList.toggle('show-left-fade', currentTranslateX < 0);
            scrollArea.classList.toggle('show-right-fade', Math.abs(currentTranslateX) < maxScroll);
        }

        // Smooth scroll function
        function scrollTabs(direction) {
            const containerWidth = scrollArea.offsetWidth;
            const contentWidth = tabsContainer.scrollWidth;
            const maxScroll = -(contentWidth - containerWidth);

            if (direction === 'left') {
                currentTranslateX = Math.min(currentTranslateX + scrollStep, 0);
            } else {
                currentTranslateX = Math.max(currentTranslateX - scrollStep, maxScroll);
            }

            // Apply smooth transform
            tabsContainer.style.transform = `translateX(${currentTranslateX}px)`;

            // Update button states after animation
            setTimeout(updateScrollButtons, 100);
        }

        // Auto-scroll to active tab
        function scrollToActiveTab() {
            const activeTab = tabsContainer.querySelector('.nav-link.active');
            if (!activeTab) return;

            const tabRect = activeTab.getBoundingClientRect();
            const containerRect = scrollArea.getBoundingClientRect();
            const tabsRect = tabsContainer.getBoundingClientRect();

            // Calculate if active tab is visible
            const tabLeft = tabRect.left - containerRect.left;
            const tabRight = tabRect.right - containerRect.left;
            const containerWidth = containerRect.width;

            // If tab is not fully visible, scroll to center it
            if (tabLeft < 50 || tabRight > containerWidth - 50) {
                const tabCenter = tabRect.left + tabRect.width / 2 - containerRect.left;
                const containerCenter = containerWidth / 2;
                const scrollOffset = tabCenter - containerCenter;

                currentTranslateX = Math.max(
                    Math.min(-scrollOffset, 0),
                    -(tabsContainer.scrollWidth - containerWidth)
                );

                tabsContainer.style.transform = `translateX(${currentTranslateX}px)`;
                setTimeout(updateScrollButtons, 100);
            }
        }

        // Event listeners
        leftButton.addEventListener('click', () => scrollTabs('left'));
        rightButton.addEventListener('click', () => scrollTabs('right'));

        // Handle window resize
        window.addEventListener('resize', () => {
            // Reset position on resize
            currentTranslateX = 0;
            tabsContainer.style.transform = 'translateX(0px)';
            setTimeout(() => {
                updateScrollButtons();
                scrollToActiveTab();
            }, 100);
        });

        // Handle tab clicks for smooth active tab centering
        tabsContainer.addEventListener('click', (e) => {
            const clickedTab = e.target.closest('.nav-link');
            if (clickedTab) {
                // Small delay to allow HTMX to update active state
                setTimeout(scrollToActiveTab, 200);
            }
        });

        // Mouse wheel scrolling (optional enhancement)
        scrollArea.addEventListener('wheel', (e) => {
            if (e.deltaX !== 0) {
                e.preventDefault();
                const direction = e.deltaX > 0 ? 'right' : 'left';
                scrollTabs(direction);
            }
        });

        // Touch/swipe support for mobile
        let touchStartX = 0;
        let touchEndX = 0;

        scrollArea.addEventListener('touchstart', (e) => {
            touchStartX = e.changedTouches[0].screenX;
        });

        scrollArea.addEventListener('touchend', (e) => {
            touchEndX = e.changedTouches[0].screenX;
            const swipeDistance = touchStartX - touchEndX;

            if (Math.abs(swipeDistance) > 50) { // Minimum swipe distance
                const direction = swipeDistance > 0 ? 'right' : 'left';
                scrollTabs(direction);
            }
        });

        // Initial setup
        setTimeout(() => {
            updateScrollButtons();
            scrollToActiveTab();
        }, 100);

        // Re-initialize when HTMX updates content
        document.addEventListener('htmx:afterSwap', () => {
            setTimeout(() => {
                updateScrollButtons();
                scrollToActiveTab();
            }, 100);
        });
    }

    // Initialize feather icons and scrollable tabs
    if (typeof(feather) !== "undefined") {
        feather.replace();
    }

    initScrollableTabs();
});
</script>
{% endif %}