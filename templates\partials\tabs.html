{% if request.htmx %}
    <title>{% if page_title %} {{ page_title }} {% elif description %} {{ description }} {% elif subtitle %} {{ subtitle }} {% elif title %} {{ title }} {% else %} {{ item.description|default:'Gestion des Ecoles' }} {% endif %} | EcolePro</title>
{% endif %}

{% if nav_items %}
<style>
.scrollable-tabs-container {
    position: relative;
    width: 100%;
    overflow: hidden;
}

.tabs-scroll-area {
    overflow-x: hidden;
    overflow-y: hidden;
    scroll-behavior: smooth;
}

.nav-tabs-scrollable {
    display: flex;
    flex-wrap: nowrap;
    min-width: max-content;
    transition: transform 0.3s ease;
}

.nav-tabs-scrollable .nav-item {
    flex-shrink: 0;
    white-space: nowrap;
}

.scroll-button {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 40px;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background: linear-gradient(to right, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7), transparent);
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease;
    border: none;
}

.scroll-button:hover {
    background: linear-gradient(to right, rgba(248, 249, 250, 0.95), rgba(248, 249, 250, 0.8), transparent);
}

.scroll-button.visible {
    opacity: 1;
    visibility: visible;
}

.scroll-button-left {
    left: 0;
    background: linear-gradient(to right, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7), transparent);
}

.scroll-button-right {
    right: 0;
    background: linear-gradient(to left, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7), transparent);
}

.scroll-button-right:hover {
    background: linear-gradient(to left, rgba(248, 249, 250, 0.95), rgba(248, 249, 250, 0.8), transparent);
}

.scroll-button .feather-16 {
    color: #6c757d;
    transition: color 0.3s ease;
}

.scroll-button:hover .feather-16 {
    color: #495057;
}
</style>

<div class="scrollable-tabs-container">
    <!-- Left scroll button -->
    <button class="scroll-button scroll-button-left" id="scroll-left" type="button">
        <span data-feather="chevron-left" class="feather-16"></span>
    </button>

    <!-- Right scroll button -->
    <button class="scroll-button scroll-button-right" id="scroll-right" type="button">
        <span data-feather="chevron-right" class="feather-16"></span>
    </button>

    <!-- Scrollable tabs area -->
    <div class="tabs-scroll-area" id="tabs-scroll-area">
        <ul class="nav nav-tabs nav-tabs-scrollable" id="scrollable-tabs">
            {% for item in nav_items %}
                <li class="nav-item">
                    <a class="nav-link {% if item.active_nav == active_nav %}active{% endif %}"
                        href="{{ item.url }}"
                        hx-get="{{ item.url }}"
                        hx-target="#app-content">
                        <span data-feather="{{ item.icon|default:'chevron-right' }}" class="feather-16 align-middle"></span> {{ item.description }}
                    </a>
                </li>
            {% endfor %}
        </ul>
    </div>
    {% include 'partials/indicator.html' %}
</div>
{% endif %}

<script>
$(document).ready(function() {
    // Initialize scrollable tabs functionality
    function initScrollableTabs() {
        const scrollArea = document.getElementById('tabs-scroll-area');
        const tabsContainer = document.getElementById('scrollable-tabs');
        const leftButton = document.getElementById('scroll-left');
        const rightButton = document.getElementById('scroll-right');

        if (!scrollArea || !tabsContainer || !leftButton || !rightButton) {
            return;
        }

        let currentTranslateX = 0;
        const scrollStep = 200; // pixels to scroll per click

        // Check if scrolling is needed and update button visibility
        function updateScrollButtons() {
            const containerWidth = scrollArea.offsetWidth;
            const contentWidth = tabsContainer.scrollWidth;
            const maxScroll = contentWidth - containerWidth;

            // Show/hide buttons based on scroll need
            if (maxScroll <= 0) {
                leftButton.classList.remove('visible');
                rightButton.classList.remove('visible');
                return;
            }

            // Update button visibility
            leftButton.classList.toggle('visible', currentTranslateX < 0);
            rightButton.classList.toggle('visible', Math.abs(currentTranslateX) < maxScroll);
        }

        // Smooth scroll function
        function scrollTabs(direction) {
            const containerWidth = scrollArea.offsetWidth;
            const contentWidth = tabsContainer.scrollWidth;
            const maxScroll = -(contentWidth - containerWidth);

            if (direction === 'left') {
                currentTranslateX = Math.min(currentTranslateX + scrollStep, 0);
            } else {
                currentTranslateX = Math.max(currentTranslateX - scrollStep, maxScroll);
            }

            // Apply smooth transform
            tabsContainer.style.transform = `translateX(${currentTranslateX}px)`;

            // Update button states after animation
            setTimeout(updateScrollButtons, 100);
        }

        // Auto-scroll to active tab
        function scrollToActiveTab() {
            const activeTab = tabsContainer.querySelector('.nav-link.active');
            if (!activeTab) return;

            const tabRect = activeTab.getBoundingClientRect();
            const containerRect = scrollArea.getBoundingClientRect();

            // Calculate if active tab is visible
            const tabLeft = tabRect.left - containerRect.left;
            const tabRight = tabRect.right - containerRect.left;
            const containerWidth = containerRect.width;

            // If tab is not fully visible, scroll to center it
            if (tabLeft < 40 || tabRight > containerWidth - 40) {
                const tabCenter = tabRect.left + tabRect.width / 2 - containerRect.left;
                const containerCenter = containerWidth / 2;
                const scrollOffset = tabCenter - containerCenter;

                currentTranslateX = Math.max(
                    Math.min(-scrollOffset, 0),
                    -(tabsContainer.scrollWidth - containerWidth)
                );

                tabsContainer.style.transform = `translateX(${currentTranslateX}px)`;
                setTimeout(updateScrollButtons, 100);
            }
        }

        // Event listeners
        leftButton.addEventListener('click', () => scrollTabs('left'));
        rightButton.addEventListener('click', () => scrollTabs('right'));

        // Handle window resize
        window.addEventListener('resize', () => {
            // Reset position on resize
            currentTranslateX = 0;
            tabsContainer.style.transform = 'translateX(0px)';
            setTimeout(() => {
                updateScrollButtons();
                scrollToActiveTab();
            }, 100);
        });

        // Handle tab clicks for smooth active tab centering
        tabsContainer.addEventListener('click', (e) => {
            const clickedTab = e.target.closest('.nav-link');
            if (clickedTab) {
                // Small delay to allow HTMX to update active state
                setTimeout(scrollToActiveTab, 200);
            }
        });

        // Mouse wheel scrolling
        scrollArea.addEventListener('wheel', (e) => {
            if (e.deltaX !== 0) {
                e.preventDefault();
                const direction = e.deltaX > 0 ? 'right' : 'left';
                scrollTabs(direction);
            }
        });

        // Touch/swipe support for mobile
        let touchStartX = 0;
        let touchEndX = 0;

        scrollArea.addEventListener('touchstart', (e) => {
            touchStartX = e.changedTouches[0].screenX;
        });

        scrollArea.addEventListener('touchend', (e) => {
            touchEndX = e.changedTouches[0].screenX;
            const swipeDistance = touchStartX - touchEndX;

            if (Math.abs(swipeDistance) > 50) { // Minimum swipe distance
                const direction = swipeDistance > 0 ? 'right' : 'left';
                scrollTabs(direction);
            }
        });

        // Initial setup
        setTimeout(() => {
            updateScrollButtons();
            scrollToActiveTab();
        }, 100);

        // Re-initialize when HTMX updates content
        document.addEventListener('htmx:afterSwap', () => {
            setTimeout(() => {
                updateScrollButtons();
                scrollToActiveTab();
            }, 100);
        });
    }

    // Initialize feather icons and scrollable tabs
    if (typeof(feather) !== "undefined") {
        feather.replace();
    }

    initScrollableTabs();
});
</script>