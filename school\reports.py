import datetime
import os
import ssl
import arabic_reshaper
from bidi.algorithm import get_display
from django.http import FileResponse
from django.db.models.aggregates import Sum, Count 
from django.db.models import F, IntegerField
from django.db.models.functions import Coalesce
from fpdf import FPDF
from fpdf.fonts import FontFace
from school.school_utils import get_fees
from main import utils as main_utils
from django.db.models import Q
from .school_utils import get_fees
from .models import Payment, SalaryPaymentOptions


def reshape_text(text):
    """Properly Reformats arabic text """
    return get_display(arabic_reshaper.reshape(text))


def get_french_headers(cycle, school, position='L'):
    if position == 'L':
        if school.left_header:
            return school.left_header
        left_text = "Ministère de l'Education Nationale \net de l'Alphabétisation\n" 
        if cycle and cycle == main_utils.CYCLE_PRIMARY:
            left_text += f"IEPP: {school.IEP or ''}\n"
            # left_text += f"Secteur pédagogique: ** {school.secteur_p or ''}**\n"
            left_text += f"** {school.get_name(cycle)}**\n"
        elif cycle and cycle == main_utils.CYCLE_SECONDARY:
            left_text += f"DRENA: {school.location.dren}\n"
            left_text += f"** {school.get_name(cycle)}" + \
                        (f"({school.code or ''})" if school.code else '') + "**\n"
        left_text += f"Contacts: {school.phone1 or ''} / {school.phone2 or ''}"
        return left_text
    elif position == 'R':
        if school.right_header:
            return school.right_header
        right_text ="République de Côte d'Ivoire\n"
        right_text += "Union - Discipline - Travail"
        return right_text

def get_arabic_headers(cycle, school, position='L', reshaped=True):
    if position == 'L':
        if school.left_header:
            return school.left_header
        left_text = "République de Côte d'Ivoire \nUnion-Discipline-Travail\n" 
        left_text += f"**{school.get_name(cycle)}**\n"
        if cycle == main_utils.CYCLE_PRIMARY:
            left_text += f"IEPP : {school.IEP or ' '}\n"
            # left_text += f"Secteur pédagogique: ** {school.secteur_p or ' '} **\n"
        else:
            left_text += f"DRENA: ** {school.location.dren} **\n"
        left_text += f"Contacts: {school.phone1 or ''} {'/ ' + school.phone2 if school.phone2 else ' '}"
        return left_text
    elif position == 'R':
        if school.right_header:
            if reshaped:
                return reshape_text(school.right_header)
            return school.right_header
        
        right_text = ''
        if reshaped:
            right_text = reshape_text("جمهورية ساحل العاج") + "\n" 
            right_text += reshape_text("اتحاد - نظام - عمل") + "\n"

            right_text +="**" + (
                reshape_text(school.translation or '') or school.get_name(cycle)) + "**\n"
        else:
            right_text = "جمهورية ساحل العاج" + "\n" 
            right_text += "اتحاد - نظام - عمل" + "\n"
            right_text +="**" + (school.translation or school.get_name(cycle) or '') + "**\n"
        return right_text


class Document(FPDF):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.add_font('TimesNewRoman', '', 'static/fonts/times.ttf', uni=True)
        self.add_font('TimesNewRoman', 'B', 'static/fonts/timesbd.ttf', uni=True)
        self.add_font('TimesNewRoman', 'BI', 'static/fonts/timesbi.ttf', uni=True)
        self.add_font('TimesNewRoman', 'I', 'static/fonts/timesi.ttf', uni=True)
        self.add_font('Arial', '', 'static/fonts/arial/arial.ttf', uni=True)
        self.add_font('Arial', 'B', 'static/fonts/arial/arialbd.ttf', uni=True)
        self.add_font('Arial', 'BI', 'static/fonts/arial/arialbi.ttf', uni=True)
        self.add_font('Arial', 'I', 'static/fonts/arial/ariali.ttf', uni=True)
        self.add_font('ArialNarrow', '', 'static/fonts/arial/ARIALN.TTF', uni=True)
        self.add_font('ArialNarrow', 'B', 'static/fonts/arial/ARIALNB.TTF', uni=True)
        self.add_font('ArialNarrow', 'BI', 'static/fonts/arial/ARIALNBI.TTF', uni=True)
        self.add_font('ArialNarrow', 'I', 'static/fonts/arial/ARIALNI.TTF', uni=True)
        self.add_font('Helv', '', 'static/fonts/helvetica/Helvetica.ttf', uni=True)
        self.add_font('Helv', 'B', 'static/fonts/helvetica/Helveticab.ttf', uni=True)
        self.add_font('Helv', 'I', 'static/fonts/helvetica/Helveticai.ttf', uni=True)
        self.add_font('Helv', 'BI', 'static/fonts/helvetica/Helveticabi.ttf', uni=True)
        self.set_font('Times', size=12)
        self.school = None
        self.file_path = None

    def reshape_text(self, text):
        return reshape_text(text)

    def set_font(self, family='Times', style= "", size = 0):
        if family.lower() == 'times':
            return super().set_font('TimesNewRoman', style, size)
        return super().set_font(family, style, size)
    
    def add_header_french_schools(self, cycle, x=20, y=10, small=False): 
        ln_height = 5
        font_size = 10

        if small:
            ln_height = 4
            font_size = 7.8

        self.set_xy(x, y)
        if self.cur_orientation == 'L':
            if not small:
                self.set_x(self.x + 15)
            else:
                self.set_x(self.x + 2)

        left_text = get_french_headers(cycle, self.school, 'L')

        self.set_font('Times', '', font_size)
        y = self.y 
        self.multi_cell(w=70, h=ln_height, txt=left_text, align='L', markdown=True)
        
        x = self.x
        if small:
            x = x - 15

        if self.cur_orientation == 'L' and not small:
            x += 20
        
        try:
            self.image(self.school.logo.url, w=20, h=20, x=x, y=y)
        except: 
            self.image('static/img/armoiries.png', w=20, h=20, x=x, y=y)

        right_text = get_french_headers(cycle, self.school, 'R')

        if not small:
            self.set_xy(x + 45, y)
            self.multi_cell(w=80, h=5, txt=right_text)
        else:
            self.set_xy(x + 30, y)
            self.set_font_size(9)
            self.multi_cell(w=40, h=5, txt=right_text)

    def add_header_arabic_schools(self, cycle, x=20, y=5, small=False, adjust_position=True): 
        ln_height = 5
        font_size = 9

        if small:
            ln_height = 4
            font_size = 8

        if not small and adjust_position:
            self.set_xy(10, y)
        else:
            self.set_xy(x, y)

        if self.cur_orientation == 'L' and adjust_position:
            if not small:
                self.set_x(10)
            else:
                self.set_x(self.x + 2)

        left_text = get_arabic_headers(cycle, self.school, 'L')

        self.set_font('Times', '', font_size)
        y = self.y 
        self.multi_cell(w=80 if not small else 70, 
                        h=ln_height, txt=left_text, 
                        align='C' if not small else 'L', 
                        markdown=True)
        
        x = self.x 
        
        if small:
            x = x - 15

        try:
            self.image(self.school.logo.url, w=20, h=20, x=x if small else (self.w / 2) - 10 , y=y)
        except: 
            self.image('static/img/armoiries.png', w=20, h=20, x=x if small else (self.w / 2) - 10, y=y)

        right_text = get_arabic_headers(cycle, self.school, 'R')
        
        if not small:
            self.set_xy(self.w - 90, y)
            self.set_font('Times', '', 11)
            self.multi_cell(w=80, h=5, txt=right_text, align='C', markdown=True)
        else:
            self.set_xy(x + 30, y)
            self.set_font('Times', '', 9)
            self.multi_cell(w=40, h=5, txt=right_text, align='R', markdown=True)

    def add_default_header(self, current_x, current_y, cycle=None, small=False, adjust_position=True):
        if self.school.education == main_utils.EDUCATION_FRENCH and cycle:
            self.add_header_french_schools(cycle, current_x, current_y, small=small)
        elif self.school.education == main_utils.EDUCATION_ARABIC:
            self.add_header_arabic_schools(
                cycle or main_utils.CYCLE_PRIMARY, current_x, 
                current_y, small=small, adjust_position=adjust_position)
        else:
            if self.school.logo:
                try:
                    self.image(self.school.logo.url, w=20, h=20, y=self.y - 5)
                    self.set_xy(current_x + 20, current_y)
                except: 
                    pass
            
            school = self.school
            text = "République de Côte d'Ivoire\n"
            text += "Union - Discipline - Travail\n" 
            text += "Ministère de l'éducation nationale et de l'Alphabétisation\n" 

            school_name = school.get_name(cycle)
            text += f"**{str(school_name).upper()}**"
            if school.phone1:
                text += f"\n{school.phone1} / {school.phone2 or ''}\n"

            if cycle and cycle == main_utils.CYCLE_SECONDARY:
                text += "Statut " + school.get_status_display()
            self.multi_cell(txt=text, align='LEFT', w=100, markdown=True)
        self.set_x(current_x)
           
    def add_header(self, school=None, divider=False, x=15, y=5, cycle=None, use_img=False):
        if self.cur_orientation == 'L':
            x += 40

        if not self.school and school:
            self.school = school
        if self.school:
            if use_img:
                path = f"{self.school.header_img.url if self.school.header_img else ''}" 
                self.set_xy(x, y)
                try: 
                    self.image(path, w=180, h=30)
                except:
                    self.add_default_header(current_x=x, current_y=y, cycle=cycle)
            else:
                self.add_default_header(current_x=x, current_y=y, cycle=cycle)

        if divider:
            self.line(20, 40, 190 if self.cur_orientation == 'P' else 270, 40)

    def get_file_response(self, filename='document', as_attachment=True):
        base_dir = 'media'
        if not os.path.exists(base_dir):
            os.mkdir(base_dir)

        pdf_path = os.path.join(base_dir, 'pdf')
        if not os.path.exists(pdf_path):
            os.mkdir(pdf_path)
        file_path = os.path.join(pdf_path, f'{filename}.pdf')
        self.output(file_path)
        return FileResponse(
                open(file_path, 'rb'), as_attachment=as_attachment, 
                content_type='application/pdf')
    
    def add_image_or_avatar(self, url, **kwargs):
        try:
            self.image(url, **kwargs)
        except:
            self.image('static/img/avatar.jpg', **kwargs)
    
    def add_image_or_gov_img_or_avatar(self, url, student_id, **kwargs):
        ssl._create_default_https_context = ssl._create_unverified_context
        if url:
            try:
                self.image(url, **kwargs)
            except:
                if student_id:
                    try:
                        url = f'https://agfne.sigfne.net/vas/picture-noprod/{student_id}'
                        self.image(url, **kwargs)
                    except:
                        self.image('static/img/avatar.jpg', **kwargs)
            return
        
        if student_id:
            try:
                url = f'https://agfne.sigfne.net/vas/picture-noprod/{student_id}'
                self.image(url, **kwargs)
            except:
                self.image('static/img/avatar.jpg', **kwargs)
            return
        else:
            self.image('static/img/avatar.jpg', **kwargs)
        
    def add_students_count_component(self, table, boys, girls, last=None, placeholders=None):
        if not placeholders:
            placeholders = {
                'title': 'EFFECTIFS',
                'first': 'G',
                'second': 'F',
                'third': 'T ',
            }
        row = table.row()
        row.cell(placeholders['title'], colspan=3)
        row = table.row()
        row.cell(placeholders['first'])
        row.cell(placeholders['second'])
        row.cell(placeholders['third'])
        row = table.row()
        row.cell(str(boys))
        row.cell(str(girls))
        total = boys + girls
        row.cell(str(last or total))

    def add_term_info_component(self, table, term):
        row = table.row()
        row.cell('PERIODE')
        row.cell(str(term))
        
        row = table.row()
        row.cell('DISCIPLINE')
        row.cell('')
        
        row = table.row()
        row.cell('PROF')
        row.cell('')
    
    def add_results_component(
            self, table, result_obj,
            students={'G': 0, 'F': 0}, 
            education=main_utils.EDUCATION_FRENCH):
        row = table.row()
        row.cell('')

        if education == main_utils.EDUCATION_FRENCH:
            row.cell('Garçons')
            row.cell('Filles')
            row.cell('Total')
        else:
            row.cell(self.reshape_text(main_utils.TRANSLATIONS_FR_AR['garcons']))
            row.cell(self.reshape_text(main_utils.TRANSLATIONS_FR_AR['filles']))
            row.cell(self.reshape_text(main_utils.TRANSLATIONS_FR_AR['total']))

        row = table.row()
        if education == main_utils.EDUCATION_FRENCH:
            row.cell('Effectif')
        else:
            row.cell(self.reshape_text(main_utils.TRANSLATIONS_FR_AR['effectif']))
        row.cell(str(students.get('G', 0)))
        row.cell(str(students.get('F', 0)))
        row.cell(str(students.get('G', 0) + students.get('F', 0)))
        
        row = table.row()
        if education == main_utils.EDUCATION_FRENCH:
            row.cell('Présents')
        else:
            row.cell(self.reshape_text(main_utils.TRANSLATIONS_FR_AR['presents']))
        row.cell(str(result_obj.boys_present))
        row.cell(str(result_obj.girls_present))
        row.cell(str(result_obj.boys_present + result_obj.girls_present))
        
        row = table.row()
        if education == main_utils.EDUCATION_FRENCH:
            row.cell('Admis')
        else:
            row.cell(self.reshape_text(main_utils.TRANSLATIONS_FR_AR['admis']))
        row.cell(str(result_obj.boys_admitted))
        row.cell(str(result_obj.girls_admitted))
        row.cell(str(result_obj.boys_admitted + result_obj.girls_admitted))
        
        row = table.row()
        if education == main_utils.EDUCATION_FRENCH:
            row.cell('% Admis')
        else:
            row.cell(self.reshape_text(main_utils.TRANSLATIONS_FR_AR['pourc_admis']))
        boys_perc = round((result_obj.boys_perc), 2)
        girls_perc = round((result_obj.girls_perc), 2)
        all_admitted = result_obj.boys_admitted + result_obj.girls_admitted
        all_present = result_obj.boys_present + result_obj.girls_present
        total_perc = round((all_admitted * 100) / (all_present or 1), 2)

        row.cell(str(boys_perc) + "%")
        row.cell(str(girls_perc) + "%")
        row.cell(str(total_perc) + "%")
    
    def get_headers_width(self, headers, rotated_items_height):
        self.total_width = 0
        for index, header in enumerate(headers):
            if index > 2 and index < len(headers) - 4:
                self.total_width += rotated_items_height
            else:
                for key in header:
                    self.total_width += header[key]['width']
        self.x_right_aligned = (self.w - self.total_width) / 2 + 5

    def add_summary_component(self, enrollment, terms_to_display, data, value_key, description, 
                              summary_data, max_value=None, max_key=None, value=None,
                              is_rank=False, is_distinction=False, is_decision=False, term_max_avg=None,
                              education=None, translation=''):
        to_append = [description]
        terms_data =  []
        reversed_terms_to_display = terms_to_display[:]

        if education == main_utils.EDUCATION_ARABIC:
            reversed_terms_to_display.reverse()

        for i, term_obj in enumerate(reversed_terms_to_display):
            value = enrollment.__dict__.get(f'{value_key}{i + 1}')
            if value and not main_utils.has_decimal_part(value):
                value = int(value)
            elif not value:
                value = '-'
            
            if value and value != '-' and not is_rank and not is_distinction and not is_decision:
                value = str(value) + (' / ' + str(max_value or summary_data.get(f'{max_key}{term_obj.term_id}'))) if (max_value or max_key) else f'{value}'
            elif is_rank and value and value != '-':
                is_ex = enrollment.__dict__.get(f'is_ex_for_term_{i + 1}')
                value = f"{main_utils.get_rank_str(value, bool(is_ex))} /{max_value}" 
            elif is_distinction and value and value != '-':
                value = main_utils.compute_distinction(
                    value, max=term_max_avg,
                    education=education,
                    return_language=education)
                if education == main_utils.EDUCATION_ARABIC:
                    value = self.reshape_text(value)
                elif len(terms_to_display) > 3:
                    value = str(value).replace('Assez-', 'A. ')
            elif is_decision and value and value != '-':
                decision = main_utils.get_decision(value, term_max_avg, True)
                if education == main_utils.EDUCATION_ARABIC:
                    value = main_utils.get_result_translation(enrollment.student.gender, bool(decision == 'A'), education)
                    value = self.reshape_text(value)
                else:
                    value = main_utils.get_decision(value, term_max_avg, education==main_utils.EDUCATION_FRENCH)
                    if value == 'A':
                        value = 'Admis'
                    else:
                        value = 'N. Admis'

            terms_data.append(value)

        if education == main_utils.EDUCATION_ARABIC:
            terms_data.reverse()
            
        to_append.extend(terms_data)
        if translation:
            to_append.append(self.reshape_text(translation))
        data.append(to_append)


        
class PaymentReport(Document):
    def add_report(self, enrollment, user, payments, total_fees):
        # Add document title
        self.set_font('Times', 'B', 14)
        self.cell(txt=f'RECU DE PAIEMENT {enrollment.year}', 
                  h=10, w=180, align='C', border=True)

        self.set_font('Times', 'BU', 14)
        y = self.y + 10
        self.set_xy(14, y)
        self.cell(txt="Informations sur l'élève", w=170, h=8)
        self.set_font('Times', '', 11)
        self.ln()
        self.set_x(6)
        student = enrollment.student
        birth_date = student.birth_date_str()
        text = f"""{' ' * 8}Matricule: **{student.student_id or student.identifier or ''} ** 
        Nom et Prénoms: **{str(student)[:25]}** 
        Né(e) le: **{birth_date}** à **{student.birth_place or ''}** 
        Sexe: **{student.gender}**          Classe: **{enrollment.level_fr or enrollment.generic_level_fr or ''}**
        Montant total à payer:  **{"{:,}".format(total_fees).replace(',', ' ')} F CFA**
        Contact parent: **{student.father_phone or student.mother_phone or student.phone or ''}**"""
        self.multi_cell(txt=text, w=130, markdown=True, h=6)

        photo_url = student.photo.url if student.photo else ''
        self.add_image_or_gov_img_or_avatar(
            photo_url, student.student_id, 
            x=170, y=self.y - 37, w=25, h=30, alt_text='photo')
        
        self.set_x(14)
        table_title_y = self.y
        self.set_font('Times', 'BU', 14)
        self.cell(txt='Liste des versements', w=120)
        self.set_font('Times', 'BU', 12)
        self.ln(3)
        
        # Add table
        total_amount = 0
        TABLE_DATA = [['N°', 'DATE', 'LIBELLE', 'MONTANT'],]
        for index, payment in enumerate(payments):
            total_amount += payment.get_total()
            payment_data = [
                str(index + 1).zfill(2),
                str(payment.created_at.strftime('%d/%m/%Y')), 
                str(payment.get_payment_type_display() if payment.payment_type else f"Paiement {index + 1}"), 
                "{:,}".format(payment.get_total()).replace(',', ' ') + ' F',
            ]
            TABLE_DATA.append(payment_data)
        
        if len(TABLE_DATA) < 6:
            difference = 6 - len(TABLE_DATA)
            for n in range(difference):
                payment_data = ['', '', '', '',]
                TABLE_DATA.append(payment_data)

        total_amount_str = "{:,}".format(total_amount).replace(',', ' ') + ' F'
        
        self.set_font('Times', size=11)
        self.set_xy(15, self.y + 5)
        greyscale = 200
        with self.table(
            width=120,
            line_height=5, cell_fill_color=greyscale, 
            cell_fill_mode="ROWS", 
            col_widths=(10, 25, 60, 25),
            align='LEFT', 
            text_align=('CENTER', 'LEFT', 'LEFT', 'RIGHT')) as table:
            for data_row in TABLE_DATA:
                row = table.row()
                for datum in data_row:
                    row.cell(datum)
            
            row = table.row()
            row.cell(colspan=3, text='TOTAL VERSEMENTS', 
                     align='RIGHT',
                     style=FontFace(emphasis='BOLD'))
            # row.cell(text='')
            # row.cell(text='')
            row.cell(total_amount_str, style=FontFace(emphasis='BOLD'))
            
            row = table.row()
            row.cell(colspan=3, text='RESTE A PAYER (SOLDE)',
                     align='RIGHT', 
                     style=FontFace(emphasis='BOLD'))
            # row.cell(text='')
            # row.cell(text='')
            remaining = total_fees - total_amount
            remaining_str = "{:,}".format(remaining).replace(',', ' ') + ' F'
            row.cell(style=FontFace(emphasis='BOLD'), text=remaining_str)

        # Add signature area
        self.set_font_size(10)
        self.set_xy(145, table_title_y)
        self.cell(w=30, txt='--Cachet et signature du comptable--', markdown=True)

        self.set_xy(132, self.y + 35)
        current_date_str = datetime.datetime.today().strftime('%d/%m/%Y')
        acc = user.get_school_accountant()
        self.multi_cell(w=80, 
            txt=f"**{acc.get_full_name()}**\n"
            f"**{acc.phone or ''}\n**"
            f"{str(acc.school.exact_location or acc.school.location)}, le {current_date_str}", 
            markdown=True, align='CENTER')
        
        if user.school.education == main_utils.EDUCATION_ARABIC:
            self.set_font_size(12)
            text = "--**" + "معلومات الطالب" + "**--" + "\n"
            text += " رقم التسجيل :" + f"** {student.student_id or student.identifier or ''} **" + "\n"
            text += " الاسم واللقب :" + f"** {student.full_name_ar or ''} **" + "\n"
            text += " تاريخ ومكان الميلاد :" + f"** {student.birth_place_ar or ''} **" + "\n"
            text += " الفصل العربي :" + f"** {enrollment.level_ar or enrollment.generic_level_ar or ''} **" + "\n"
            text += "مجموع المبالغ المستحقة الدفع :"
            text = reshape_text(text)
            self.set_xy(90, y + 2)
            self.multi_cell(w=80, txt=text, markdown=True, h=6, align='RIGHT')
        
    def add_content(self, enrollment, user, copies=2):
        total_fees = enrollment.get_fees_total()

        # First copy
        self.add_header(divider=False, use_img=True, school=enrollment.school)
        self.set_xy(self.x, self.y + 5)
        self.add_report(enrollment, user, enrollment.payment_set.all(), 
                        total_fees)
        self.set_xy(15, 43)
        self.dashed_line(5, 143, 205, 143, space_length=3)
        
        if copies == 2:
            # Second copy
            self.add_header(x=15, y=145, divider=False, use_img=True, school=enrollment.school)
            self.set_xy(15, 175)
            self.add_report(enrollment, user, enrollment.payment_set.all(), 
                            total_fees)


class PaymentReport2(Document):
    def add_report(self, enrollment, user, payments, total_fees, pricing, summary):
        # Add document title
        self.set_font('Times', 'B', 14)
        self.cell(txt=f'Reçu de Paiement {enrollment.year}', 
                  h=10, w=180, align='C', border=True)

        self.set_font('Times', 'BU', 14)
        y = self.y + 10
        self.set_xy(14, y)
        self.cell(txt="Informations sur l'élève", w=170, h=8)
        self.set_font('Times', '', 11)
        self.ln()
        self.set_x(6)
        last_payment = payments.order_by('-created_at').first()
        last_payment_amount = 0
        last_payment_date = ''
        if last_payment:
            last_payment_amount = last_payment.get_total()
            last_payment_date = last_payment.created_at.strftime('%d/%m/%Y')

        student = enrollment.student
        birth_date = student.birth_date_str()
        text = f"""{' ' * 8}Matricule: **{student.student_id or student.identifier or ''} ** 
        Nom et Prénoms: **{str(student)[:25]}** 
        Né(e) le: **{birth_date}**  Lieu: **{student.birth_place or ''}** 
        Sexe: **{student.gender}**          Classe: **{enrollment.level_fr or enrollment.generic_level_fr or ''}**
        Contact parent:  **{student.father_phone or student.mother_phone or student.phone or ' '}**
        Dernier paiement: **{"{:,}".format(last_payment_amount).replace(',', ' ')} F CFA (le {last_payment_date})**"""
        self.multi_cell(txt=text, w=130, markdown=True, h=6)

        photo_url = student.photo.url if student.photo else ''
        self.add_image_or_gov_img_or_avatar(
            photo_url, student.student_id, 
            x=170, y=self.y - 37, w=25, h=30, alt_text='photo')
        
        self.set_x(14)
        table_title_y = self.y
        self.set_font('Times', 'BU', 14)
        self.cell(txt="Résumé des paiements de l'année", w=120)
        self.set_font('Times', 'BU', 12)
        self.ln(3)
        
        # Add table
        total_amount = 0
        total_to_pay = 0
        total_paid = 0
        TABLE_DATA = [
            ['N°', 'Libellé', 'Montant à payer', 'Paiements', 'Reste'],
        ]

        # Inscription
        to_pay = enrollment.enrollment_fees or pricing['inscription'] or 0
        paid = summary['inscription'] or 0
        total_to_pay += to_pay
        total_paid += paid
        payment_data = [
            '01',
            "Frais d'inscription", 
            "{:,}".format(to_pay).replace(',', ' ') + ' F', 
            "{:,}".format(paid).replace(',', ' ') + ' F', 
            "{:,}".format(to_pay - paid).replace(',', ' ') + ' F', 
        ]
        TABLE_DATA.append(payment_data)

        # Scolarité
        to_pay = enrollment.year_fees or pricing['scolarite'] or 0
        paid = summary['scolarite'] or 0
        total_to_pay += to_pay
        total_paid += paid
        payment_data = [
            '02',
            "Scolarité", 
            "{:,}".format(to_pay).replace(',', ' ') + ' F', 
            "{:,}".format(paid).replace(',', ' ') + ' F', 
            "{:,}".format(to_pay - paid).replace(',', ' ') + ' F', 
        ]
        TABLE_DATA.append(payment_data)

        # Annexe
        to_pay = enrollment.annexe_fees or pricing['annexe'] or 0
        paid = summary['annexe'] or 0
        total_to_pay += to_pay
        total_paid += paid
        payment_data = [
            '03',
            "Frais annexes", 
            "{:,}".format(to_pay).replace(',', ' ') + ' F', 
            "{:,}".format(paid).replace(',', ' ') + ' F', 
            "{:,}".format(to_pay - paid).replace(',', ' ') + ' F', 
        ]
        TABLE_DATA.append(payment_data)
        

        self.set_font('Times', size=11)
        self.set_xy(15, self.y + 5)
        greyscale = 200
        with self.table(
            width=120,
            line_height=7, cell_fill_color=greyscale, 
            cell_fill_mode="ROWS", 
            col_widths=(10, 35, 35, 30, 25),
            align='LEFT', 
            text_align=('CENTER', 'LEFT', 'RIGHT', "RIGHT", 'RIGHT')) as table:
            for data_row in TABLE_DATA:
                row = table.row()
                for datum in data_row:
                    row.cell(datum)
            

            row = table.row()
            row.cell(colspan=2, text='TOTAL', 
                     align='RIGHT',
                     style=FontFace(emphasis='BOLD'))
            row.cell("{:,}".format(total_to_pay).replace(',', ' ') + ' F', style=FontFace(emphasis='BOLD'))
            row.cell("{:,}".format(total_paid).replace(',', ' ') + ' F', style=FontFace(emphasis='BOLD'))
            row.cell("{:,}".format(total_to_pay - total_paid).replace(',', ' ') + ' F', style=FontFace(emphasis='BOLD'))

        # Add signature area
        self.set_font_size(10)
        self.set_xy(145, table_title_y)
        self.cell(w=30, txt='--Cachet et signature du comptable--', markdown=True)

        self.set_xy(132, self.y + 35)
        current_date_str = datetime.datetime.today().strftime('%d/%m/%Y')
        acc = user.get_school_accountant()
        self.multi_cell(w=80, 
            txt=f"**{acc.get_full_name()}**\n"
            f"**{acc.phone or ''}\n**"
            f"{str(acc.school.exact_location or acc.school.location)}, le: {current_date_str}", 
            markdown=True, align='CENTER')
        
        if user.school.education == main_utils.EDUCATION_ARABIC:
            level_ar = enrollment.level_ar or enrollment.generic_level_ar
            if level_ar:
                level_ar = str(level_ar)
            else:
                level_ar = ''
            self.set_font_size(12)
            text = "--**" + "معلومات الطالب" + "**--" + "\n"
            text += " رقم التسجيل :" + f"** {student.student_id or student.identifier or ''} **" + "\n"
            text += " الاسم واللقب :" + f"** {student.full_name_ar or ''} **" + "\n"
            text += " تاريخ ومكان الميلاد :" + f"** {student.birth_place_ar or ''} **" + "\n"
            text += " الفصل العربي :" + f"** {level_ar} **" + "\n"
            text += "رقم هاتف الوالد :" + "\n"
            text += "الدفعة الأخيرة :"
            text = reshape_text(text)
            self.set_xy(90, y + 2)
            self.multi_cell(w=80, txt=text, markdown=True, h=6, align='RIGHT')
        
    def add_content(self, enrollment, user, copies=2):
        total_fees = enrollment.enrollment_fees + enrollment.year_fees

        # First copy
        pricing = get_fees(
            user, enrollment.generic_level_fr.id, 
            enrollment.generic_level_ar.id if enrollment.generic_level_ar else None,
            enrollment.status, with_annexe=True)
        payments_summary = None
        if enrollment.payment_set.filter(payment_type__isnull=False).exists():
            payments_summary = enrollment.payment_set.aggregate(
                inscription1=Sum(
                    'amount', 
                    filter=Q(payment_type=Payment.TYPE_INSCRIPTION), 
                    distinct=True),
                scolarite1=Sum(
                    'amount', 
                    filter=Q(payment_type=Payment.TYPE_SCOLARITE), 
                    distinct=True),
                annexe1=Sum(
                    'amount', 
                    filter=Q(payment_type=Payment.TYPE_ANNEXE), 
                    distinct=True),
                inscription2=Sum(
                    'inscription', 
                    filter=Q(payment_type__isnull=True), 
                    distinct=True),
                scolarite2=Sum(
                    'amount', 
                    filter=Q(payment_type__isnull=True), 
                    distinct=True),
                annexe2=Sum(
                    'annexe', 
                    filter=Q(payment_type__isnull=True), 
                    distinct=True),
                inscription=Coalesce(F('inscription1'), 0, output_field=IntegerField()) + Coalesce(F('inscription2'), 0, output_field=IntegerField()),
                scolarite=Coalesce(F('scolarite1'), 0, output_field=IntegerField()) + Coalesce(F('scolarite2'), 0, output_field=IntegerField()),
                annexe=Coalesce(F('annexe1'), 0, output_field=IntegerField()) + Coalesce(F('annexe2'), 0, output_field=IntegerField()),
            )
        else:
            payments_summary = enrollment.payment_set.aggregate(
                inscription=Coalesce(Sum('inscription'), 0, output_field=IntegerField()),
                scolarite=Coalesce(Sum('amount'), 0, output_field=IntegerField()),
                annexe=Coalesce(Sum('annexe'), 0, output_field=IntegerField()),
            )

        self.add_header(divider=False, use_img=True, school=enrollment.school)
        self.set_xy(self.x, self.y + 5)
        self.add_report(enrollment, user, enrollment.payment_set.all(), 
                        total_fees, pricing, payments_summary)
        self.set_xy(15, 43)
        self.dashed_line(5, 143, 205, 143, space_length=3)
        
        if copies == 2:
            
            # Second copy
            self.add_header(x=15, y=145, divider=False, use_img=True, school=enrollment.school)
            self.set_xy(15, 175)
            self.add_report(enrollment, user, enrollment.payment_set.all(), 
                            total_fees, pricing, payments_summary)
        

class PaymentsListReport(Document):
    def add_content(self, queryset, user=None, payment_type=None):
        # school = queryset.first().enrollment.school 
        # cycle = school.cycle

        # if cycle == main_utils.CYCLE_BOTH:
        #     cycle = main_utils.CYCLE_PRIMARY
        total = 0
        self.add_header()
        self.set_xy(self.x - 1, 45 + 1)
        self.set_font('Times', 'B', 12)

        text = ''
        if payment_type and payment_type == Payment.TYPE_INSCRIPTION:
            text = " DES FRAIS D'INSCRIPTION "
        elif payment_type and payment_type == Payment.TYPE_SCOLARITE:
            text = " DES FRAIS DE SCOLARITE "
        elif payment_type and payment_type == Payment.TYPE_ANNEXE:
            text = " DES FRAIS ANNEXES "

        title = f'LISTE DES VERSEMENTS{text}' + (': ' + user.get_full_name() if user else '')
        self.cell(w=180, h=8, txt=title, border=1, align='C')
        self.set_y(self.y + 15)
        self.set_font('Times', size=11)
        self.set_line_width(0.3)
        headings_style = FontFace(emphasis="BOLD", color=255, fill_color=(128, 128, 128))
        with self.table(
            # borders_layout="NO_HORIZONTAL_LINES",
            cell_fill_color=(224, 235, 255),
            col_widths=(10, 30, 75, 20, 30, 20),
            headings_style=headings_style,
            line_height=7,
            text_align=('CENTER', 'CENTER', 'LEFT', 'LEFT', 'RIGHT', 'CENTER'),
            width=185,
        ) as table:
            row = table.row()
            row.cell('N°')
            row.cell("MAT./ID")
            row.cell('NOM ET PRENOMS')
            row.cell('CLASSE')
            row.cell('MONTANT')
            row.cell("DATE")
            # row.cell('SOLDE')

            counter = 1
            payment_total = 0
            for payment in queryset:
                if payment_type:
                    if payment_type == Payment.TYPE_INSCRIPTION:
                        if payment.inscription or payment.payment_type == Payment.TYPE_INSCRIPTION:
                            payment_total = payment.amount if payment.payment_type == Payment.TYPE_INSCRIPTION else payment.inscription
                    if payment_type == Payment.TYPE_SCOLARITE:
                        if payment.amount or payment.payment_type == Payment.TYPE_SCOLARITE:
                            payment_total = payment.amount
                    if payment_type == Payment.TYPE_ANNEXE:
                        if payment.annexe or payment.payment_type == Payment.TYPE_ANNEXE:
                            payment_total = payment.amount if payment.payment_type == Payment.TYPE_ANNEXE else payment.annexe
                        
                else:
                    payment_total = payment.get_total()

                total += payment_total
                enrollment = payment.enrollment
                row = table.row()
                row.cell(f'{str(counter).zfill(2)}')
                row.cell(enrollment.student.student_id or enrollment.student.identifier or '')
                row.cell(str(enrollment.student).upper()[:30])
                row.cell(str(enrollment.level_fr or enrollment.generic_level_fr))
                row.cell(f"{payment_total}")
                row.cell(payment.created_at.strftime('%d/%m/%Y'))
                # row.cell(f"{enrollment.get_fees_total() - (enrollment.total_paid or 0)}")
                # row.cell(str(enrollment.student.birth_place).upper())
                # row.cell('VALIDE' if enrollment.confirmed else 'OUVERT')
                counter += 1
        
        self.y = self.y + 10
        self.set_font_size(18)
        self.cell(txt=f"Total: {'-' * 60} {'{:,}'.format(total or 0).replace(',', ' ')} F")


class ClassListPDF(Document):
    def footer(self):
        self.set_y(-20)
        self.cell(txt=f'Page {self.page_no()}', 
                  h=10, w=180, align='CENTER')
        
    def add_content(self, queryset, level, redden_girls_names=False, show_ids=False):
        self.add_header(level.school, cycle=level.generic_level.cycle)
        self.set_xy(self.x + 10, 45 + 1)
        self.set_font('ArialNarrow', 'B', 12)
        title = f'LISTE DE CLASSE ' + f"{level} | ({level.year})"
        self.cell(w=160, h=8, txt=title, border=1, align='C')
        self.set_xy(120, self.y + 15)

        current_year = datetime.datetime.today().year
        queryset = queryset.annotate(age=current_year - F('student__birth_year'))
        summary = queryset.aggregate(
            boys=Count(
                'student', filter=Q(student__gender=main_utils.GENDER_MALE), 
                distinct=True) or 0,
            girls=Count(
                'student', filter=Q(student__gender=main_utils.GENDER_FEMALE), 
                distinct=True) or 0,
        )
        boys = summary['boys']
        girls = summary['girls']

        with self.table(
            align='LEFT', 
            text_align=('CENTER', 'CENTER', 'CENTER'),
            col_widths=(15, 15, 15),
            width=60,
            line_height=7) as table:
            self.add_students_count_component(table, boys, girls)
        
        education = level.education
        self.set_y(self.y + 5)
        self.set_font('ArialNarrow', size=10)
        if education == main_utils.EDUCATION_ARABIC:
            self.set_font('TimesNewRoman', size=11)

        self.set_line_width(0.3)
        headings_style = FontFace(emphasis="BOLD", color=255, fill_color=(128, 128, 128))
        headers = [
            {'N°': {'width': 10, 'align': 'CENTER'}},
            {'Matricule': {'width': 35, 'align': 'CENTER'}},
            {'Nom et Prénoms': {'width': 75, 'align': 'LEFT'}},
            {'Sexe': {'width': 14, 'align': 'CENTER'}},
            {'Né(e) le': {'width': 37, 'align': 'CENTER'}},
            {'Age': {'width': 15, 'align': 'CENTER'}},
            {'Classe': {'width': 33, 'align': 'LEFT'}},
        ]

        is_arabic_school = (level.school.education == main_utils.EDUCATION_ARABIC)

        if is_arabic_school:
            headers.append({'Classe arabe': {'width': 33, 'align': 'LEFT'}},)

        if education == main_utils.EDUCATION_ARABIC:
            headers.reverse()
            
        widths = []
        aligns = []
        titles = []
        for header in headers:
            for key, value in header.items():
                widths += value['width'],
                if education == main_utils.EDUCATION_FRENCH:
                    titles += key,
                    aligns += value['align'],
                else:
                    aligns += str(value['align']).replace('LEFT', 'RIGHT'),
                    titles += reshape_text(
                        main_utils.TRANSLATIONS_FR_AR.get(key.lower(), key)),

        with self.table(
            # borders_layout="NO_HORIZONTAL_LINES",
            cell_fill_color=(224, 235, 255),
            col_widths=widths,
            headings_style=headings_style,
            line_height=6.5,
            text_align=aligns,
            width=185,
        ) as table:
            row = table.row()
            
            for title in titles:
                row.cell(title)

            counter = 1
            for enrollment in queryset:
                row = table.row()
                if redden_girls_names and enrollment.student.gender == \
                   main_utils.GENDER_FEMALE:
                   self.set_text_color(255, 0, 0)
                    
                student_identifier = enrollment.student.identifier if show_ids else ''
                if education == main_utils.EDUCATION_FRENCH:
                    row.cell(f'{str(counter).zfill(2)}')
                    row.cell(enrollment.student.student_id \
                            or student_identifier or '')
                    row.cell(str(enrollment).upper())
                    row.cell(str(enrollment.student.gender))
                    row.cell(str(enrollment.student.birth_date_str() or ''))
                    row.cell(str(enrollment.age))
                    row.cell(str(enrollment.level_fr or ''))

                    if is_arabic_school:
                        self.set_font('TimesNewRoman', size=12)
                        level_ar = enrollment.level_ar or ''
                        level_ar = str(level_ar)
                        level_ar = self.reshape_text(level_ar)
                        row.cell(level_ar)
                        self.set_font('ArialNarrow', size=10)
                else:
                    if is_arabic_school:
                        self.set_font('TimesNewRoman', size=12)
                        level_ar = enrollment.level_ar or ''
                        level_ar = str(level_ar)
                        level_ar = self.reshape_text(level_ar)
                        row.cell(level_ar)
                    self.set_font('ArialNarrow', size=10)
                    row.cell(str(enrollment.level_fr or ''))
                    row.cell(str(enrollment.age))
                    row.cell(str(enrollment.student.birth_date_str() or ''))
                    row.cell(str(enrollment.student.gender))
                    self.set_font('TimesNewRoman', size=12)
                    full_name = reshape_text(enrollment.student.full_name_ar)
                    row.cell(str(full_name or enrollment).upper())
                    self.set_font('ArialNarrow', size=10)
                    row.cell(enrollment.student.student_id \
                            or student_identifier or '')
                    row.cell(f'{str(counter).zfill(2)}')
                self.set_text_color(0, 0, 0)
                counter += 1


class ClassPaymentsListPDF(Document):
    def footer(self):
        self.set_y(-20)
        self.cell(txt=f'Page {self.page_no()}', 
                  h=10, w=180, align='CENTER')
        
    def add_content(self, queryset, level, redden_girls_names=False):
        self.add_header(level.school)
        self.set_xy(self.x + 10, 45 + 1)
        self.set_font('ArialNarrow', 'B', 12)
        title = f'RESUME DES VERSEMENTS PAR CLASSE ' + f"{level} ({level.year})"
        self.cell(w=160, h=8, txt=title, border=1, align='C')
        self.set_xy(120, self.y + 15)

        summary = queryset.aggregate(
            total_paid=Sum(F('paid')),
            total_fees=Sum(F('fees')),
            remaining=F('total_fees') - F('total_paid')
        )
        fees = summary['total_fees']
        paid = summary['total_paid']
        remaining = summary['remaining']

        fees = f"{'{:,}'.format(fees or 0).replace(',', ' ')}"
        paid = f"{'{:,}'.format(paid or 0).replace(',', ' ')}"
        remaining = f"{'{:,}'.format(remaining or 0).replace(',', ' ')}"
        with self.table(
            align='LEFT', 
            text_align=('CENTER', 'CENTER', 'CENTER'),
            col_widths=(15, 15, 15),
            width=60,
            line_height=7) as table:
            placeholders = {
                'title': 'RESUME',
                'first': 'A verser',
                'second': 'Versé',
                'third': 'Reste',
            }
            self.add_students_count_component(
                table, fees, paid, remaining, placeholders)
        
        education = level.education
        self.set_y(self.y + 5)

        if education == main_utils.EDUCATION_FRENCH:
            self.set_font('ArialNarrow', size=10)
        else:
            self.set_font('Arial', size=10)

        other_lang = main_utils.get_other_education(education)
        self.set_line_width(0.3)
        headings_style = FontFace(emphasis="BOLD", color=255, fill_color=(128, 128, 128))
        custom_header = {'Versé': {'width': 23, 'align': 'RIGHT'}}
        headers = [
            {'N°': {'width': 10, 'align': 'CENTER'}},
            {'Matricule': {'width': 35, 'align': 'CENTER'}},
            {'Nom et Prénoms': {'width': 60, 'align': 'LEFT'}},
            {'Sexe': {'width': 14, 'align': 'CENTER'}},
            {'Contacts': {'width': 40, 'align': 'CENTER'}},
            {'Classe ' + other_lang + 'r': {'width': 25, 'align': 'CENTER'}},
            {'Montant à payer': {'width': 23, 'align': 'RIGHT'}},
            custom_header,
            {'Reste': {'width': 20, 'align': 'RIGHT'}},
        ]

        if education == main_utils.EDUCATION_ARABIC:
            headers.reverse()
            
        widths = []
        aligns = []
        titles = []
        for header in headers:
            for key, value in header.items():
                widths += value['width'],
                if education == main_utils.EDUCATION_FRENCH:
                    titles += key,
                    aligns += value['align'],
                else:
                    aligns += str(value['align']).replace('LEFT', 'RIGHT'),
                    titles += reshape_text(
                        main_utils.TRANSLATIONS_FR_AR.get(key.lower(), key)),

        with self.table(
            # borders_layout="NO_HORIZONTAL_LINES",
            cell_fill_color=(224, 235, 255),
            col_widths=widths,
            headings_style=headings_style,
            line_height=6.5,
            text_align=aligns,
        ) as table:
            row = table.row()
            
            for title in titles:
                row.cell(title)

            counter = 1
            for enrollment in queryset:
                row = table.row()
                if redden_girls_names and enrollment.student.gender == \
                   main_utils.GENDER_FEMALE:
                   self.set_text_color(255, 0, 0)

                if education == main_utils.EDUCATION_FRENCH:
                    row.cell(f'{str(counter).zfill(2)}')
                    row.cell(enrollment.student.student_id \
                            or enrollment.student.identifier or '')
                    row.cell(str(enrollment).upper()[:28])
                    row.cell(str(enrollment.student.gender))
                    row.cell(str(enrollment.student.get_phone()))
                    row.cell(str(enrollment.level_ar))
                    row.cell('{:,}'.format(enrollment.fees or 0).replace(',', ' '))
                    row.cell('{:,}'.format(enrollment.paid or 0).replace(',', ' '))
                    row.cell('{:,}'.format(enrollment.remaining or 0).replace(',', ' '))
                else:
                    row.cell('{:,}'.format(enrollment.remaining or 0).replace(',', ' '))
                    row.cell('{:,}'.format(enrollment.paid or 0).replace(',', ' '))
                    row.cell('{:,}'.format(enrollment.fees or 0).replace(',', ' '))
                    row.cell(str(enrollment.level_fr))
                    row.cell(str(enrollment.student.get_phone()))
                    row.cell(str(enrollment.student.gender))
                    full_name = reshape_text(enrollment.student.full_name_ar)
                    if not full_name:
                        full_name = str(enrollment.student).upper()

                    row.cell(str(full_name))
                    row.cell(enrollment.student.student_id \
                            or enrollment.student.identifier or '')
                    row.cell(f'{str(counter).zfill(2)}')
                self.set_text_color(0, 0, 0)
                counter += 1


class ClassPaymentsListDetailedPDF(Document):
    def footer(self):
        self.set_y(-20)
        self.cell(txt=f'Page {self.page_no()}', 
                  h=10, w=180, align='CENTER')
        
    def add_content(self, queryset, level, redden_girls_names=False):
        self.add_header(level.school)
        self.set_xy(self.x + 10, 45 + 1)
        self.set_font('ArialNarrow', 'B', 12)
        title = f'RESUME DES VERSEMENTS PAR CLASSE ' + f"{level} ({level.year})"
        self.cell(w=160, h=8, txt=title, border=1, align='C')
        self.set_xy(120, self.y + 15)

        summary = queryset.aggregate(
            total_paid=Sum(F('paid')),
            total_fees=Sum(F('fees')),
            remaining=F('total_fees') - F('total_paid')
        )
        fees = summary['total_fees']
        paid = summary['total_paid']
        remaining = summary['remaining']

        fees = f"{'{:,}'.format(fees or 0).replace(',', ' ')}"
        paid = f"{'{:,}'.format(paid or 0).replace(',', ' ')}"
        remaining = f"{'{:,}'.format(remaining or 0).replace(',', ' ')}"
        with self.table(
            align='LEFT', 
            text_align=('CENTER', 'CENTER', 'CENTER'),
            col_widths=(15, 15, 15),
            width=60,
            line_height=7) as table:
            placeholders = {
                'title': 'RESUME',
                'first': 'A verser',
                'second': 'Versé',
                'third': 'Reste',
            }
            self.add_students_count_component(
                table, fees, paid, remaining, placeholders)
        
        education = level.education
        self.set_y(self.y + 5)

        if education == main_utils.EDUCATION_FRENCH:
            self.set_font('ArialNarrow', size=10)
        else:
            self.set_font('Arial', size=11)

        other_lang = main_utils.get_other_education(education)
        self.set_line_width(0.3)
        headings_style = FontFace(emphasis="BOLD", color=255, fill_color=(128, 128, 128))
        headers = [
            {'N°': {'width': 20, 'align': 'CENTER'}},
            {'Matricule': {'width': 45, 'align': 'CENTER'}},
            {'Nom et Prénoms': {'width': 78, 'align': 'LEFT'}},
            {'Sexe': {'width': 18, 'align': 'CENTER'}},
            {'Contacts': {'width': 70, 'align': 'CENTER'}},
            {'Classe ' + other_lang + 'r': {'width': 35, 'align': 'CENTER'}},
            {'Montant à payer': {'width': 35, 'align': 'RIGHT'}},
            {'Inscription payée': {'width': 40, 'align': 'RIGHT'}},
            {'Scolarité payée': {'width': 30, 'align': 'RIGHT'}},
            {'Annexe payée': {'width': 30, 'align': 'RIGHT'}},
            {'Solde': {'width': 30, 'align': 'RIGHT'}},
            {'Autre 1': {'width': 35, 'align': 'RIGHT'}},
            {'Autre 2': {'width': 35, 'align': 'RIGHT'}},
        ]

        if education == main_utils.EDUCATION_ARABIC:
            headers.reverse()
            
        widths = []
        aligns = []
        titles = []
        for header in headers:
            for key, value in header.items():
                widths += value['width'],
                if education == main_utils.EDUCATION_FRENCH:
                    titles += key,
                    aligns += value['align'],
                else:
                    aligns += str(value['align']).replace('LEFT', 'RIGHT'),
                    titles += reshape_text(
                        main_utils.TRANSLATIONS_FR_AR.get(key.lower(), key)),

        with self.table(
            # borders_layout="NO_HORIZONTAL_LINES",
            cell_fill_color=(224, 235, 255),
            col_widths=widths,
            headings_style=headings_style,
            line_height=6.5,
            text_align=aligns,
        ) as table:
            row = table.row()
            
            for title in titles:
                row.cell(title)

            counter = 1
            for enrollment in queryset:
                row = table.row()
                if redden_girls_names and enrollment.student.gender == \
                   main_utils.GENDER_FEMALE:
                   self.set_text_color(255, 0, 0)

                if education == main_utils.EDUCATION_FRENCH:
                    row.cell(f'{str(counter).zfill(2)}')
                    row.cell(enrollment.student.student_id \
                            or enrollment.student.identifier or '')
                    row.cell(str(enrollment).upper()[:30])
                    row.cell(str(enrollment.student.gender))
                    row.cell(str(enrollment.student.get_phone()))
                    row.cell(str(enrollment.level_ar or enrollment.generic_level_ar or ''))
                    row.cell('{:,}'.format(enrollment.fees or 0).replace(',', ' '))
                    row.cell('{:,}'.format(enrollment.paid_inscription or 0).replace(',', ' '))
                    row.cell('{:,}'.format(enrollment.paid_scolarite or 0).replace(',', ' '))
                    row.cell('{:,}'.format(enrollment.paid_annexe or 0).replace(',', ' '))
                    row.cell('{:,}'.format(enrollment.remaining or 0).replace(',', ' '))
                    row.cell('')
                    row.cell('')
                else:
                    row.cell('')
                    row.cell('')
                    row.cell('{:,}'.format(enrollment.remaining or 0).replace(',', ' '))
                    row.cell('{:,}'.format(enrollment.paid_annexe or 0).replace(',', ' '))
                    row.cell('{:,}'.format(enrollment.paid_scolarite or 0).replace(',', ' '))
                    row.cell('{:,}'.format(enrollment.paid_inscription or 0).replace(',', ' '))
                    row.cell('{:,}'.format(enrollment.fees or 0).replace(',', ' '))
                    row.cell(str(enrollment.level_fr or enrollment.generic_level_fr or ''))
                    row.cell(str(enrollment.student.get_phone()))
                    row.cell(str(enrollment.student.gender))
                    full_name = reshape_text(enrollment.student.full_name_ar)
                    if not full_name:
                        full_name = str(enrollment.student).upper()
                        
                    row.cell(str(full_name))
                    row.cell(enrollment.student.student_id \
                            or enrollment.student.identifier or '')
                    row.cell(f'{str(counter).zfill(2)}')
                self.set_text_color(0, 0, 0)
                counter += 1


class LevelStatsReport(Document):
    def add_content(self, queryset, user, cycle=None):
        if not cycle:
            cycle = user.school.cycle 

        self.add_page()
        self.add_header(school=user.school, cycle=cycle)

        self.set_xy(self.x - 1, 45 + 1)
        self.set_font('Times', 'B', 12)
        title = f'TABLEAU DES EFFECTIFS PAR CLASSE ET PAR GENRE'
        self.cell(w=180, h=8, txt=title, border=1, align='C')
        self.set_y(self.y + 15)
        self.set_font('Times', size=11)
        self.set_line_width(0.3)
        headings_style = FontFace(emphasis="BOLD", fill_color=(192, 192, 192))
        with self.table(
            # borders_layout="NO_HORIZONTAL_LINES",
            cell_fill_color=(224, 235, 255),
            headings_style=headings_style,
            line_height=7,
            text_align=('CENTER', 'LEFT', 'CENTER', 'CENTER', 'CENTER'),
            col_widths=(20, 100, 50, 50, 50),
        ) as table:
            row = table.row()
            row.cell('N°')
            row.cell("CLASSE")
            row.cell('GARCONS')
            row.cell('FILLES')
            row.cell('TOTAL')

            counter = 1
            boys_total = 0
            girls_total = 0

            for level in queryset:
                row = table.row()
                row.cell(f'{str(counter).zfill(2)}')
                row.cell(str(level))
                row.cell(str(level.boys))
                row.cell(str(level.girls))
                row.cell(str(level.students))

                boys_total += level.boys or 0
                girls_total += level.girls or 0
                counter += 1
            
            row = table.row(style=headings_style)
            row.cell('RECAPITULATIF', colspan=2, align='RIGHT')
            row.cell(str(boys_total))
            row.cell(str(girls_total))
            row.cell(str(boys_total + girls_total))

        self.y = self.y + 10
        self.set_font_size(18)



class FicheScolarite(Document):
    def add_content(self, enrollment):
        self.set_font('TimesNewRoman', size=9)
        self.school = str(enrollment.school)
        y = self.y
        self.multi_cell(
            w=(self.w / 2) - 10,
            h=5,
            text="MINISTERE DE L'EDUCATION NATIONALE\n"
                 "ET DE L'ALPHABETISATION\n"
                 f"DIRECTION REGIONALE DE { enrollment.school.location }\n"
                 "INSPECTION DE L'ENSEIGNANEMENT PRESCOLAIRE\n"
                 f"ET PRIMAIRE DE {enrollment.school.IEP or ''}",
                 align='C'
        )
        self.set_xy(self.x + 30, y)

        self.multi_cell(
            w=70,
            h=5,
            text="REPUBLIQUE DE CÔTE D'IVOIRE\n"
                 "Union - Discipline - Travail\n",
                 align='C'
        )
        
        self.ln()
        self.set_xy(-50, 16)
        self.image('static/img/armoiries.png', w=15, h=15)

        self.ln()
        self.set_xy(10, 40)
        self.set_font_size(24)
        self.cell(
            w=self.w - 20, text='**FICHE SCOLAIRE**', 
            border=1, markdown=True, align='C')
        
        self.set_font_size(12)
        self.ln()
        self.set_xy(-80, self.y + 5)
        student_id = enrollment.student.student_id or ''
        self.cell(w=40, text=f"Matricule: {student_id}", align='L')

        self.ln()
        self.set_xy(10, self.y + 8)
        text = f"Nom: **{enrollment.student.last_name}**            Prénoms: **{enrollment.student.first_name}**\n"  \
               f"Né(e): le {enrollment.student.birth_date_str()}       Lieu: {enrollment.student.birth_place or ' '}    S/P ou commune de: .......\n" \
               "Acte de naissance\n" \
               "Ou jugement supplétif n° .................... délivré le ........./........../20............. à ........................................"
        self.multi_cell(w=self.w - 20, text=text, h=6, markdown=True)
        self.set_xy(10, self.y + 5)
        self.cell(w=self.w - 20, markdown=True, text='                                        --Le père--                               --La mère--                                  --Le tuteur--')
        self.set_xy(10, self.y + 5)
        text = "Nom et Prénoms: ....................................      ..........................................      ........................................\n" \
               "Profession: ..............................................      ..........................................      ........................................\n"\
               "Domicile: .................................................      ..........................................      ........................................\n"
        self.multi_cell(w=self.w - 20, text=text, h=6)

        self.ln(0)
        self.set_xy(10, self.y - 5)
        table_data = [
            [
                'Année scolaire', 
                '20.....20....',
                '20.....20....',
                '20.....20....',
                '20.....20....',
                '20.....20....',
                '20.....20....',
                '20.....20....',
                '20.....20....',
            ],
            [
                'Année de scolarité', 
                '1è',
                '2è',
                '3è',
                '4è',
                '5è',
                '6è',
                '7è',
                '8è',
            ],
            [
                'Niveau du cours', 
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
            ],
            [
                'Moyenne annuelle', 
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
            ],
            [
                'Classe-    ment', 
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
            ],
            [
                'Effectif de la classe', 
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
            ]
        ]

        self.set_font('TimesNewRoman', '', size=11)
        with self.table(
            table_data, width=self.w - 20, 
            headings_style=FontFace(emphasis=False),
            text_align=('C', 'C', 'C', 'C', 'C', 'C', 'C', 'C', 'C'),
            line_height=5) as table:
            pass

        self.ln()
        self.set_font('TimesNewRoman')
        self.set_xy(10, self.y)
        text = f"**{self.school}**        Ville ou Sous-Préfecture: ...................................\n"  \
               "Fréquenté du: ......................................................................       Au : .................................................................\n" \
               "Motif de départ: ..................................................................................................................................................\n" 

        self.multi_cell(w=self.w - 20, text=text, h=7, markdown=True)
        self.ln()
        self.set_xy(10, self.y - 12)
        self.cell(w=self.w - 20, align='R', text='A...................................., le ........................................')
        self.ln()
        self.set_xy(10, self.y + 5)
        self.cell(w=self.w - 20, markdown=True, align='L', text='--Nom, Prénoms et contact du directeur--                                                   --Cachet du directeur--')
        
        self.ln()
        self.set_xy(10, self.y + 15)
        text = f"**{self.school}**        Ville ou Sous-Préfecture: ...................................\n"  \
               "Fréquenté du: ......................................................................       Au : .................................................................\n" \
               "Motif de départ: ..................................................................................................................................................\n" 

        self.multi_cell(w=self.w - 20, text=text, h=7, markdown=True)
        self.ln()
        self.set_xy(10, self.y - 12)
        self.cell(w=self.w - 20, align='R', text='A...................................., le ........................................')
        self.ln()
        self.set_xy(10, self.y + 5)
        self.cell(w=self.w - 20, markdown=True, align='L', text='--Nom, Prénoms et contact du directeur--                                               --Cachet du directeur--')


class BulletinPaie(Document):
    def __init__(self, school, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.school = school

    def footer(self):
        # Pied de page
        self.set_y(-15)
        self.set_font('Arial', 'I', 8)
        self.cell(0, 10, f'Page {self.page_no()}/{{nb}}', 0, 0, 'C')

    def add_content(self, staff, payment):
        self.add_page()
        self.set_auto_page_break(auto=True, margin=15)
        
        # Informations de l'établissement
        self.add_header(self.school)

        self.set_y(self.y + 10)        
        # Informations de l'employé
        self.set_font('Arial', 'B', 10)
        self.cell(95, 5, 'INFORMATIONS EMPLOYÉ', 0, 0)
        self.cell(95, 5, 'PÉRIODE DE PAIE', 0, 1)
        
        self.set_font('Arial', '', 10)
        self.cell(95, 5, f"Nom: {staff}", 0, 0)
        self.cell(95, 5, f"Période: {payment.get_period()}", 0, 1)
        
        self.cell(95, 5, f"N° CNPS: {staff.cnps or 'En cours'}", 0, 0)
        self.cell(95, 5, f"Date de paiement: {payment.payment_date.strftime('%d/%m/%Y')}", 0, 1)
        
        self.cell(95, 5, f"Emploi: {staff.role}", 0, 1)
        
        self.ln(10)
        
        # Tableau des rémunérations
        self.cell(0, 7, '**GAINS**', 0, 1, markdown=True)
        headers = ['Rubrique', 'Base', 'Taux', 'Montant']
        self.set_font('Arial', 'B', 10)
        
        # Largeurs des colonnes
        w = [80, 35, 35, 35]
        
        # En-tête du tableau
        for i, header in enumerate(headers):
            self.cell(w[i], 7, header, 1, 0, 'C')
        self.ln()
        
        # Contenu du tableau
        self.set_font('Arial', '', 10)
        lignes = [
            ['Salaire de base', f'{payment.salary}', '1%', f'{staff.salary}'],
        ]

        for option in payment.staffsalaryformonthoption_set \
            .filter(option__operation=SalaryPaymentOptions.OPERATION_ADDING):
             lignes.append(
                 [ option.option.name, f'{payment.salary}', f"{str(option.rate) +' %' if option.rate else '-'}", f"{option.amount if option.amount else '-'}"]
            )
        lignes.append(
            ['TOTAL BRUT IMPOSABLE', '-', '-', f'{payment.gains}'],
        )
        
        for index, ligne in enumerate(lignes):
            if index == len(lignes) - 1:
                self.set_fill_color(200, 200, 200)
                fill = True
            else:
                fill = False
            for i, item in enumerate(ligne):
                self.cell(w[i], 6, item, 1, 0, 'R' if i > 0 else 'L', fill=fill)
            self.ln()
            self.set_fill_color(255, 255, 255) 
        
        # Cotisations
        self.ln(10)
        self.set_font('Arial', 'B', 10)
        self.cell(0, 7, 'COTISATIONS/RETENUES', 0, 1)
        
        headers_cotis = ['Rubrique', 'Base', 'Taux salarial', 'Montant']
        for i, header in enumerate(headers_cotis):
            self.cell(w[i], 7, header, 1, 0, 'C')
        
        self.ln()
        self.set_font('Arial', '', 10)
        cotisations = []
        for option in payment.staffsalaryformonthoption_set \
            .filter(option__operation=SalaryPaymentOptions.OPERATION_SUBSTRACTING):
             cotisations.append(
                 [ option.option.name, f'{payment.salary}', f"{str(option.rate) +' %' if option.rate else '-'}", f"{option.amount if option.amount else '-'}"]
            )
        cotisations.append(['TOTAL RETENUES', '-', '-', f'{payment.deductions}'])
        
        for index, ligne in enumerate(cotisations):
            if index == len(cotisations) - 1:
                print(index, len(lignes))
                self.set_fill_color(200, 200, 200)
                fill = True
            else:
                fill = False
            for i, item in enumerate(ligne):
                self.cell(w[i], 6, item, 1, 0, 'R' if i > 0 else 'L', fill=fill)
            self.ln()
            self.set_fill_color(255, 255, 255) 

        # Net à payer
        self.ln(10)
        self.set_font('Arial', 'B', 12)
        self.cell(130, 10, 'NET À PAYER:', 0, 0, 'R')
        self.cell(55, 10, f'{payment.gains - payment.deductions} F CFA', 1, 1, 'C')