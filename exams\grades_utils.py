from django.db.models import Q, F, <PERSON>, FloatField, IntegerField, Prefetch, OuterRef, Subquery
from django.db import transaction
from django.db.models.aggregates import Sum, Avg, Count, Min, Max
from django.db.models.expressions import Value, ExpressionWrapper, Case, When
from django.db.models.functions import Rank, Coalesce
import time
from . import models
from main import utils
from school import models as school_models

def division_factor_applicable(generic_level, education):
    """ Returns True if education is French and level is primary"""
    return education == utils.EDUCATION_FRENCH and \
        generic_level.cycle == utils.CYCLE_PRIMARY



def create_grade_if_necessary(
        user, level, term_id, education, year, 
        queryset, request_data=None):
    
    subjects = models.LevelSubject.objects.for_school(
        user, level.short_name, education=education, year=year
    ).filter(active=True).only('order', 'subject__id')
    subjects_count = subjects.count()
    term = models.SchoolTerm.objects \
        .for_year(year, user, level, education) \
        .only('id', 'term__id', 'year__id') \
        .get(id=term_id)
    
    avg_filter = None
    if request_data:
        avg_filter = request_data.get('filter') or request_data.get('avg_filter')
    
    avg = term.max / 2
    
    subjects_list = list(subjects)
    objs_to_create = []
    queryset = queryset.annotate(
        grades_count=Count(
            'grade', distinct=True, filter=Q(grade__school_term=term, 
                                             grade__subject__active=True)),
        result_total=Subquery(
            models.TermResult.objects.filter(
                enrollment=OuterRef('pk'), school_term=term
            ).only('total').values('total')[:1]
        ),
        result_average=Subquery(
            models.TermResult.objects.filter(
                enrollment=OuterRef('pk'), school_term=term
            ).only('average').values('average')[:1]
        ),
        result_rank=Subquery(
            models.TermResult.objects.filter(
                enrollment=OuterRef('pk'), school_term=term
            ).only('rank').values('rank')[:1]
        ),
    )

    if avg_filter and avg_filter == 'less_than_avg':
        queryset = queryset.filter(Q(result_average__lt=avg) | Q(result_average__isnull=True))
    elif avg_filter and avg_filter == 'avg':
        queryset = queryset.filter(result_average=avg)
    elif avg_filter and avg_filter == 'more_than_avg':
        queryset = queryset.filter(result_average__gte=avg)
        
    data = {}
    expected_grades_count = subjects_count * queryset.count()

    db_grades_count = models.Grade.objects.filter(
        school_term=term, enrollment__in=queryset, subject__active=True
    ).count()

    # Todo: use this syntax instead of exists to check if grade is created for the term
    # subject_id = f'subject{subject.id}'
    #     enrollment_qs = enrollment_qs.annotate(
    #         **{subject_id: Count('grade', 
    #                              filter=Q(grade__school_term=term) & \
    #                                     Q(grade__subject=subject))}

    # for subject in subjects_list:
    #     pass

    if expected_grades_count > db_grades_count:
        for enrollment in queryset:
            if enrollment.grades_count < subjects_count:
                remaining = subjects_count - enrollment.grades_count 
                for subject in subjects_list:
                    if remaining <= 0:
                        break
                    qs = models.Grade.objects.filter(
                        enrollment=enrollment, school_term=term,
                        subject=subject
                    )
                    add_to_list = False
                    if enrollment.grades_count == 0:
                        add_to_list = True
                    elif not qs.exists():
                        add_to_list = True

                    if add_to_list:
                        grade = models.Grade(
                            enrollment=enrollment, subject=subject,
                            school_term=term, grade=0, updated_by=user)
                        objs_to_create.append(grade)
                        remaining -= 1
        if len(objs_to_create) > 0:
            models.Grade.objects.bulk_create(objs_to_create)
    
    queryset = queryset.prefetch_related(
        Prefetch('grade_set', 
                 queryset=models.Grade.objects.filter(school_term=term, subject__active=True) \
                    .select_related('school_term', 'subject__subject') \
                    .order_by('subject__order')\
                    .only(
                        'enrollment__id', 'grade', 'subject__order', 
                        'subject__subject__id', 
                        'school_term__id'),
                 to_attr='filtered_grades'), 'termresult_set'
    ).annotate(
        term_result_exists=Count('termresult', 
            filter=Q(termresult__school_term=term)))

    data = {}
    for enrollment in queryset:
        grades = list(enrollment.filtered_grades)
        if enrollment.term_result_exists:
            # result = enrollment.termresult_set.filter(school_term=term).first()
            grades.append(enrollment.result_total)
            grades.append(enrollment.result_average)
            grades.append(enrollment.result_rank)
        else:
            grades.append(0)
            grades.append(0)
            grades.append(0)
        data[enrollment] = grades
    return data


def clean_grades_left_as_zeros(queryset, subjects, term):
    """ Sets the grade value to None if no grade is found for the subject """
    subjects_count = len(subjects)
    
    for subject in subjects:
        students_marked_qs = models.Grade.objects.filter(
            school_term=term, 
            subject=subject, 
            grade__isnull=False,
            grade__gt=0,
            enrollment__in=queryset
        )
        students_not_marked_qs = models.Grade.objects.filter(
            school_term=term, 
            subject=subject, 
            grade=0,
            enrollment__in=queryset
        )

        if (students_marked_qs.count() == 0):
            students_not_marked_qs.update(grade=None)


def update_level_term_result(user, level, term):
    """ Updates a student's term result """

    start_time = time.time()

    # Initialize data to use
    education = term.education
    year = term.year
    subjects = models.LevelSubject.objects.for_school(
        user, level.generic_level.short_name, education, year)
    coefs = [subject.coefficient for subject in subjects.all()]
    queryset = school_models.Enrollment.objects.for_user(
               user=user, year=year).filter(active=True).prefetch_related('grade_set')
    
    queryset = utils.filter_enrollment_by_level(level, queryset)\
        .annotate(result_exists=Count(
            'termresult', filter=Q(termresult__school_term=term),
            distinct=True))

    create_grade_if_necessary(
            user, level=level.generic_level, term_id=term.id, 
            education=education,
            queryset=queryset, year=year)

    clean_grades_left_as_zeros(queryset, subjects, term)
    if utils.is_second_cycle_fr(level):
        queryset = annotate_group_average_exists(queryset, term)

    use_division_factor = division_factor_applicable(
        level.generic_level, education)
    
    first_coef = coefs[0] if coefs else 1
    coefs_are_same = (coefs.count(first_coef) == len(coefs))

    
    
    # Create results that do not exist
    create_results_objs(queryset, term)

    term_results = models.TermResult.objects.filter(
            enrollment__in=queryset, school_term=term)
    
    objs_to_create = []
    objs_to_update = []
    
    if not use_division_factor and coefs_are_same:
        # For primary french when coefs are same
        term_results = models.TermResult.objects.filter(
            enrollment__in=queryset, school_term=term)\
            .annotate(
                new_total=Sum('enrollment__grade__grade',
                              filter=Q(enrollment__grade__school_term=term) & \
                              Q(enrollment__grade__grade__isnull=False) & \
                              Q(enrollment__grade__subject__active=True) ),
                new_average=Avg('enrollment__grade__grade',
                              filter=Q(enrollment__grade__school_term=term) & \
                              Q(enrollment__grade__grade__isnull=False) & \
                              Q(enrollment__grade__subject__active=True)),
            )

        for result in term_results:
            result.total = result.new_total
            result.average = result.new_average
            objs_to_update.append(result)
        
    elif utils.is_second_cycle_fr(level=level):
        update_second_cycle_fr_results(
            queryset=queryset, level=level, term=term, 
            objs_to_create=objs_to_create,
            objs_to_update=objs_to_update, subjects=subjects)
    else:
        term_results = term_results.annotate(
            new_total = Sum(F('enrollment__grade__grade'), 
                filter=Q(enrollment__grade__school_term=term) & \
                       Q(enrollment__grade__grade__isnull=False) & \
                       Q(enrollment__grade__subject__active=True)
            ),
            coefs = Sum(
                ExpressionWrapper(
                                F('enrollment__grade__subject__max') / F('enrollment__grade__school_term__max'),
                                output_field=FloatField()),
                filter=Q(enrollment__grade__school_term=term) & \
                       Q(enrollment__grade__grade__isnull=False) & \
                       Q(enrollment__grade__subject__active=True)
            ),
            new_average = ExpressionWrapper(
                Coalesce(F('new_total'), Value(0)) / Coalesce(F('coefs'), Value(1)),
                output_field=FloatField()
            )  
        )

        for item in term_results:
            item.total = item.new_total or 0
            if item.average != None:
                item.average = item.new_average
            else:
                item.average = None
            objs_to_update.append(item)

    if objs_to_create:
        models.TermResult.objects.bulk_create(objs_to_create)
    
    if objs_to_update:
        models.TermResult.objects.bulk_update(
            objs_to_update, 
            fields=['total', 'average', 'average_with_coef'])
        
    term_results = models.TermResult.objects.filter(
        enrollment__in=queryset, school_term=term)
    update_rank(term_results, 'average', models.TermResult, with_ex=True)

    # Update level term result
    update_level_term_stats(level, queryset, term)

    if is_last_term(user, level, term):
        update_students_annual_results(level, user)

    # Set level files as unclean
    categories_to_update = [
        school_models.PDFFile.CATEGORY_REPORT,
        school_models.PDFFile.CATEGORY_RESULT,
        school_models.PDFFile.CATEGORY_RESULT2,
    ]
    school_models.PDFFile.objects.filter(
        level=level, term=term.term, 
        category__in=categories_to_update) \
        .update(is_clean=False)
    end_time = time.time()
    print(f'Updating average for {level} {level.school} query took', end_time - start_time, 'seconds')


def create_results_objs(queryset, term):
    enrollments_without_result = queryset.filter(result_exists=0)
    objs_to_create = []
    added = {}

    for item in enrollments_without_result.all():
        objs_to_create.append(
            models.TermResult(
                enrollment=item, 
                school_term=term)
        )
    
    if objs_to_create:
        models.TermResult.objects.bulk_create(objs_to_create)
        objs_to_create = []
        


def annotate_group_average_exists(queryset, term):
    return queryset.prefetch_related('studentsubjectgroupaverage_set').annotate(
        letter_result_exists=Count(
                'studentsubjectgroupaverage', 
                filter=Q(studentsubjectgroupaverage__group=utils.CATEGORY_LETTERS) & \
                        Q(studentsubjectgroupaverage__term=term), 
                distinct=True),
        science_result_exists=Count(
                'studentsubjectgroupaverage', 
                filter=Q(studentsubjectgroupaverage__group=utils.CATEGORY_SCIENCE) & \
                        Q(studentsubjectgroupaverage__term=term), 
                distinct=True),
        other_result_exists=Count(
                'studentsubjectgroupaverage', 
                filter=(
                    Q(studentsubjectgroupaverage__group=utils.CATEGORY_OTHER, studentsubjectgroupaverage__term=term) \
                    |
                    Q(studentsubjectgroupaverage__group__isnull=True, studentsubjectgroupaverage__term=term)
                ), distinct=True),
    )


def annotate_rank(queryset, field_to_use):
    return queryset.order_by(f'-{field_to_use}') \
            .annotate(
                new_rank=Window(
                    expression=Rank(),
                    order_by=F(field_to_use).desc(),
                )  
            )

def update_rank(queryset, field_to_use, obj_class, 
                with_ex=False, for_french=False):
    """ Updates the queryset's rank field by ranking data following
        field_to_use. If with_ex=True, then model must have is_ex field"""
    queryset = annotate_rank(queryset, field_to_use)

    ranks_dict = {}
    objs = []   
    for item in queryset:
        if for_french:
            item.french_rank = item.new_rank
        else:
            item.rank = item.new_rank

        if with_ex:
            if not str(item.new_rank) in ranks_dict:
                key = str(item.new_rank)
                ranks_dict[key] = 1
            else:
                ranks_dict[key] += 1
            
            if for_french:
                item.french_is_ex = (ranks_dict[key] > 1)
            else:
                item.is_ex = (ranks_dict[key] > 1)

        objs.append(item)

    update_fields = ['french_rank' if for_french else 'rank']
    if with_ex:
        if for_french:
            update_fields.append('french_is_ex')
        else:
            update_fields.append('is_ex')
    obj_class.objects.bulk_update(objs, fields=update_fields)


def create_subjects_from_kit(kit, school, year, update_existing=False):
    """ Creates subjects from a kit """
    subjects_to_create = []
    school_subjects = school.get_subjects(year).select_related('subject')
    for subject_kit in kit.subject_kits.prefetch_related('subjects').all():
        for level in subject_kit.levels.all():
            queryset = subject_kit.subjects.all()
            for subject in queryset:
                if not school_subjects.filter(
                    year=year, subject=subject.subject,
                    level=level, school=school).exists():
                    obj = models.LevelSubject(
                        year=year, subject=subject.subject,
                        coefficient=subject.coefficient, max=subject.max,
                        active=subject.active, order=subject.order, level=level,
                        school=school)
                    subjects_to_create.append(obj)
    models.LevelSubject.objects.bulk_create(subjects_to_create)


def create_terms_from_kit(kit, school, year, update_existing=False):
    """ Creates subjects from a kit """
    terms_to_create = []
    school_terms = school.get_terms(year).select_related('term')
    for term_kit in kit.term_kits.prefetch_related('terms').all():
        queryset = term_kit.terms.all()
        for term in queryset:
            for level in term_kit.levels.all():
                if not school_terms.filter(
                    year=year, level=level, 
                    school=school, term=term.term).exists():

                    obj = models.SchoolTerm(
                        year=year, term=term.term,
                        coefficient=term.coefficient, max=term.max,
                        active=True, level=level,
                        school=school, cycle=level.cycle,
                        education=term.education)
                    terms_to_create.append(obj)
    models.SchoolTerm.objects.bulk_create(terms_to_create)


def has_primary_cycle(school):
    return school.cycle == utils.CYCLE_BOTH or \
        school.cycle == utils.CYCLE_PRIMARY

def has_secondary_cycle(school):
     return school.cycle == utils.CYCLE_SECONDARY or \
        school.cycle == utils.CYCLE_BOTH


def get_full_kit(model_name, cycle, education, association=None):
    queryset = model_name.objects.filter(
                Q(cycle=cycle) | Q(cycle=utils.CYCLE_BOTH),
                education=education)
    if association and issubclass(model_name, models.CompleteCycleSubjectKit) and \
        education == utils.EDUCATION_ARABIC:
        queryset = queryset.filter(association=association)

    if queryset.exists():
        return queryset.first()
    

def add_kit_if_exists(model_name, cycle, education, list_obj, school, year):
    """ Appends subject kit to list if no subject exists for the cycle and education"""
    exists = False

    if issubclass(model_name, models.CompleteCycleSubjectKit):
        exists = True
        cycle_levels = school_models.GenericLevel.objects.filter(cycle=cycle)

        for level in cycle_levels:
            if not school.levelsubject_set.filter(    
            level__cycle=cycle, subject__education=education, 
            year=year, level=level).exists():
                exists = False
                break
            # exists = school.levelsubject_set.filter(    
            #     level__cycle=cycle, subject__education=education, 
            #     year=year).exists()
    elif issubclass(model_name, models.CompleteTermKit):
        exists = school.schoolterm_set\
            .filter(education=education, cycle=cycle, year=year)

    if not exists:
        kit = get_full_kit(model_name, cycle, education, school.association)
        if kit: 
            list_obj.append(kit)


def create_default_subjects_and_terms(school :school_models.School, year, french_only=False):
    """ Generates the default subjects for a school """
    subject_kits = []
    term_kits = []

    if has_primary_cycle(school):
        add_kit_if_exists(models.CompleteCycleSubjectKit,
            utils.CYCLE_PRIMARY, utils.EDUCATION_FRENCH,
            subject_kits, school, year)
        add_kit_if_exists(models.CompleteTermKit,
            utils.CYCLE_PRIMARY, utils.EDUCATION_FRENCH,
            term_kits, school, year)
        
        if school.education == utils.EDUCATION_ARABIC and not french_only:
            add_kit_if_exists(models.CompleteTermKit,
                utils.CYCLE_PRIMARY, utils.EDUCATION_ARABIC,
                term_kits, school, year)


    if has_secondary_cycle(school):
        add_kit_if_exists(models.CompleteCycleSubjectKit,
            utils.CYCLE_SECONDARY, utils.EDUCATION_FRENCH,
            subject_kits, school, year)
        add_kit_if_exists(models.CompleteTermKit,
            utils.CYCLE_SECONDARY, utils.EDUCATION_FRENCH,
            term_kits, school, year)
        
        if school.education == utils.EDUCATION_ARABIC and not french_only:
            add_kit_if_exists(models.CompleteTermKit,
                utils.CYCLE_SECONDARY, utils.EDUCATION_ARABIC,
                term_kits, school, year)

    if school.education == utils.EDUCATION_ARABIC and school.association and not french_only:
        if has_primary_cycle(school):
            add_kit_if_exists(models.CompleteCycleSubjectKit,
                utils.CYCLE_PRIMARY, utils.EDUCATION_ARABIC,
                subject_kits, school, year)

        if has_secondary_cycle(school):
            add_kit_if_exists(models.CompleteCycleSubjectKit,
                utils.CYCLE_SECONDARY, utils.EDUCATION_ARABIC,
                subject_kits, school, year)
            

    for kit in subject_kits:
        create_subjects_from_kit(kit=kit, school=school, year=year)
    for kit in term_kits:
        create_terms_from_kit(kit=kit, school=school, year=year)


def update_level_term_stats(level, queryset=None, term=None):
    """ Updates term statistics : boys, girls admitted etc. for a given level 
        in the specified term. If term is None, then updates annual statistics.
    """
    average :float = 0

    if term:
        average = (term.max / 2)
    else:
        terms_qs = models.SchoolTerm.objects.filter(
            level=level.generic_level, school=level.school,
            year=level.year, education=level.education
        )
        average = terms_qs.first().max / 2

    education = level.education

    if not queryset:
        queryset = school_models.Enrollment.objects.filter(
            Q(year=level.year)
        )
        queryset = utils.filter_enrollment_by_level(level, queryset)

    if term:
        result :dict = queryset.aggregate(
            boys=Count('student', filter=Q(student__gender=utils.GENDER_MALE), 
                    distinct=True),
            boys_present=Count('student', 
                        filter=Q(student__gender=utils.GENDER_MALE) &  \
                            Q(termresult__school_term=term) & \
                            Q(termresult__average__gte=1), 
                    distinct=True),
            boys_admitted=Count('termresult', 
                filter=Q(termresult__school_term=term) & \
                    Q(student__gender=utils.GENDER_MALE) & \
                    Q(termresult__average__gte=average)),
            boys_perc=(F('boys_admitted') / (F('boys_present') or 1) * 100) or 1,
            girls=Count('student', filter=Q(student__gender=utils.GENDER_FEMALE), 
                    distinct=True),
            girls_present=Count('student', 
                    filter=Q(student__gender=utils.GENDER_FEMALE) & \
                            Q(termresult__school_term=term) & \
                            Q(termresult__average__gte=1), 
                    distinct=True),
            girls_admitted=Count('termresult', 
                filter=Q(termresult__school_term=term) & \
                    Q(student__gender=utils.GENDER_FEMALE) & \
                    Q(termresult__average__gte=average)),
            girls_perc=(F('girls_admitted') / (F('girls_present') or 1) * 100) or 1,
            min_average=Min('termresult__average', 
                filter=Q(termresult__school_term=term)),
            max_average=Max('termresult__average', 
                filter=Q(termresult__school_term=term)),
            level_average=(F('min_average') + F('max_average')) / 2
        )
    else:
        result :dict = queryset.aggregate(
            boys=Count('student', filter=Q(student__gender=utils.GENDER_MALE), 
                    distinct=True),
            boys_present=Count('student', 
                        filter=Q(student__gender=utils.GENDER_MALE) &  \
                            Q(educationyearresult__education=education) & \
                            Q(educationyearresult__average__gte=1), 
                    distinct=True),
            boys_admitted=Count('educationyearresult', 
                filter=Q(educationyearresult__education=education) & \
                    Q(student__gender=utils.GENDER_MALE) & \
                    Q(educationyearresult__average__gte=average)),
            boys_perc=(F('boys_admitted') / (F('boys_present') or 1) * 100) or 1,
            girls=Count('student', filter=Q(student__gender=utils.GENDER_FEMALE), 
                    distinct=True),
            girls_present=Count('student', 
                    filter=Q(student__gender=utils.GENDER_FEMALE) & \
                            Q(educationyearresult__education=education) & \
                           Q(educationyearresult__average__gte=1), 
                    distinct=True),
            girls_admitted=Count('educationyearresult', 
                filter=Q(educationyearresult__education=education) & \
                    Q(student__gender=utils.GENDER_FEMALE) & \
                    Q(educationyearresult__average__gte=average)),
            girls_perc=(F('girls_admitted') / (F('girls_present') or 1) * 100) or 1,
            min_average=Min('educationyearresult__average', 
                filter=Q(educationyearresult__education=education)),
            max_average=Max('educationyearresult__average', 
                filter=Q(educationyearresult__education=education)),
            level_average=(F('min_average') + F('max_average')) / 2
        )

    queryset = models.LevelStatistics.objects.update_or_create(
        defaults={
            'boys_present': result.get('boys_present', 0) or 0,
            'boys_admitted': result.get('boys_admitted', 0) or 0,
            'boys_perc': result.get('boys_perc', 0) or 0,
            'girls_present': result.get('girls_present', 0) or 0,
            'girls_admitted': result.get('girls_admitted', 0) or 0,
            'girls_perc': result.get('girls_perc', 0) or 0,
            'min_average': result.get('min_average', 0) or 0,
            'max_average': result.get('max_average', 0) or 0,
            'level_average': result.get('level_average', 0) or 0,
        }, level=level, term=term
    )


def update_students_annual_results(level, user):
    queryset = school_models.Enrollment.objects.for_user_minimum(user=user, year=level.year)
    queryset = utils.filter_enrollment_by_level(level, queryset)
    
    last_term_id = get_last_term(level, user).id
    education = level.education
    level_term = models.SchoolTerm.objects.active(
        level.year, user=user, level=level.generic_level, education=education).first()
    is_primary = utils.is_primary_fr(level)
    if not is_primary:
        # For secondary school
        queryset = queryset.annotate(
            average_total=Sum(
                ExpressionWrapper(
                    F('termresult__average') * F('termresult__school_term__coefficient'),
                    output_field=FloatField()),
                filter=Q(termresult__average__isnull=False) & \
                       Q(termresult__school_term__isnull=False) & \
                       Q(termresult__school_term__active=True) & \
                       Q(termresult__school_term__education=education),
                # distinct=True
            ),
            coefs=Coalesce(Sum('termresult__school_term__coefficient',
                filter=Q(termresult__average__isnull=False) & \
                       Q(termresult__school_term__isnull=False) & \
                       Q(termresult__school_term__active=True) & \
                       Q(termresult__school_term__education=education),
                # distinct=True
            ), 1, output_field=FloatField()),
            annual_average=F('average_total') / F('coefs'),
        )

    else:
        grand_total_field = 'grand_total'
        coefs_field = 'coefs'
        if not level.school.use_class_average:
            grand_total_field += '_'
            coefs_field += '_'
        queryset = queryset.annotate(
            class_total=Coalesce(Sum(
                ExpressionWrapper(
                    F('termresult__average') * F('termresult__school_term__coefficient'),
                    output_field=FloatField()),
                filter=Q(termresult__average__isnull=False) & \
                       ~Q(termresult__school_term__id=last_term_id) & \
                       Q(termresult__school_term__active=True) & \
                       Q(termresult__school_term__education=education),
                # distinct=True
            ), 0, output_field=FloatField()),
            class_coefs=Coalesce(Sum('termresult__school_term__coefficient',
                filter=Q(termresult__average__isnull=False) & \
                       ~Q(termresult__school_term__id=last_term_id) & \
                       Q(termresult__school_term__active=True) & \
                       Q(termresult__school_term__education=education),
                # distinct=True
            ), 0, output_field=IntegerField()),
            class_average=F('class_total') / Coalesce(F('class_coefs'), 1, output_field=FloatField()),
            last_total=Coalesce(Sum(
                ExpressionWrapper(
                    F('termresult__average') * F('termresult__school_term__coefficient'),
                    output_field=FloatField()),
                filter=Q(termresult__average__isnull=False) & \
                       Q(termresult__school_term__id=last_term_id) & \
                       Q(termresult__school_term__education=education),
                # distinct=True
            ), 0, output_field=FloatField()),
            last_coef= Coalesce(Sum('termresult__school_term__coefficient',
                filter=Q(termresult__average__isnull=False) & \
                       Q(termresult__school_term__id=last_term_id) & \
                       Q(termresult__school_term__education=education),
                # distinct=True
            ), 0, output_field=IntegerField()),
            last_average = Coalesce(F('last_total') / F('last_coef'), 0, output_field=FloatField()),
            coefs_ = F('last_coef') + F('class_coefs'),
            coefs = (F('last_coef') / 2) + 1,
            grand_total_ = ExpressionWrapper(F('class_total') + F('last_total'), output_field=FloatField()),
            grand_total = ExpressionWrapper(F('class_average') + (F('last_total') / 2), output_field=FloatField()),
            annual_average = ExpressionWrapper(F(grand_total_field) / F(coefs_field), output_field=FloatField()),
        )
    
    required_avg = 10
    if level_term:
        required_avg = level_term.max / 2

    queryset = queryset.annotate(
        education_result_exists = Count(
                'educationyearresult', 
                filter=Q(educationyearresult__education=education),
                distinct=True),
        max_total=Sum( 
            ExpressionWrapper(
                F('termresult__school_term__max') * F('termresult__school_term__coefficient'),
                output_field=FloatField()),
            filter=Q(termresult__average__isnull=False) & \
                    Q(termresult__school_term__active=True) & \
                    Q(termresult__school_term__education=education),
            # distinct=True
        ),
        # required_avg=(F('max_total') / F('coefs')) / 2,
        decision=Case(
                When(
                    annual_average__gte=required_avg, 
                    then=Value(models.EducationYearResult.DECISION_ADMITTED)
                ),
                When(
                    annual_average__lt=required_avg, 
                    then=Value(models.EducationYearResult.DECISION_STAYS_DOWN)
                )
            ), 
    )

    results_to_update = []
    results_to_create = []

    for enrollment in queryset:
        if enrollment.education_result_exists:
            result = enrollment.educationyearresult_set \
                .filter(education=education).first()
            result.average = enrollment.annual_average or 0
            result.decision = enrollment.decision

            if is_primary:
                result.average_with_coef = enrollment.grand_total or 0
                result.class_average = enrollment.class_average
                result.admission_average = enrollment.last_average

            results_to_update.append(result)

        else:
            obj = models.EducationYearResult(
                enrollment=enrollment,
                education=education,
                average=enrollment.annual_average or 0,
                average_with_coef=0,
                class_average=0,
                admission_average=0,
            )

            if is_primary:
                obj.average_with_coef=enrollment.grand_total or 0
                obj.class_average=enrollment.class_average
                obj.admission_average=enrollment.last_average
            obj.decision = enrollment.decision
            results_to_create.append(obj)

    if results_to_update:
        models.EducationYearResult.objects.bulk_update(
            objs=results_to_update, 
            fields=[
                'average', 'average_with_coef',
                'class_average', 'admission_average',
                'decision'
            ]
        )

    if results_to_create:
        models.EducationYearResult.objects.bulk_create(
            objs=results_to_create)
    
    # Update annual statistics
    update_level_term_stats(level)

    queryset = models.EducationYearResult.objects.filter(
        enrollment__year=level.year, education=education)
    queryset = queryset.filter(
        Q(enrollment__level_fr=level) | Q(enrollment__level_ar=level)
    )
    update_rank(queryset, 'average', models.EducationYearResult, with_ex=True)



def get_last_term(level, user):
    """ Returns the last term of a school for a given level """
    return models.SchoolTerm.objects.active(
                year=level.year, user=user, 
                education=level.education, 
                level=level.generic_level
            ).order_by('-term__order').first()

def get_first_term(level, user):
    """ Returns the first term of a school for a given level """
    return models.SchoolTerm.objects.active(
                year=level.year, user=user, 
                education=level.education, 
                level=level.generic_level
            ).order_by('term__order').first()

def is_last_term(user, level, term):
    """Indicates whether a given term is the last of the year for a school
        for a given level """
    last_term = get_last_term(level, user)
    if last_term:
        return term.id == last_term.id
    return False

def is_first_term(user, level, term):
    """Indicates whether a given term is the first of the year for a school
        for a given level """
    first_term = get_first_term(level, user)
    if first_term:
        return term.id == first_term.id
    return False


def round_number(number, return_zero_if_none=False):
    if number:
        return round(number, 2)
    
    if return_zero_if_none:
        return 0

    return number

def update_second_cycle_fr_results(
        queryset, level, term, objs_to_create, objs_to_update,
        subjects):
    
    # Annotate data
    qs = models.TermResult.objects.filter(
        enrollment__in=queryset, school_term=term)
    qs = annotate_main_results(qs, term, True)

    for item in qs:
        item.total = round_number(item.new_total, True)
        item.average = round_number(item.new_average)
        item.average_with_coef = round_number(item.coeffed_total, True)
        item.french_average = round_number(item.french_avg)
        item.french_coefs = round_number(item.french_coefficients)
        objs_to_update.append(item)
    
    models.TermResult.objects.bulk_update(
        objs_to_update, 
        fields=[
            'total', 'average', 'average_with_coef',
            'french_average', 'french_coefs',
        ])
    objs_to_update = []

    update_rank(
        models.TermResult.objects.filter(
            enrollment__in=queryset, school_term=term),
            'french_average',
            models.TermResult,
            True, True
    )


    # Create subject_group_average objects
    # TODO: Annotate group result on StudentGroupAverage directly for speed
    objs_to_create = []
    annotated = annotate_group_results(queryset, term)


    # Create or update subject group averages
    for obj in annotated:
        if obj.letter_result_exists:
            avg_obj = models.StudentSubjectGroupAverage.objects.filter(
                group=utils.CATEGORY_LETTERS,
                term=term, enrollment=obj,
            ).first()

            avg_obj.average = round_number(obj.letter_avg, True) 
            objs_to_update.append(avg_obj)
        elif not obj.letter_result_exists: 
            objs_to_create.append(
                models.StudentSubjectGroupAverage(
                    enrollment=obj,
                    term=term, group=utils.CATEGORY_LETTERS,
                    average=round_number(obj.letter_avg, True) ,
                )
            )
        
        if obj.science_result_exists:
            avg_obj = models.StudentSubjectGroupAverage.objects.filter(
                group=utils.CATEGORY_SCIENCE,
                term=term, enrollment=obj,
            ).first()

            avg_obj.average = round_number(obj.science_avg, True) 
            objs_to_update.append(avg_obj)
        elif not obj.science_result_exists: 
            objs_to_create.append(
                models.StudentSubjectGroupAverage(
                    enrollment=obj,
                    term=term, group=utils.CATEGORY_SCIENCE,
                    average=round_number(obj.science_avg, True) ,
                )
            )
    
        if obj.other_result_exists:
            avg_obj = models.StudentSubjectGroupAverage.objects.filter(
                group=utils.CATEGORY_OTHER,
                term=term, enrollment=obj,
            ).first()

            avg_obj.average = round_number(obj.other_avg, True) 
            objs_to_update.append(avg_obj)
        elif not obj.other_result_exists: 
            objs_to_create.append(
                models.StudentSubjectGroupAverage(
                    enrollment=obj,
                    term=term, group=utils.CATEGORY_OTHER,
                    average=round_number(obj.other_avg, True),
                )
            )
    
    if objs_to_create:
        models.StudentSubjectGroupAverage.objects.bulk_create(objs_to_create)
    
    if objs_to_update:
        models.StudentSubjectGroupAverage.objects.bulk_update(
            objs_to_update, fields=['average'])
        
    # Update subjects groups ranks
    update_subject_groups_rank(level, term)
    with transaction.atomic():
        for subject in subjects:
            grades_qs = models.Grade.objects.for_level(level=level, term=term) \
                .select_related('subject').filter(subject=subject, subject__active=True)
            update_rank(grades_qs, 'grade', models.Grade, True)

    objs_to_create = []
    objs_to_update = []


def update_subject_groups_rank(level, term):
    update_subject_group_rank(utils.CATEGORY_LETTERS, level, term)
    update_subject_group_rank(utils.CATEGORY_SCIENCE, level, term)
    update_subject_group_rank(utils.CATEGORY_OTHER, level, term)

    
def update_subject_group_rank(group, level, term):
    queryset = models.StudentSubjectGroupAverage.objects \
        .for_group(group, term, level)
    update_rank(queryset, 'average', models.StudentSubjectGroupAverage,
                True)

    

def annotate_main_results(queryset, term, with_french_data=False):
    """ Returns a new queryset containing new_total, coeffed_total and average"""
    queryset = queryset.annotate(
        new_total = Sum(F('enrollment__grade__grade'),
            filter=Q(enrollment__grade__grade__isnull=False) & \
                    Q(enrollment__grade__school_term=term) & \
                    Q(enrollment__grade__subject__active=True), 
                    distinct=True),

        coeffed_total = Sum(
            ExpressionWrapper(
                F('enrollment__grade__grade') * F('enrollment__grade__subject__coefficient'),
                output_field=FloatField()),
            filter=Q(enrollment__grade__grade__isnull=False) & \
                    Q(enrollment__grade__school_term=term) & \
                    Q(enrollment__grade__subject__active=True)),

        new_coefs = Sum(F('enrollment__grade__subject__coefficient'),
            filter=Q(enrollment__grade__grade__isnull=False) & \
                    Q(enrollment__grade__school_term=term) & \
                    Q(enrollment__grade__subject__active=True)),

        new_average = F('coeffed_total') / Coalesce(F('new_coefs'), Value(1)),
    )

    if with_french_data:
        queryset = queryset.annotate(
            french_total = Sum(
                ExpressionWrapper( 
                    F('enrollment__grade__grade') * F('enrollment__grade__subject__coefficient'),
                    output_field=FloatField()
                    ), 
                    filter=Q(enrollment__grade__school_term=term) & \
                        Q(enrollment__grade__grade__isnull=False) & \
                        Q(enrollment__grade__subject__subject__is_sous_matiere=True) & \
                        Q(enrollment__grade__subject__active=True)
            ),
            french_coefficients = Sum(
                F('enrollment__grade__subject__coefficient'),
                filter=Q(enrollment__grade__school_term=term) & \
                    Q(enrollment__grade__grade__isnull=False) & \
                    Q(enrollment__grade__subject__subject__is_sous_matiere=True) & \
                    Q(enrollment__grade__subject__active=True)
            ),
            french_avg = ExpressionWrapper(
                Coalesce(
                    F('french_total'), Value(0)) / Coalesce(F('french_coefficients'), Value(1)
                ),
                output_field=FloatField()
            ),
        )
    return queryset


def annotate_group_results(queryset, term):
    """ Returns the queryset with letter, science and other results annotated """
    return queryset.annotate(
        letter_total = Sum(
            ExpressionWrapper( 
                F('grade__grade') * F('grade__subject__coefficient'),
                output_field=FloatField()
            ), 
            filter=Q(grade__school_term=term) & \
                    Q(grade__grade__isnull=False) & \
                    Q(grade__subject__subject__category=utils.CATEGORY_LETTERS) & \
                    Q(grade__subject__active=True)
        ),
        letter_coefs = Sum(
            F('grade__subject__coefficient'),
            filter=Q(grade__school_term=term) & \
                    Q(grade__grade__isnull=False) & \
                    Q(grade__subject__subject__category=utils.CATEGORY_LETTERS) & \
                    Q(grade__subject__active=True)
        ),
        letter_avg = ExpressionWrapper(
            Coalesce(F('letter_total'), Value(0)) / Coalesce(F('letter_coefs'), Value(1)),
            output_field=FloatField()
        ),
        science_total = Sum(
            ExpressionWrapper( 
                F('grade__grade') * F('grade__subject__coefficient'),
                output_field=FloatField()
            ), 
            filter=Q(grade__school_term=term) & \
                    Q(grade__grade__isnull=False) & \
                    Q(grade__subject__subject__category=utils.CATEGORY_SCIENCE) & \
                    Q(grade__subject__active=True)
        ),
        science_coefs = Sum(
            F('grade__subject__coefficient'),
            filter=Q(grade__school_term=term) & \
                    Q(grade__grade__isnull=False) & \
                    Q(grade__subject__subject__category=utils.CATEGORY_SCIENCE) & \
                    Q(grade__subject__active=True)
        ),
        science_avg = ExpressionWrapper(
            Coalesce(F('science_total'), Value(0)) / Coalesce(F('science_coefs'), Value(1)),
            output_field=FloatField()
        ),
        other_total = Sum(
            ExpressionWrapper( 
                F('grade__grade') * F('grade__subject__coefficient'),
                output_field=FloatField()
            ), 
            filter=Q(grade__school_term=term) & \
                    Q(grade__grade__isnull=False) & \
                    Q(grade__subject__subject__category=utils.CATEGORY_OTHER) & \
                    Q(grade__subject__active=True)
        ),
        other_coefs = Sum(
            F('grade__subject__coefficient'),
            filter=Q(grade__school_term=term) & \
                    Q(grade__grade__isnull=False) & \
                    Q(grade__subject__subject__category=utils.CATEGORY_OTHER) & \
                    Q(grade__subject__active=True)
        ),
        other_avg = ExpressionWrapper(
            Coalesce(F('other_total'), Value(0)) / Coalesce(F('other_coefs'), Value(1)),
            output_field=FloatField()
        )
    )
