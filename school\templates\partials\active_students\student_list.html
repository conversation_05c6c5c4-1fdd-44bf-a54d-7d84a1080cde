
{% load static %}
{% load humanize %}
{% load widget_tweaks %}
<div class="row">
    <div class="col" hx-get="{{ request.path }}?page={{ page }}{% if education %}&education={{ education}}{% endif %}" hx-trigger="saved from:body" hx-target="#app-content">
        <div class="tile">
            {% if active_nav == 'all' %}
            <div class="tile-title-w-btn">
              <div class="d-flex justify-content-between">
                  {% if perms.school.add_enrollment %}
                    {% if school_plan != PLAN_LEVEL %}
                    <div class="btn-group show-on-pc">
                      <a class="btn btn-success" href="{% url 'school:student_add' %}" 
                          hx-get="{% url 'school:student_add' %}" 
                          hx-target="#dialog-xl">+ Nouvelle inscription</a>
                    </div>
                    <div class="btn-group show-on-phone">
                        <a class="btn btn-success" href="{% url 'school:student_add_wizard' %}" 
                          hx-get="{% url 'school:student_add_wizard' %}" 
                          hx-target="#dialog">+ Nouvelle inscription</a>
                    </div>
                    {% else %}
                      <div class="btn-group">
                        <a class="btn btn-success" href="{% url 'school:student_add' %}" 
                            hx-get="{% url 'school:student_add' %}" 
                            hx-target="#dialog-xl">+ Nouvelle inscription</a>
                      </div>
                    {% endif %}
                  {% endif %}
                </div>
                <a class="btn btn-sm btn-primary" 
                        href="{% url 'school:student_edit_list' %}">
                    <i data-feather="edit-2" class="align-middle"></i> Mode édition
                </a>
              <div class="{% if user.school.education == 'F' %}d-none{% endif %}">
                <select name="cycle" id="cycle" 
                      class="form-control form-control-sm {% if cycle %} bg-warning border-warning {% endif %}"
                      hx-get="{{ request.path }}"
                      hx-include="[name=search]">
                  <option value="">Filtrer par Cycle</option>
                  <option value="PF" {% if cycle == 'PF' %} selected {% endif %}>Primaire Français</option>
                  <option value="PA" {% if cycle == 'PA' %} selected {% endif %}>Primaire Arabe</option>
                  <option value="SF" {% if cycle == 'SF' %} selected {% endif %}>Secondaire Français</option>
                  <option value="SA" {% if cycle == 'SA' %} selected {% endif %}>Secondaire Arabe</option>
                </select>
              </div>
            </div>
            {% else %}
                <div class="form-group col-md-6">
                    <label for="generic_level">Niveau</label>
                    <select name="generic_level" id="generic_level" class="form-control w-auto" 
                            hx-get="{% url 'school:students_level_attribution' %}?education={{education}}">
                        <option value="">-------------------------</option>
                        {% for level in generic_levels %}
                        <option value="{{ level.id }}" {% if selected_level == level.id %} selected="selected" {% endif %}>{{ level }}</option>
                        {% endfor %}
                    </select>
                </div>
            {% endif %}
            {% if active_nav != 'ar' and active_nav != 'fr' %}
                <div class="form-group row">
                    <div class="col-8">
                      <label for="search" class="pl-2">Rechercher:</label>
                      <div class="form-group has-search">
                          <span class="icon" data-feather="search"></span>
                          <input type="search" id="search" name="search" class="form-control" 
                            placeholder="Rechercher par NOM ou par MATRICULE"
                            value="{{ search }}"
                              hx-get="{{ request.path }}"
                              hx-include="[name=level_fr], [name=level_ar], [name=generic_level_fr], [name=generic_level_ar], [name=status], [name=cycle]"
                              hx-vals='{"statut": "{{active_nav}}" }'>
                      </div>
                    </div>
                    <div class="col-4">
                      <label for="search" class="pl-2 text-muted">Afficher</label>
                      <select name="per_page" id="per_page" class="form-control" 
                          hx-get="{{ request.path }}?page={{ page }}{% if education %}&education={{ education}}{% endif %}&search={{ search }}"
                          hx-include="[name=gender], [name=cycle], [name=birth_year], [name=generic_level_fr], [name=level_fr], [name=status]{% if user.school.education == EDUCATION_ARABIC %}, [name=generic_level_ar], [name=level_ar]{% endif %}">
                        <option value="10" {% if per_page|floatformat:'0' == '10' %} selected="selected" {% endif %}>10 élèves</option>
                        <option value="25" {% if per_page|floatformat:'0' == '25' %} selected="selected" {% endif %}>25 élèves</option>
                        <option value="50" {% if per_page|floatformat:'0' == '50' %} selected="selected" {% endif %}>50 élèves</option>
                        <option value="100" {% if per_page|floatformat:'0' == '100' %} selected="selected" {% endif %}>100 élèves</option>
                        <option value="200" {% if per_page|floatformat:'0' == '200' %} selected="selected" {% endif %}>200 élèves</option>
                        <option value="300" {% if per_page|floatformat:'0' == '300' %} selected="selected" {% endif %}>300 élèves</option>
                      </select> 
                    </div>
                  </div>
                {% endif %}
            <form class="table-responsive" id="students_form" novalidate method="post">
                {% csrf_token %}

                {% if result_found %}
                  <div class="alert alert-warning font-weight-bold d-flex justify-content-between border">
                    <span><span data-feather="filter" class="feather-16 align-middle mr-2"></span> {{ result_found }} résultats</span>
                    <span><span data-feather="chevrons-right" class="feather-16 align-middle"></span> Garçons : {{ result_boys }} || Filles : {{ result_girls }}</span>
                  </div>
                {% endif %}
                <table class="table table-striped table-sm table-hover table-bordered checkbox-table" {% if active_nav == 'ar' or active_nav == 'fr' %} id="datatable" {% endif %} style="font-size: 11.2px;">
                    <thead class="bg-primary text-white">
                    <tr id="select-all-input">
                        <th class="align-middle">
                            <input type="checkbox" name="select-all" id="select-all">
                        </th>
                        {% if active_nav != 'ar' and active_nav != 'fr' and per_page == '10' or per_page == '25' or per_page < 50 %}
                        <th class="align-middle">Photo</th>
                        {% endif %}

                        <th style="min-width: 120px;" class="align-middle sticky-col sticky-header">Nom et Prénoms</th>
                        <th class="align-middle">Matricule</th>
                        <th class="text-center align-middle" style="max-width: auto;">
                          {% if filter_form and not filter_form.gender.value %}
                            {% render_field filter_form.gender class='form-control mt-0 bg-light px-0 w-auto m-auto' style='height: 30px; border-radius: 2px; font-size: 11px;' %}
                          {% elif 'gender' in filter_form.fields %}
                              {% render_field filter_form.gender class='form-control w-auto mt-0 px-0 bg-warning m-auto' style='height: 30px; border-radius: 2px; font-size: 11px;' %}
                          {% else %}
                          Sexe
                          {% endif %}
                        </th>
                        <th class="align-middle">
                          {% if filter_form and not filter_form.birth_year.value %}
                            {% render_field filter_form.birth_year class='form-control mt-0 bg-light px-0 w-auto m-auto' style='height: 30px; border-radius: 2px; font-size: 11px;' %}
                          {% elif 'birth_year' in filter_form.fields %}
                            {% render_field filter_form.birth_year class='form-control w-auto mt-0 px-0 bg-warning m-auto' style='height: 30px; border-radius: 2px; font-size: 11px;' %}
                          {% else %}
                            Né le
                          {% endif %}
                        </th>
                        <th class="align-middle">Lieu</th>
                        {% if active_nav == 'ar' or active_nav == 'fr' %}
                          <th>Classe</th>
                        {% elif school_plan != PLAN_LEVEL %}
                          <th>
                            {% if not filter_form.generic_level_fr.value %}
                                {% render_field filter_form.generic_level_fr class='form-control w-auto mt-0 bg-light' style='height: 30px; border-radius: 2px; font-size: 11px;' %}
                              {% else %}
                                {% render_field filter_form.generic_level_fr class='form-control w-auto mt-0 bg-warning' style='height: 30px; border-radius: 2px; font-size: 11px;' %}
                            {% endif %}
                          </th>
                          {% if user.school.education == EDUCATION_ARABIC %}
                            <th class="align-middle">
                              {% if not filter_form.generic_level_ar.value %}
                                  {% render_field filter_form.generic_level_ar class='form-control w-auto mt-0 bg-light' style='height: 30px; border-radius: 2px; font-size: 11px;' %}
                                {% else %}
                                  {% render_field filter_form.generic_level_ar class='form-control w-auto mt-0 bg-warning' style='height: 30px; border-radius: 2px; font-size: 11px;' %}
                              {% endif %}
                            </th>
                          {% endif %}
                        {% endif %}
                        <th class="align-middle">
                          {% comment %}
                          {% if not filter_form.status.value %}
                              {% render_field filter_form.status class='form-control w-auto mt-0 bg-light' style='height: 30px; border-radius: 2px; font-size: 11px;' %}
                            {% else %}
                              {% render_field filter_form.status class='form-control w-auto mt-0 bg-warning' style='height: 30px; border-radius: 2px; font-size: 11px;' %}
                          {% endif %}
                          {% endcomment %}
                          Statut
                        </th>
                        <th class="align-middle">Qualité</th>
                        {% if active_nav != 'ar' and active_nav != 'fr' %}
                        <th class="align-middle">Nationalité</th>
                        <th class="align-middle text-center">Actions</th>
                        {% endif %}
                    </tr>
                    </thead>
                    <tbody>
                    {% if not education or selected_level %}
                    {% for enrollment in enrollments %}
                    <tr class="checkbox">
                        <td class="">
                            <input type="checkbox" name="check-{{enrollment.id}}" id="check-{{enrollment.id}}" class="row-checkbox">
                        </td>

                        {% if active_nav != 'ar' and active_nav != 'fr' and per_page == '10' or per_page == '25' or per_page < 50 %}
                        <td class="align-middle text-center p-0 " style="min-width: 60px;" hx-get="{% url 'school:student_edit_wizard' enrollment.id %}?page={{ page }}&{% if level_fr %}level_fr={{ level_fr }}{% elif level_ar %}level_ar={{ level_ar}}{% endif %}"
                        hx-target="#dialog">
                          {% if enrollment.student.photo %}
                          <img data-original="{{ enrollment.student.photo.url }}" 
                                  alt="" 
                                  class="lazy border img-thumbnail rounded-circle">
                          {% elif enrollment.is_second_cycle_fr and enrollment.student.student_id %}
                              <img data-original="{{ enrollment.student.government_photo }}" 
                                  alt="" 
                                  class="lazy border img-thumbnail rounded-circle"
                                  id="{{ enrollment.id }}"
                                  onload="if (this.src.endsWith('CC')) {
                                    this.src = '{{ enrollment.student.blank_photo }}'
                                  }">
                            {% else %}
                            <img data-original="{{ enrollment.student.blank_photo }}" 
                                  alt="" 
                                  class="lazy border img-thumbnail rounded-circle">
                            {% endif %}
                        </td>
                        {% endif %}
                        
                        <td class="border align-middle sticky-col {% if not forloop.counter|divisibleby:'2' %} bg-lightgray {% else %} bg-white {% endif %}">
                          <a href="" hx-get="{% url 'school:student_detail' enrollment.id %}" hx-push-url="{% url 'school:student_detail' enrollment.id %}" hx-target="#app-content">{{ enrollment }}</a>
                          {% if user.school.education == 'A' and enrollment.student.full_name_ar %}
                          <br>
                          <span class="text-muted">{{ enrollment.student.full_name_ar }}</span> 
                          {% endif %}
                        </td>
                        
                        <th class="align-middle" style="font-weight: normal;">{{ enrollment.student.student_id|default_if_none:'' }}</th>
                        <td class="align-middle text-center">{{ enrollment.student.gender }}</td>
                        <td class="align-middle">{{ enrollment.student.birth_date_str }} {% if active_nav != 'ar' and active_nav != 'fr' %} <br> <span class="text-muted">{{ enrollment.age }} ans </span>{% endif %}</td>
                        <td class="align-middle text-center">
                          {{ enrollment.student.birth_place|slice:15 }}
                          {% if user.school.education == 'A' and enrollment.student.birth_place_ar %}
                          <br>
                          <span class="text-muted">{{ enrollment.student.birth_place_ar }}</span> 
                          {% endif %}
                        </td>
                        {% if active_nav == 'ar' %}
                        <td class="align-middle" style="min-width: 60px;">{{ enrollment.level_ar|default_if_none:'N/A' }}</td>
                        {% elif active_nav == 'fr' %}
                        <td class="align-middle" style="min-width: 60px;">{{ enrollment.level_fr|default_if_none:'N/A' }}</td>
                        {% elif school_plan != PLAN_LEVEL %}
                        <td class="align-middle" style="min-width: 60px;">{{ enrollment.level_fr|default_if_none:enrollment.generic_level_fr }}</td>
                          {% if user.school.education == EDUCATION_ARABIC %}
                          <td class="align-middle" style="min-width: 60px;">{{ enrollment.level_ar|default_if_none:enrollment.generic_level_ar|default_if_none:'-' }}</td>
                          {% endif %}
                        {% endif %}
                        <td class="align-middle text-center">{{ enrollment.get_aff }}</td>
                        <td class="align-middle">{{ enrollment.qualite }}</td>
                        {% if active_nav != 'ar' and active_nav != 'fr' %}
                        <td class="align-middle">{{ enrollment.student.get_nationality_display|slice:15 }}</td>

                        <td class="align-middle text-center d-flex" style="min-width: 60px;">
                          <a href="#" hx-get="{% url 'school:student_edit_wizard' enrollment.id %}?page={{ page }}"
                          hx-target="#dialog" class="show-on-phone btn btn-xs btn-primary text-white" title="Modifier infos" data-toggle="tooltip">
                         <span data-feather="edit" class="feather-16"></span>
                       </a>
                       <a href="#" hx-get="{% url 'school:student_edit' enrollment.id %}?page={{ page }}"
                           hx-target="#dialog-xl" class="show-on-pc btn btn-xs btn-primary text-white" title="Modifier infos" data-toggle="tooltip">
                           <span data-feather="edit" class="feather-16"></span>
                       </a>
                       <!-- <a href="#" hx-get="{% url 'school:student_delete' enrollment.id %}?page={{ page }}&{% if level_fr %}level_fr={{ level_fr }}{% elif level_ar %}level_ar={{ level_ar}}{% endif %}"
                              hx-target="#dialog" class="text-danger ml-2 btn btn-xs btn-danger" title="Supprimer élève" data-toggle="tooltip">
                              <span data-feather="trash-2" class="feather-16 text-white"></span>
                          </a> -->
                          <div class="dropdown">
                            <button class="btn btn-xs dropdown" style="background-color: inherit;" type="button" data-toggle="dropdown" aria-expanded="false">
                                <i data-feather="more-vertical" class="feather-16"></i>
                            </button>
                            <div class="dropdown-menu" style="font-size: 0.8rem;">
                                <a class="dropdown-item" 
                                  href="{% url 'school:fiche_scolarite_pdf' enrollment.id %}" hx-target="#dialog">
                                  Fiche de scolarité 
                                </a>
                                <div class="dropdown-divier"></div>
                                <a class="dropdown-item" onclick="Pace.restart()"
                                  hx-get="{% url 'school:student_delete' enrollment.id %}?page={{ page }}&{% if level_fr %}level_fr={{ level_fr }}{% elif level_ar %}level_ar={{ level_ar}}{% endif %}"
                                  hx-target="#dialog">
                                  Supprimer élève 
                                </a>
                            </div>
                          </div>
                        </td>
                        {% endif %}
                    </tr>
                    {% endfor %}
                    {% endif %}
                    </tbody>
                </table>
                {% if active_nav != 'ar' and active_nav != 'fr' %}
                {% include 'partials/pagination.html' with include_items="[name=level_fr], [name=cycle], [name=level_ar], [name=search], [name=generic_level_fr], [name=generic_level_ar], [name=status], [name=gender], [name=per_page]" vals_items='{"statut": "{{active_nav}}" }' %}
                {% endif %}
                {% if active_nav == 'ar' or active_nav == 'fr' %}
                  <div class="bg-light p-1 rounded mt-2 border">
                    <label for="" class="ml-3">Classe à attribuer:</label>
                    <div class="form-group col-md-4 d-flex">
                      <select name="level_to_attribute" id="level_to_attribute" 
                        class="form-control" required="required">
                        <option value="">-------------------------</option>
                        {% for level in selected_levels %}
                        <option value="{{ level.id }}">{{ level }}</option>
                        {% endfor %}
                      </select>
                      <button class="btn btn-success ml-3" {% if not selected_levels.exists %} disabled="disabled" {% endif %} 
                        hx-post="{% url 'school:attribute_level' %}" 
                        hx-include="[name^=check], [name=level_to_attribute]"
                        hx-vals='{"education": "{{ education }}"}'>Attribuer</button>
                    </div>
                  </div>
                {% else %}
    
                {% include 'partials/student/actions.html' %}
    
                {% endif %}
            </form>
        </div>
    </div>
</div>
<script>
  document.addEventListener("DOMContentLoaded", function(ev) {
      feather.replace();
      $("img.lazy").lazyload();

  });

  if (typeof(feather) != "undefined") {
      feather.replace();
      $("img.lazy").lazyload();
  };

  $(document).ready(function(){
        $(".checkbox-table").simpleCheckboxTable();
    });

</script>