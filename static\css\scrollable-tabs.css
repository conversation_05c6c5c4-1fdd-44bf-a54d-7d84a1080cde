/* Custom styles for scrollable tabs */
.nav-tabs-container {
    position: relative;
    overflow: hidden;
    margin-bottom: 1rem;
}

.nav-tabs-scroll {
    overflow-x: auto;
    overflow-y: hidden;
    display: flex;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

.nav-tabs-scroll::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

.nav-tabs {
    flex-wrap: nowrap;
    border-bottom: none;
    min-width: 100%;
}

.nav-tabs .nav-item {
    white-space: nowrap;
}

.scroll-button {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1;
    background-color: rgba(255, 255, 255, 0.8);
    border: none;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    cursor: pointer;
    transition: all 0.3s ease;
}

.scroll-button:hover {
    background-color: rgba(255, 255, 255, 1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.scroll-button:focus {
    outline: none;
}

.scroll-button-left {
    left: 0;
}

.scroll-button-right {
    right: 0;
}

.scroll-button.disabled {
    opacity: 0.3;
    cursor: default;
}

/* Hide scroll buttons on larger screens */
@media (min-width: 992px) {
    .scroll-button {
        display: none;
    }
}

/* Dark mode support */
.dark-mode .scroll-button {
    background-color: rgba(52, 58, 64, 0.8);
    color: #fff;
}

.dark-mode .scroll-button:hover {
    background-color: rgba(52, 58, 64, 1);
}

.dark-mode .nav-tabs-container {
    border-bottom: 1px solid #495057;
}

/* Make sure the active tab is visible */
.nav-tabs .nav-link.active {
    position: relative;
    z-index: 2;
}
