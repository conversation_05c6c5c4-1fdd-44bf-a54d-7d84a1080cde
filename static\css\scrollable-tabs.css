/* Custom styles for scrollable tabs */
.nav-tabs-container {
    position: relative;
    overflow: hidden;
    margin-bottom: 1rem;
    padding: 0 30px; /* Make room for the scroll buttons */
}

.nav-tabs-scroll {
    overflow-x: auto;
    overflow-y: hidden;
    display: flex;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
    width: 100%;
    padding-bottom: 2px; /* Prevent bottom border from being cut off */
}

.nav-tabs-scroll::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

.nav-tabs {
    flex-wrap: nowrap;
    border-bottom: none;
    display: flex;
}

.nav-tabs .nav-item {
    white-space: nowrap;
    display: flex;
}

.nav-tabs .nav-link {
    white-space: nowrap;
    padding: 0.5rem 1rem;
    display: flex;
    align-items: center;
}

.scroll-button {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 5;
    background-color: rgba(255, 255, 255, 0.9);
    border: 1px solid #dee2e6;
    border-radius: 50%;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 0;
}

.scroll-button:hover {
    background-color: rgba(255, 255, 255, 1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    border-color: #adb5bd;
}

.scroll-button:focus {
    outline: none;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.scroll-button-left {
    left: 0;
}

.scroll-button-right {
    right: 0;
}

.scroll-button.disabled {
    opacity: 0.3;
    cursor: default;
    pointer-events: none;
}

/* Show scroll buttons on all screen sizes when needed */
@media (min-width: 992px) {
    .nav-tabs-container {
        padding: 0 40px; /* More space for buttons on larger screens */
    }

    .scroll-button {
        width: 32px;
        height: 32px;
    }
}

/* Dark mode support */
.dark-mode .scroll-button {
    background-color: rgba(52, 58, 64, 0.9);
    color: #fff;
    border-color: #495057;
}

.dark-mode .scroll-button:hover {
    background-color: rgba(52, 58, 64, 1);
    border-color: #6c757d;
}

.dark-mode .scroll-button:focus {
    box-shadow: 0 0 0 0.2rem rgba(52, 58, 64, 0.25);
}

.dark-mode .nav-tabs-container {
    border-bottom: 1px solid #495057;
}

.dark-mode .nav-tabs .nav-link {
    color: #adb5bd;
}

.dark-mode .nav-tabs .nav-link.active {
    color: #fff;
    background-color: #343a40;
    border-color: #495057 #495057 #343a40;
}

/* Make sure the active tab is visible */
.nav-tabs .nav-link.active {
    position: relative;
    z-index: 2;
}
