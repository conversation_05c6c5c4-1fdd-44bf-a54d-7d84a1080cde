from django.db.models.functions import Coalesce
from django.db.models import Sum, F, IntegerField, Subquery, OuterRef
from school.models import Year, School, Enrollment, Subscription, Payment
from users.models import CustomUser
from celery import shared_task
from exams.grades_utils import create_default_subjects_and_terms
from exams.models import EducationYearResult, SchoolTerm, LevelSubject
from school.models import Subscription, School, Level, GenericLevel, Message, MessageBalance
from main.sms import send_sms
from main import utils
from .school_utils import get_next_level

@shared_task(bind=True)
def create_default_subjects_and_terms_task(self, school_id):
    year = Year.objects.get(active=True)
    school = School.objects.get(pk=school_id)
    create_default_subjects_and_terms(school=school, year=year)

  
@shared_task(bind=True)
def create_levels_for_school(self, school_id, plan, year_id, level_id=None):
    school = School.objects.get(pk=school_id)
    year = Year.objects.get(pk=year_id)
    plan_level = None

    if level_id:
        plan_level = GenericLevel.objects.get(pk=level_id)

    objs_to_create = []
    education = school.education
    if plan == Subscription.PLAN_LEVEL:
        obj = Level(
            education=utils.EDUCATION_FRENCH,
            generic_level=plan_level,
            school=school, year=year, 
            number=str(plan_level), max=70)
        objs_to_create.append(obj)

        if education == utils.EDUCATION_ARABIC:
            obj = Level(
                education=utils.EDUCATION_ARABIC,
                generic_level=plan_level,
                school=school, year=year, 
                number=str(plan_level), max=70)
            objs_to_create.append(obj)

    else:
        cycle = school.cycle
        generic_levels = GenericLevel.objects.all()
        if cycle != utils.CYCLE_BOTH:
            generic_levels = generic_levels.filter(cycle=cycle)

        for generic_level in generic_levels:
            obj = Level(
                education=utils.EDUCATION_FRENCH,
                generic_level=generic_level,
                school=school, year=year, 
                number=str(generic_level), max=70)
            objs_to_create.append(obj)

            if education == utils.EDUCATION_ARABIC:
                obj = Level(
                    education=utils.EDUCATION_ARABIC,
                    generic_level=generic_level,
                    school=school, year=year, 
                    number=str(generic_level), max=70)
                objs_to_create.append(obj)
    Level.objects.bulk_create(objs_to_create)


@shared_task(bind=True)
def initialize_school_for_active_year(self, school_id, user_id):
    year = Year.objects.get(active=True)
    school = School.objects.get(pk=school_id)
    previous_plan = Subscription.objects.filter(school=school, year=year.previous).first()
    if not Subscription.objects.filter(school=school, year=year).exists():
        Subscription.objects.create(
            school=school, year=year, 
            plan=previous_plan.plan, 
            level=previous_plan.level)

    previous_year = year.previous
    user = CustomUser.objects.get(pk=user_id)
    current_students_qs = Enrollment.objects \
        .filter(school=school, year=year) \
        .only('id', 'student__identifier') \
        .values('student__identifier')
    current_students_ids_list = [student['student__identifier'] for student in current_students_qs]

    queryset = Enrollment.objects \
        .filter(school=school, year=previous_year, active=True) \
        .exclude(student__identifier__in=current_students_ids_list)
    queryset = queryset.annotate(
            fees=Sum('enrollment_fees', distinct=True) + \
                    Sum('year_fees', distinct=True) + \
                    Sum('annexe_fees', distinct=True),

            inscription=Sum('payment__inscription'),
            scolarite=Sum('payment__amount'),
            annexe=Sum('payment__annexe'),
            paid = Coalesce(F('inscription'), 0, output_field=IntegerField()) + \
                   Coalesce(F('scolarite'), 0, output_field=IntegerField()) + \
                   Coalesce(F('annexe'), 0, output_field=IntegerField()),
            remaining=F('fees') - F('paid'),
            mga_fr= \
                Subquery(queryset=EducationYearResult.objects \
                    .filter(education=utils.EDUCATION_FRENCH, enrollment__id=OuterRef('pk')).only('average') \
                    .values('average')[:1]
            ),
            annual_rank_fr= \
                Subquery(queryset=EducationYearResult.objects \
                    .filter(education=utils.EDUCATION_FRENCH, enrollment__id=OuterRef('pk')) \
                    .only('rank') \
                    .values('rank')[:1]
            ),
            annual_decision_fr= \
                Subquery(queryset=EducationYearResult.objects \
                    .filter(education=utils.EDUCATION_FRENCH, enrollment__id=OuterRef('pk')) \
                    .only('decision') \
                    .values('decision')[:1]
            ),
            mga_ar= \
                Subquery(queryset=EducationYearResult.objects \
                    .filter(education=utils.EDUCATION_ARABIC, enrollment__id=OuterRef('pk')).only('average') \
                    .values('average')[:1]
            ),
            annual_rank_ar= \
                Subquery(queryset=EducationYearResult.objects \
                    .filter(education=utils.EDUCATION_ARABIC, enrollment__id=OuterRef('pk')) \
                    .only('rank') \
                    .values('rank')[:1]
            ),
            annual_decision_ar= \
                Subquery(queryset=EducationYearResult.objects \
                    .filter(education=utils.EDUCATION_ARABIC, enrollment__id=OuterRef('pk')) \
                    .only('decision') \
                    .values('decision')[:1]
            ),
        )
   
    # Copy students to new year
    objs_to_create = []
    for enrollment in queryset:
        next_level_fr = enrollment.generic_level_fr
        next_level_ar = enrollment.generic_level_ar

        if enrollment.mga_fr and enrollment.annual_decision_fr and enrollment.annual_decision_fr == 'A':
            next_level_fr = get_next_level(enrollment.generic_level_fr)
        if enrollment.mga_ar and enrollment.annual_decision_ar == 'A' and enrollment.annual_decision_ar == 'A':
            next_level_ar = get_next_level(enrollment.generic_level_ar)

        obj = Enrollment(
              school=school, year=year, student=enrollment.student,
              generic_level_fr=next_level_fr,
              generic_level_ar=next_level_ar,
              previous_level_name_fr=enrollment.generic_level_fr.short_name if enrollment.generic_level_fr else '',
              previous_level_name_ar=enrollment.generic_level_ar.short_name if enrollment.generic_level_ar else '',
              debt=enrollment.remaining if enrollment.remaining >= 0 else 0, 
              previous_mga_fr=enrollment.mga_fr,
              previous_decision_fr=enrollment.annual_decision_fr,
              previous_mga_ar=enrollment.mga_ar,
              previous_decision_ar=enrollment.annual_decision_ar,
              lv2=enrollment.lv2, status=enrollment.status,
              qualite=enrollment.qualite,
              active=False, has_scolarship=enrollment.has_scolarship,
         )
        objs_to_create.append(obj)
    Enrollment.objects.bulk_create(objs_to_create)

    # Create subjects and terms
    create_default_subjects_and_terms(school=school, year=year, french_only=True)
    if school.education != utils.EDUCATION_FRENCH:
        duplicate_arabic_terms_and_subjects(school=school, previous_year=previous_year, current_year=year)


def duplicate_arabic_terms_and_subjects(school, previous_year, current_year):
    terms = school.schoolterm_set.filter(year=previous_year, education=utils.EDUCATION_ARABIC)
    subjects = school.levelsubject_set.filter(year=previous_year, subject__education=utils.EDUCATION_ARABIC)
    terms_to_create = []
    subjects_to_create = []
    for term in terms:
        terms_to_create.append(
            SchoolTerm(
                year=current_year,
                school=school,
                education=term.education,
                cycle=term.cycle,
                term=term.term,
                level=term.level,
                coefficient=term.coefficient,
                max=term.max,
                active=term.active,
            )
        )

    for subject in subjects:
        subjects_to_create.append(
            LevelSubject(
                year=current_year,
                school=school,
                level=subject.level,
                coefficient=subject.coefficient,
                max=subject.max,
                active=subject.active,
                order=subject.order,
                subject=subject.subject
            )
        )

    SchoolTerm.objects.bulk_create(terms_to_create)
    LevelSubject.objects.bulk_create(subjects_to_create)


@shared_task(bind=True)
def send_sms_notification(self, session_key, enrollment_id, payment_id):
    enrollment = Enrollment.objects.get(pk=enrollment_id)
    payment = Payment.objects.get(pk=payment_id)
    student = enrollment.student
    phone = student.father_phone or student.phone 
    if phone:
        balance, created = MessageBalance.objects.get_or_create(
                defaults={'balance': 0, 'used': 0}, school=enrollment.school
        )
        phone = phone.split('/')[0] \
            .replace('-', '') \
            .replace('.', '') \
            .replace('_', '') \
            .replace(' ', '')
        if len(phone) == 10 and not created and balance.balance >= 2:
            phone = f'+225{phone}'
            payment_value = f'{payment.get_total():,}'.replace(',', ' ')
            total_paid = enrollment.get_payments_total()
            total_paid_str = f'{total_paid:,}'.replace(',', ' ')
            remaining = enrollment.get_fees_total() - total_paid
            remaining_str = f'{remaining:,}'.replace(',', ' ')
            message = f"{enrollment.school}: Nous confirmons votre paiement de {payment_value} FCFA " \
                        f"le {payment.created_at.strftime('%d-%m-%Y %H:%M')} pour {enrollment}{(' Matricule: ' + student.student_id) if student.student_id else ''}.\nTotal paiements: {total_paid_str} FCFA\n" \
                        f"Reste a payer: {remaining_str} F CFA. \n\nMerci de nous faire confiance,\nLa Direction de l'école"
            sms_sent = send_sms(session_key, phone, message)

            if sms_sent:
                Message.objects.create(
                    year=enrollment.year,
                    school=enrollment.school,
                    content=message,
                    to=phone,
                    student=enrollment,
                    sms_count=2,
                    sms_type=Message.PAIMENT_NOTIFICATION,
                )