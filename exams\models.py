from django.db import models
from django.db.models import Q
from django.db.models.query import QuerySet
from django.core.cache import cache
from django.utils.translation import gettext_lazy as _
from school import models as school_models
from main import utils

class SubjectManager(models.Manager):
    def for_school(self, user, education, cycle=None):
        queryset = super().get_queryset().filter(education=education)
        queryset = queryset.filter(
                   Q(school__isnull=True) | Q(school__id=user.school_id)
                )
        queryset = queryset.filter(Q(cycle__isnull=True) | Q(cycle=cycle) | Q(cycle=utils.CYCLE_BOTH))
        return queryset
    
    def get_queryset(self):
        cache_key = 'subjects'
        cached = cache.get(cache_key)
        if cached:
            return cached
        qs = super().get_queryset()
        cache.set(cache_key, qs, timeout=60 * 5)
        return qs


class Subject(models.Model):
    name = models.CharField(_('nom de la matière'), 
           max_length=255)
    translation = models.CharField(_('traduction'), 
                  max_length=255, null=True, blank=True)
    cycle = models.CharField(_('cycle'), max_length=1, 
            choices=utils.CYCLE_CHOICES, null=True)
    abbreviation = models.CharField(
        _('abbréviation'), max_length=20, 
        help_text=_('8 caractères au maximum'))
    education = models.CharField(_('éducation'), 
                choices=utils.EDUCATION_CHOICES, 
                default=utils.EDUCATION_ARABIC, 
                max_length=1)
    school = models.ForeignKey(school_models.School, 
             on_delete=models.SET_NULL, null=True, blank=True)
    category = models.CharField(max_length=1, 
                                choices=utils.CATEGORY_CHOICES, 
                                null=True, blank=True,
                                default=utils.CATEGORY_OTHER)
    code = models.CharField(_('code matière'), max_length=25, 
                            unique=True, null=True, db_index=True)
    is_sous_matiere = models.BooleanField(
        _('Marquer comme sous-matière'), default=False)
    objects = SubjectManager()

    class Meta:
        verbose_name = _('matière')
    
    def __str__(self):
        return self.abbreviation or self.name


class LevelSubjectManager(models.Manager):
    def for_school_all(self, user, level_name, education, year):
        return super().get_queryset().select_related('subject')\
            .filter(
                school__id=user.school_id, 
                level__short_name__iexact=str(level_name).lower(),
                subject__education=education, year=year
            ).order_by('order')

    def for_school(self, user, level_name, education, year):
        return self.for_school_all(user, level_name, education, year).filter(active=True)


class LevelSubject(models.Model):
    year = models.ForeignKey(
        school_models.Year, on_delete=models.CASCADE, 
        null=True, blank=True)
    subject = models.ForeignKey(Subject, on_delete=models.CASCADE)
    school = models.ForeignKey(school_models.School, 
             on_delete=models.SET_NULL, null=True, blank=True)
    level = models.ForeignKey(school_models.GenericLevel, 
            on_delete=models.PROTECT, verbose_name=_('niveau'),
            null=True, blank=True)
    coefficient = models.PositiveIntegerField(_('coefficient'), default=1)
    active = models.BooleanField(_('utilisé?'), default=True)
    order = models.PositiveIntegerField(_("ordre d'apparution"), default=1)
    max = models.PositiveIntegerField(
        _('matière sur'), 
        help_text=_('valeur maximale de la matière'), 
        default=10)
    objects = LevelSubjectManager()

    class Meta:
        verbose_name = _('matière par niveau')
        verbose_name_plural = _('matières par niveau')
        unique_together = [
            ['year', 'subject', 'level', 'school']
        ]
        ordering = ['order']

    def __str__(self):
        return f'{self.subject}'.strip()
    
    def get_first_chars(self):
        return self.__str__()[:5]


class TermManager(models.Manager):
    def get_for_cycle(self, year, user, cycle=None, education=utils.EDUCATION_FRENCH):
        queryset = self.get_queryset()
        if cycle == utils.CYCLE_PRIMARY and education == utils.EDUCATION_FRENCH:
            queryset = queryset.filter(cycle=cycle, education=education)
        else:
            queryset = queryset.filter(cycle__isnull=True)
        return queryset      
    
    def get_queryset(self):
        cache_key = 'terms'
        cached = cache.get(cache_key)
        if cached:
            return cached
        qs = super().get_queryset()
        cache.set(cache_key, qs, timeout=60 * 5)
        return qs
    
    
class Term(utils.TimeStampedModel):
    name = models.CharField(_('libellé'), max_length=255)
    abbreviation = models.CharField(max_length=25)
    education = models.CharField(max_length=255, choices=utils.EDUCATION_CHOICES,
                                 default=utils.EDUCATION_FRENCH)
    cycle = models.CharField(max_length=1, choices=utils.CYCLE_CHOICES,
            default=utils.CYCLE_PRIMARY, null=True, blank=True)
    translation = models.CharField(_('traduction en arabe') ,
                  max_length=255, null=True, blank=True)
    code = models.CharField(_('code période'), max_length=10, null=True, db_index=True)
    number = models.PositiveSmallIntegerField(null=True, blank=True)
    order = models.PositiveSmallIntegerField(default=0)
    objects = TermManager()
    
    def __str__(self):
        return self.name.upper()

    def used(self, school):
        return SchoolTerm.objects.filter(exam=self, school=school).exists()

    class Meta:
        verbose_name = _('période')
        ordering = ['education', 'order']


class SchoolTermManager(models.Manager):
    def for_year(self, year, user, level, education=utils.EDUCATION_FRENCH):
        queryset = super().get_queryset().filter(
            school=user.school, year=year, education=education,
            level__isnull=False, level=level)
        return queryset.select_related('term', 'year')

    def active(self, year, user, level, education=utils.EDUCATION_FRENCH):
        return self.for_year(year, user, level, education) \
            .filter(active=True)


class SchoolTerm(utils.TimeStampedModel):
    year = models.ForeignKey(school_models.Year, 
        on_delete=models.PROTECT, null=True, blank=True)
    school = models.ForeignKey(school_models.School, 
        on_delete=models.CASCADE, null=True, blank=True)
    education = models.CharField(max_length=1, choices=utils.EDUCATION_CHOICES)
    cycle = models.CharField(max_length=1, choices=utils.CYCLE_CHOICES)
    term = models.ForeignKey(Term, on_delete=models.PROTECT)
    level = models.ForeignKey(school_models.GenericLevel, null=True, 
        blank=True, on_delete=models.CASCADE)
    coefficient = models.PositiveSmallIntegerField(default=1)
    max = models.PositiveSmallIntegerField(_('période sur'), default=10)
    start = models.DateField(null=True, blank=True)
    end = models.DateField(null=True, blank=True)
    active = models.BooleanField(default=True)
    allow_marking = models.BooleanField(default=True)
    objects = SchoolTermManager()
    class Meta:
        verbose_name = _("période de l'école")
        verbose_name_plural = _("périodes de l'école")

    def __str__(self):
        return f'{self.term}'


class GradeManager(models.Manager):
    def for_level(self, level, term=None):
        queryset = super().get_queryset()
        education = level.education

        if education == utils.EDUCATION_ARABIC:
            queryset = queryset.filter(
                enrollment__level_ar=level,
                enrollment__active=True)
        else:
            queryset = queryset.filter(
                enrollment__level_fr=level,
                enrollment__active=True)

        if term:
            queryset = queryset.filter(school_term=term)
        return queryset.select_related('enrollment__student')


class Grade(models.Model):
    enrollment = models.ForeignKey(school_models.Enrollment, 
                 on_delete=models.CASCADE, verbose_name=_('élève'))
    subject = models.ForeignKey(LevelSubject, on_delete=models.CASCADE, 
              verbose_name=_('matière'))
    school_term = models.ForeignKey(SchoolTerm, on_delete=models.CASCADE, 
                  verbose_name=_('période'))
    grade = models.DecimalField(_('note'), decimal_places=2,
            max_digits=5, null=True, blank=True)
    rank = models.PositiveSmallIntegerField(default=0)
    is_ex = models.BooleanField(default=False)
    updated_by = models.ForeignKey('users.CustomUser', 
                 on_delete=models.SET_NULL, null=True)
    objects = GradeManager()
    
    class Meta:
        verbose_name = _('note')
        ordering = ['subject__order']
        permissions = (
            ('manage_all_grades', _('can manage grades for french and arabic')),
            ('manage_french_grades', _('can manage french grades')),
            ('manage_arabic_grades', _('can manage arabic grades')),
        )

    def get_rank(self):
        return f"{self.rank}{'ex' if self.is_ex else 'e'}" 

    def get_rank_if_grade(self):
        if self.grade:
            return self.get_rank()
        return '-'
    
    def __str__(self):
        return f'Note {self.subject} {self.school_term} - {self.grade}'


class GroupAverageManager(models.Manager):
    def for_group(self, group, term, level=None):
        queryset = self.get_queryset().filter(group=group, term=term)\
            .select_related('enrollment')

        if level and level.education == utils.EDUCATION_FRENCH:
            return queryset.filter(enrollment__level_fr=level)
        elif level and level.education == utils.EDUCATION_ARABIC:
            return queryset.filter(enrollment__level_ar=level)
        return queryset


class StudentSubjectGroupAverage(models.Model):
    enrollment = models.ForeignKey(school_models.Enrollment, 
                                   on_delete=models.CASCADE)
    term = models.ForeignKey(SchoolTerm,
                             on_delete=models.CASCADE,
                             null=True, blank=True)
    group = models.CharField(max_length=1, 
            choices=utils.CATEGORY_CHOICES)
    average = models.DecimalField(decimal_places=2, 
                                  max_digits=4, default=0)
    rank = models.PositiveSmallIntegerField(default=0)
    is_ex = models.BooleanField(default=False)
    objects = GroupAverageManager()

    class Meta:
        unique_together = [
            ['enrollment', 'term', 'group']
        ]
        verbose_name = _('bilan périodique')
        verbose_name_plural = _('bilans périodiques')

    def get_rank(self):
        return f"{self.rank}{'ex' if self.is_ex else 'e'}"
    

class TermResult(models.Model):
    school_term = models.ForeignKey(SchoolTerm, on_delete=models.CASCADE, null=True)
    enrollment = models.ForeignKey(school_models.Enrollment, 
                 on_delete=models.CASCADE)
    total = models.DecimalField(_('total'), default=0, 
            decimal_places=2, max_digits=5, null=True)
    average = models.DecimalField(_('moyenne'), 
              decimal_places=2, max_digits=5, 
              null=True)
    average_with_coef = models.DecimalField(
        _('moyenne coefficientée'),
        decimal_places=2, max_digits=6, default=0, 
        null=True, blank=True)
    rank = models.PositiveSmallIntegerField(default=0)
    is_ex = models.BooleanField(default=True)
    french_average = models.DecimalField(
        decimal_places=2, max_digits=5, null=True)
    french_rank = models.PositiveSmallIntegerField(null=True)
    french_coefs = models.PositiveSmallIntegerField(null=True)
    french_is_ex = models.BooleanField(default=False)

    class Meta:
        verbose_name = _('résultat périodique')
        verbose_name_plural = _('résultats périodiques')
        unique_together = [
            ['school_term', 'enrollment']
        ]

    def __str__(self):
        return str(self.enrollment) + ' - ' + str(self.school_term) + \
                ' - ' + str(self.average) 

    def get_rank(self):
        return utils.get_rank_str(self.rank, self.is_ex)
    
    def get_french_rank(self):
        return f"{self.french_rank}{'e' if not self.french_is_ex else 'ex'}"
    
    def get_distinction(self, education=utils.EDUCATION_FRENCH, 
                        average=None, return_language=None):

        average_str = ''
        if not average:
            if self.average:
                average_str = str(int(self.average)) 
            else:
                average_str = 0
        else:
            average_str = str(int(average)) 
        
        if (education == utils.EDUCATION_ARABIC) and \
            (return_language == utils.EDUCATION_ARABIC):
            return utils.APPRECIATIONS_AR_SUR_20.get(average_str,'-')
        return utils.APPRECIATIONS_SUR_20.get(average_str,'-')
    
    def get_distinction_10(self, education=utils.EDUCATION_FRENCH, return_language=None):
        average_str = ''
        if self.average:
            average_str = str(int(self.average)) 
        else:
            average_str = 0
        
        if education == utils.EDUCATION_ARABIC and \
            (return_language == utils.EDUCATION_ARABIC):
            return utils.APPRECIATIONS_AR_SUR_10.get(average_str,'-')
        return utils.APPRECIATIONS_SUR_10.get(average_str,'-')
    
    def compute_distinction(self, education=utils.EDUCATION_FRENCH, 
                            max=None, return_language=None):
        if not max:
            max = self.school_term.max
        
        if max == 20:
            return self.get_distinction(education, return_language=return_language)
        elif max == 10:
            return self.get_distinction_10(education, return_language=return_language)
        
        if max == 0:
            max = 1

        average = self.average or 0
        quotient = float(average) / (max / 20)
        return self.get_distinction(education, quotient, return_language)
    
    def get_appreciation(self):
        return utils.DISTINCTIONS.get(str(int(self.average or 0)))

    def get_decision(self, max=None, short=True):
        if not max:
            max = self.school_term.max
        return utils.get_decision(self.average, max)
        # return 'A' if self.average and self.average >= (max / 2) else 'NA'


class EducationYearResult(utils.TimeStampedModel):
    DECISION_ADMITTED = 'A'
    DECISION_STAYS_DOWN = 'R'
    DECISION_DISMISSED = 'E'
    DECISION_CHOICES = (
        (DECISION_ADMITTED, 'Admis'),
        (DECISION_STAYS_DOWN, 'Redouble'),
        (DECISION_DISMISSED, 'Exclu(e)'),
    )
    enrollment = models.ForeignKey(
                 school_models.Enrollment, on_delete=models.CASCADE)
    total = models.PositiveSmallIntegerField(_('total'), default=0)
    average = models.DecimalField(_('moyenne'), decimal_places=2, 
              default=0, max_digits=5)
    class_average = models.DecimalField(
        _('moy. classe'), decimal_places=2, 
              default=0, max_digits=5,
              null=True, blank=True
    )
    admission_average = models.DecimalField(
        _('moy. passage'), decimal_places=2, 
              default=0, max_digits=5,
              null=True, blank=True
    )
    average_with_coef = models.DecimalField(
        _('moyenne coefficientée'),
        decimal_places=2, max_digits=5, default=0, 
        null=True, blank=True)
    rank = models.PositiveSmallIntegerField(default=0)
    education = models.CharField(
        max_length=1, choices=utils.EDUCATION_CHOICES)
    decision = models.CharField(max_length=1, choices=DECISION_CHOICES,
               null=True, blank=True)
    is_ex = models.BooleanField(default=False)
    class Meta:
        verbose_name = _('Résultat annuel')
        unique_together = [
            ['enrollment', 'education'],
        ]

    def __str__(self):
        return f'Résultat annuel de: {str(self.enrollment)}'

    def get_rank(self):
        return utils.get_rank_str(self.rank, self.is_ex)
    
    def get_decision(self, max=20, level_code=None):
        return utils.get_decision(self.average, max, False, level_code=level_code)
    
    def get_distinction(self, max=20):
        return utils.compute_distinction(self.average, max, self.education)
    
    def get_distinction_ar(self, max=20):
        return utils.compute_distinction(self.average, max, self.education, utils.EDUCATION_ARABIC)

    def get_appreciation(self):
        return utils.DISTINCTIONS.get(str(int(self.average or 0)))


class CombinedYearResult(utils.TimeStampedModel):
    DECISION_ADMITTED = 'A'
    DECISION_STAYS_DOWN = 'R'
    DECISION_DISMISSED = 'E'
    DECISION_CHOICES = (
        (DECISION_ADMITTED, 'Admis'),
        (DECISION_STAYS_DOWN, 'Redouble'),
        (DECISION_DISMISSED, 'Exclu(e)'),
    )
    enrollment = models.OneToOneField(
                 school_models.Enrollment, 
                 on_delete=models.CASCADE)
    total = models.PositiveSmallIntegerField(_('total'), default=0)
    average = models.DecimalField(_('moyenne'), decimal_places=2, 
              default=0, max_digits=5)
    average_with_coef = models.DecimalField(
        _('moyenne coefficientée'),
        decimal_places=2, max_digits=5, default=0, 
        null=True, blank=True)
    rank = models.PositiveSmallIntegerField(default=0)
    decision = models.CharField(max_length=1, choices=DECISION_CHOICES,
               null=True, blank=True)
    class Meta:
        verbose_name = _('Résulat Général Annuel')


class SubjectKit(utils.TimeStampedModel):
    name = models.CharField(max_length=255)
    cycle = models.CharField(_('cycle'), max_length=1, 
            choices=utils.CYCLE_CHOICES, null=True)
    education = models.CharField(_('éducation'), 
                choices=utils.EDUCATION_CHOICES,
                max_length=1)
    levels = models.ManyToManyField(school_models.GenericLevel)
    subjects = models.ManyToManyField(LevelSubject)

    class Meta:
        verbose_name = _('Kit de matières')

    def __str__(self):
        return self.name
    

class CompleteCycleSubjectKit(models.Model):
    name = models.CharField(max_length=255)
    cycle = models.CharField(_('cycle'), max_length=1, 
            choices=utils.CYCLE_CHOICES, null=True)
    education = models.CharField(_('éducation'), 
                    choices=utils.EDUCATION_CHOICES,
                    max_length=1)
    association = models.CharField(
        max_length=2, choices=utils.ASSOCIATION_CHOICES,
        null=True, blank=True)   
    subject_kits = models.ManyToManyField(
        SubjectKit, verbose_name=_('packs de matière'))

    class Meta:
        verbose_name = 'Kit complet par cycle'
        verbose_name = 'Kits complets par cycle'

    def __str__(self):
        return self.name


class TermKit(utils.TimeStampedModel):
    name = models.CharField(max_length=255)
    cycle = models.CharField(_('cycle'), max_length=1, 
            choices=utils.CYCLE_CHOICES, null=True)
    education = models.CharField(_('éducation'), 
                choices=utils.EDUCATION_CHOICES,
                max_length=1)
    levels = models.ManyToManyField(school_models.GenericLevel)
    terms = models.ManyToManyField(SchoolTerm)

    class Meta:
        verbose_name = _('Kit de périodes')

    def __str__(self):
        return self.name
    

class CompleteTermKit(models.Model):
    name = models.CharField(max_length=255)
    cycle = models.CharField(_('cycle'), max_length=1, 
            choices=utils.CYCLE_CHOICES, null=True)
    education = models.CharField(_('éducation'), 
                    choices=utils.EDUCATION_CHOICES,
                    max_length=1)
    term_kits = models.ManyToManyField(
        TermKit, verbose_name=_('kit de périodes'))

    class Meta:
        verbose_name = 'Kit complet de périodes'
        verbose_name = 'Kits complet de périodes'

    def __str__(self):
        return self.name
    

class LevelStatisticsManager(models.Manager):
    def for_school(self, year, school, term, level=None):
        queryset = self.get_queryset().filter(
            level__year=year, level__school=school)

        if term:
            queryset = queryset.filter(term=term)
        
        if level:
            queryset = queryset.filter(level=level)
        return queryset


class LevelStatistics(utils.TimeStampedModel):
    """ Stores statistics about the level for different terms. 
    Term=None refers to annual statistics """
    level = models.ForeignKey(school_models.Level, on_delete=models.CASCADE)
    term = models.ForeignKey(SchoolTerm, on_delete=models.CASCADE, null=True)
    boys_present = models.PositiveSmallIntegerField(default=0)
    boys_admitted = models.PositiveSmallIntegerField(default=0)
    boys_perc = models.DecimalField(decimal_places=2, max_digits=5, default=0)
    girls_present = models.PositiveSmallIntegerField(default=0)
    girls_admitted = models.PositiveSmallIntegerField(default=0)
    girls_perc = models.DecimalField(decimal_places=2, max_digits=5, default=0)
    min_average = models.DecimalField(decimal_places=2, max_digits=5, default=0)
    max_average = models.DecimalField(decimal_places=2, max_digits=5, default=0)
    level_average = models.DecimalField(decimal_places=2, max_digits=5, default=0)
    objects = LevelStatisticsManager()

    class Meta:
        verbose_name = 'Statistiques périodique'
        unique_together = [
            ['level', 'term']
        ]

    def get_level_perc(self):
        admitted = self.boys_admitted + self.girls_admitted
        students_present = self.boys_present + self.girls_present
        return (admitted / (students_present or 1)) * 100