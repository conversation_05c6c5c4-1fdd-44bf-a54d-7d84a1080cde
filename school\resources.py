from import_export import resources
from import_export.fields import Field
from school import models as school_models
from exams.models import EducationYearResult
from main import utils
from django.db.models import Count, Q, F


class StudentResource(resources.ModelResource):
    date_naissance = Field(column_name='date_naissance')
    lieu = Field(column_name='localite')
    phone = Field(column_name='contact')

    class Meta:
        model = school_models.Enrollment
        fields = [
            'student__student_id', 
            'student__last_name', 
            'student__first_name', 
            'student__full_name_ar',
            'student__gender',
            'generic_level_fr__short_name',
            'level_fr__number',
            'student__birth_day',
            'student__birth_month',
            'student__birth_year',
            'date_naissance',
            'lieu',
            'student__father',
            'student__mother',
            'status',
            'qualite',
            'phone',
            'student__birth_place_ar',
            'generic_level_ar__short_name',
            'level_ar__number',
        ]
        export_order = fields

    def __init__(self, no_arabic=True, **kwargs):
        super().__init__(**kwargs)
        print(no_arabic)
        self.no_arabic = no_arabic

    def dehydrate_date_naissance(self, enrollment):
        return enrollment.student.birth_date_str()
    
    def dehydrate_phone(self, enrollment):
        return enrollment.student.father_phone or enrollment.student.phone
    
    def dehydrate_lieu(self, enrollment):
        return enrollment.student.birth_place

    def dehydrate_student__student_id(self, enrollment):
        return enrollment.student.student_id or enrollment.student.identifier

    def get_queryset(self, **kwargs):
        return school_models.Enrollment.objects.for_user(
            user=kwargs.get('user'), year=kwargs.get('year')
        ).select_related(
                'student', 'level_fr', 'level_ar', 'generic_level_fr', 
                'generic_level_ar') \
        .annotate(
            is_second_cycle_fr=(
                Q(generic_level_fr__cycle=utils.CYCLE_SECONDARY)
            )
        )

    def get_export_headers(self, selected_fields=None):
        headers = super().get_export_headers(selected_fields=selected_fields)
        custom_headers = {
            'student__student_id': 'matricule',
            'student__last_name': 'nom',
            'student__first_name': 'prenoms',
            'student__gender': 'sexe',
            'generic_level_fr__short_name': 'niveau',
            'level_fr__number': 'classe',
            'generic_level_ar__short_name': 'niveau_ar',
            'level_ar__number': 'classe_ar',
            'student__birth_day': 'jour',
            'student__birth_month': 'mois',
            'student__birth_year': 'annee',
            'student__full_name_ar': 'nom_complet_ar',
            'student__birth_place_ar': 'localite_ar',
            'student__father': 'pere',
            'student__mother': 'mere',
            'status': 'statut',
            'qualite': 'qualite',
            'student__phone': 'contact'
        }
        for i, header in enumerate(headers):
            if header in custom_headers:
                headers[i] = custom_headers[header]
        return headers

    def get_fields(self, **kwargs):
        fields = super().get_fields(**kwargs)
        if self.no_arabic:
            for field in fields:
                print(field.column_name)
                if str(field.column_name).endswith('_ar'):
                    fields.remove(field)
        return fields
    

class LevelResource(resources.ModelResource):
    boys = Field(column_name='boys')
    girls = Field(column_name='girls')
    students = Field(column_name='students')

    def dehydrate_boys(self, level):
        return level.boys
    
    def dehydrate_girls(self, level):
        return level.girls
    
    def dehydrate_students(self, level):
        return level.students

    def get_queryset(self, **kwargs):
        user = kwargs.get('user')
        year = kwargs.get('year')
        education = kwargs.get('education')
        levels_checked = kwargs.get('levels_checked')
        
        qs = school_models.Level.objects \
            .for_user(user=user, year=year, education=education) \
            .filter(id__in=levels_checked)
        
        if education == utils.EDUCATION_FRENCH:
            qs = qs.annotate(
                boys=Count('enrollment', filter=Q(enrollment__student__gender=utils.GENDER_MALE) & Q(enrollment__active=True), distinct=True),
                girls=Count('enrollment', filter=Q(enrollment__student__gender=utils.GENDER_FEMALE, enrollment__active=True), distinct=True),
                students=Count('enrollment', distinct=True),
            )  
        else:
            qs = qs .annotate(
                boys=Count('enrollment_ar', filter=Q(enrollment_ar__student__gender=utils.GENDER_MALE, enrollment_ar__active=True), distinct=True),
                girls=Count('enrollment_ar', filter=Q(enrollment_ar__student__gender=utils.GENDER_FEMALE, enrollment_ar__active=True), distinct=True),
                students=Count('enrollment_ar', distinct=True),
            )
        
        return qs.order_by('generic_level__order', 'number')

    def get_export_headers(self):
        headers = super().get_export_headers()
        custom_headers = {
            'generic_level__short_name': 'niveau',
            'number': 'classe',
            'boys': 'garçons',
            'girls': 'filles',
            'students': 'effectif',
        }
        for i, header in enumerate(headers):
            if header in custom_headers:
                headers[i] = custom_headers[header]
        return headers
    
    class Meta:
        model = school_models.Level
        fields = [
            'generic_level__short_name', 'number', 'boys', 'girls', 'students'
        ]
        export_order = fields




class StudentResourceForCertificate(resources.ModelResource):
    date_naissance = Field(column_name='date_naissance')
    lieu = Field(column_name='localite')
    phone = Field(column_name='contact')
    mga = Field(column_name='mga')
    mention = Field(column_name='mention')
    mention_arabe = Field(column_name='mention_arabe')

    class Meta:
        model = school_models.Enrollment
        fields = [
            'student__student_id', 
            'student__last_name', 
            'student__first_name', 
            'student__gender',
            'generic_level_fr__short_name',
            'level_fr__number',
            'generic_level_ar__short_name',
            'level_ar__number',
            'student__birth_day',
            'student__birth_month',
            'student__birth_year',
            'date_naissance',
            'lieu',
            'student__birth_place_ar',
            'student__father',
            'student__mother',
            'phone',
            'student__full_name_ar',
            'mga',
            'mention'
        ]
        export_order = fields

    def dehydrate_date_naissance(self, enrollment):
        return enrollment.student.birth_date_str()
    
    def dehydrate_phone(self, enrollment):
        return enrollment.student.father_phone or enrollment.student.phone
    
    def dehydrate_lieu(self, enrollment):
        return enrollment.student.birth_place

    def dehydrate_student__student_id(self, enrollment):
        return enrollment.student.student_id or enrollment.student.identifier
    
    def dehydrate_mga(self, enrollment):
        result = enrollment.educationyearresult_set.filter(education=utils.EDUCATION_ARABIC).first()
        return round(result.average, 2) if result else 0
    
    def dehydrate_mention(self, enrollment):
        if not enrollment.generic_level_ar:
            return ''
        result = enrollment.educationyearresult_set.filter(education=utils.EDUCATION_ARABIC).first()
        code = enrollment.generic_level_ar.short_name
        return result.get_distinction(10 if code and code == 'CM2' else 20) if result else ''
    
    def dehydrate_mention_arabe(self, enrollment):
        if not enrollment.generic_level_ar:
            return ''
        result = enrollment.educationyearresult_set.filter(education=utils.EDUCATION_ARABIC).first()
        code = enrollment.generic_level_ar.short_name
        return result.get_distinction_ar(10 if code and code == 'CM2' else 20) if result else ''

    def get_queryset(self, **kwargs):
        return school_models.Enrollment.objects.for_user_minimum(
            user=kwargs.get('user'), year=kwargs.get('year')
        )

    def get_export_headers(self, selected_fields=None):
        headers = super().get_export_headers(selected_fields=selected_fields)
        custom_headers = {
            'student__student_id': 'matricule',
            'student__last_name': 'nom',
            'student__first_name': 'prenoms',
            'student__gender': 'sexe',
            'generic_level_fr__short_name': 'niveau',
            'level_fr__number': 'classe',
            'generic_level_ar__short_name': 'niveau_ar',
            'level_ar__number': 'classe_ar',
            'student__birth_day': 'jour',
            'student__birth_month': 'mois',
            'student__birth_year': 'annee',
            'student__full_name_ar': 'nom_arabe',
            'student__father': 'pere',
            'student__mother': 'mere',
            'student__phone': 'contact',
            'student__birth_place_ar': 'lieu_arabe',
        }
        for i, header in enumerate(headers):
            if header in custom_headers:
                headers[i] = custom_headers[header]
        return headers


class DFARessource(resources.ModelResource):
    year_code = Field(column_name='annee')
    mga = Field(column_name='mga')
    dfa = Field(column_name='dfa')

    class Meta:
        model = school_models.Enrollment
        fields = [
            'year_code', 'student__student_id',
            'student__last_name', 
            'student__first_name',
            'generic_level_fr__short_name', 
            'mga', 'dfa'
        ]
        export_order = [
            'year_code', 'student__student_id', 'student__last_name', 
            'student__first_name', 'generic_level_fr__short_name', 'mga', 'dfa', 
        ]

    def dehydrate_year_code(self, enrollment):
        year = str(enrollment.year)
        return f'{year[2:4]}{year[7:9]}'
    
    def dehydrate_mga(self, enrollment):
        result = enrollment.educationyearresult_set.filter(education=utils.EDUCATION_FRENCH).first()
        return round(result.average, 2) if result else 0
    
    def dehydrate_dfa(self, enrollment):
        result = enrollment.educationyearresult_set.filter(education=utils.EDUCATION_FRENCH).first()
        return result.decision if result else ''
    
    def get_export_headers(self, selected_fields=None):
        headers = super().get_export_headers(selected_fields=selected_fields)
        custom_headers = {
            'student__student_id': 'matricule',
            'student__last_name': 'nom',
            'student__first_name': 'prenoms',
            'generic_level_fr__short_name': 'niveau',
        }

        for i, header in enumerate(headers):
            if header in custom_headers:
                headers[i] = custom_headers[header]
        return headers
    

class StaffResource(resources.ModelResource):

    class Meta:
        model = school_models.StaffCard
        exclude = ['id', 'created_at', 'updated_at']