{% csrf_token %}
{% if term %}
<select name="selected-term" id="selected-term" class="d-none">
    <option value="{{ term }}">Période</option>
</select>
{% endif %}

<div class="mb-3">
    <div class="alert alert-info">
        <i class="fa fa-info-circle"></i> Les notes sont affichées dans un tableur interactif. Vous pouvez modifier les notes directement dans le tableur.
    </div>
</div>

<div id="spreadsheet-container" class="mb-4"></div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Prepare data for jspreadsheet
    const data = [];

    {% for enrollment, grades in data.items %}
    data.push([
        '{{ enrollment.id }}',
        '{{ enrollment.student.student_id|default_if_none:enrollment.student.identifier }}',
        '{{ enrollment }}',
        {% if lang == 'A' %}
        '{{ enrollment.student.full_name_ar|default_if_none:"" }}',
        {% endif %}
        {% for grade in grades %}
        {% if grade.grade or grade.grade == 0 %}
        '{{ grade.grade }}',
        {% elif not grade.grade and not grade %}
        'NC',
        {% elif not grade.grade %}
        {% if grade > 0 or grade == 0 %}
        '{{ grade }}',
        {% else %}
        'NC',
        {% endif %}
        {% endif %}
        {% endfor %}
        '{{ enrollment.result_total|default_if_none:"" }}',
        '{{ enrollment.result_average|default_if_none:"" }}',
        '{{ enrollment.result_rank|default_if_none:"" }}'
    ]);
    {% endfor %}

    // Get subjects data for AJAX requests
    const subjects = [];
    {% for subject in subjects %}
    subjects.push({
        id: {{ subject.id }},
        name: '{{ subject.subject.name|default:subject.subject.abbreviation }}',
        max: {{ subject.max }}
    });
    {% endfor %}

    // Initialize jspreadsheet
    const spreadsheet = jspreadsheet(document.getElementById('spreadsheet-container'), {
        data: data,
        columns: [
            { type: 'hidden', title: 'ID', width: 50 },
            { type: 'text', title: 'Matricule', width: 100, readOnly: true },
            { type: 'text', title: 'Nom et Prénoms', width: 200, readOnly: true },
            {% if lang == 'A' %}
            { type: 'text', title: 'Nom en Arabe', width: 150, readOnly: true },
            {% endif %}
            {% for subject in subjects %}
            {
                type: 'numeric',
                title: '{{ subject.get_first_chars }} ({{ subject.max }})',
                width: 100,
                mask: '0.00',
                decimal: '.',
                maxLength: 5
            },
            {% endfor %}
            { type: 'numeric', title: 'Total', width: 100, readOnly: true },
            { type: 'numeric', title: 'Moyenne', width: 100, readOnly: true, mask: '0.00', decimal: '.' },
            { type: 'numeric', title: 'Rang', width: 80, readOnly: true }
        ],
        minDimensions: [{{ subjects|length|add:5 }}, {{ data|length }}],
        tableOverflow: true,
        tableHeight: '600px',
        tableWidth: '100%',
        columnSorting: true,
        search: true,
        pagination: 50,
        paginationOptions: [10, 25, 50, 100],
        style: {
            tableHeaderRow: 'background-color: #007bff; color: white;',
        },
        onchange: function(instance, cell, x, y, value) {
            // Get the enrollment ID and subject index
            const enrollmentId = instance.getValueFromCoords(0, y);
            const subjectIndex = x - {% if lang == 'A' %}3{% else %}2{% endif %};

            if (subjectIndex >= 0 && subjectIndex < subjects.length) {
                const subjectId = subjects[subjectIndex].id;

                // Send AJAX request to update grade
                const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;

                fetch('{% url "exams:grade_edit" %}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRFToken': csrfToken,
                        'HX-Request': 'true'
                    },
                    body: new URLSearchParams({
                        'enrollment': enrollmentId,
                        'subject': subjectId,
                        'level': '{{ short_name }}',
                        'education': '{{ lang }}',
                        'grade': value,
                        'term': '{{ term }}'
                    })
                })
                .then(response => {
                    if (response.ok) {
                        // Success - add visual feedback
                        cell.style.backgroundColor = '#d4edda';
                        setTimeout(() => {
                            cell.style.backgroundColor = '';
                        }, 1000);
                    } else {
                        // Error handling
                        cell.style.backgroundColor = '#f8d7da';
                        setTimeout(() => {
                            cell.style.backgroundColor = '';
                        }, 1000);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    cell.style.backgroundColor = '#f8d7da';
                    setTimeout(() => {
                        cell.style.backgroundColor = '';
                    }, 1000);
                });
            }
        }
    });

    // Add custom toolbar with buttons
    const toolbar = document.createElement('div');
    toolbar.className = 'jexcel_toolbar mb-3';
    toolbar.innerHTML = `
        <div class="btn-group">
            <button type="button" class="btn btn-sm btn-outline-primary" id="export-excel">
                <span data-feather="download" class="feather-16 align-middle"></span> Exporter Excel
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary" id="print-sheet">
                <span data-feather="printer" class="feather-16 align-middle"></span> Imprimer
            </button>
            <button type="button" class="btn btn-sm btn-outline-success" id="refresh-data">
                <span data-feather="refresh-cw" class="feather-16 align-middle"></span> Actualiser
            </button>
        </div>
    `;

    document.getElementById('spreadsheet-container').parentNode.insertBefore(toolbar, document.getElementById('spreadsheet-container'));

    // Add event listeners to buttons
    document.getElementById('export-excel').addEventListener('click', function() {
        spreadsheet.download();
    });

    document.getElementById('print-sheet').addEventListener('click', function() {
        spreadsheet.print();
    });

    document.getElementById('refresh-data').addEventListener('click', function() {
        window.location.reload();
    });

    // Initialize feather icons
    if (typeof feather !== 'undefined') {
        feather.replace();
    }
});
</script>
