import requests, os
from typing import Any
from django.db import models, connection
from django.db.models import Prefetch, OuterRef, Subquery
from django.views.decorators.csrf import csrf_exempt
from django.http import HttpResponse, HttpResponseRedirect, FileResponse, JsonResponse
from django_htmx.http import HttpResponseClientRedirect, HttpResponseClientRefresh, retarget
from django.shortcuts import render
from django.urls import reverse
from django.views import generic
from django.contrib.auth import mixins, decorators
from django.core.paginator import Paginator
from django.db.models import (
    F, Sum, Q, Count, Case, When, IntegerField, FloatField, Value, Max,
    ExpressionWrapper, Avg)
from django.db.models.functions import Coalesce, Ceil
from tablib import Dataset

import openpyxl
from sweetify import sweetify
from .grades_utils import (
    update_level_term_result,
    is_last_term,
    is_first_term,
    get_last_term,
    create_grade_if_necessary)
from .tasks import (
    go_to_sleep, update_levels_results_task,
    create_grades_for_new_subject, generate_fiche_table,
    generate_reports, all_grades_import_task
)

from . import models, navs, forms
from main import utils as main_utils
from school.views import BaseHTMXRequestsView, BaseView, get_session_year
from school import models as school_models, school_utils
from exams import reports


class CustomPaginatedListView(BaseHTMXRequestsView, generic.ListView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.search_fields = []

    def get_paginate_by(self, queryset):
        page_size = self.request.GET.get('per_page')
        if str(page_size).isnumeric():
            return int(page_size)
        return 10

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        search_value = self.request.GET.get('search')
        page_size = self.request.GET.get('per_page')

        if search_value:
            context['search'] = search_value
        if page_size:
            context['per_page'] = page_size

        return context

    def get_queryset(self):
        queryset = super().get_queryset()
        return self.filter_by_search_value(queryset=queryset)

    def filter_by_search_value(self, queryset):
        search_value = self.request.GET.get('search')
        new_qs = self.model.objects.none()

        if self.search_fields and search_value:
            for field in self.search_fields:
                if not new_qs.exists():
                    filter_expr = {str(field) : search_value}
                    if not str(field).endswith('iexact'):
                        filter_expr = {f'{field}__icontains': search_value}
                    new_qs = self.model.objects.filter(**filter_expr)
            return new_qs
        return queryset


class SchoolTermsListView(mixins.PermissionRequiredMixin,
                          BaseHTMXRequestsView, generic.ListView):
    template_name = 'partials/term/terms_base.html'
    model = models.SchoolTerm
    context_object_name = 'terms'
    permission_required = 'exams.manage_all_grades'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        nav_items = None
        lang = self.request.GET.get('lang', main_utils.EDUCATION_FRENCH)
        nav_items = navs.get_terms_navs(
            self.request,
            self.get_year(),
            reverse('exams:terms'), lang)

        context['partial_template'] = 'partials/term/terms_list.html'
        context['nav_items'] = nav_items
        context['lang'] = lang
        context['year'] = self.get_year()
        context['active_nav'] = self.request.GET.get('cycle') or self.request.GET.get('niveau')
        context['level'] = self.request.GET.get('niveau') or ''
        context['cycle'] = self.request.GET.get('cycle') or ''
        context['page_title'] = 'Gestion des périodes'
        return context

    def get_queryset(self):
        user = self.request.user
        year = self.get_year()
        school = user.get_school()
        education = self.request.GET.get('lang',
                    main_utils.EDUCATION_FRENCH).upper()[0]
        level_name = self.request.GET.get('niveau')
        level = None
        if level_name:
            level = school_models.GenericLevel.objects.get(short_name=level_name)
        else:
            level = school_models.GenericLevel.objects.filter(cycle=self.request.GET.get('cycle')).first()
        return models.SchoolTerm.objects.for_year(year, user, level, education)


class SchoolTermStatusToggleView(
    mixins.PermissionRequiredMixin, generic.UpdateView):
    model = models.SchoolTerm
    template_name = 'partials/confirm.html'
    permission_required = 'exams.manage_all_grades'
    fields = []

    def form_valid(self, form):
        obj = self.get_object()
        cycle = self.request.GET.get('cycle')
        active = not obj.active
        if not cycle:
            obj.active = active
            obj.save(update_fields=['active'])
        else:
            models.SchoolTerm.objects \
                .filter(level__cycle=cycle, year=obj.year,
                        term__education=obj.education,
                        term__id=obj.term_id,
                        school=obj.school) \
                .update(active=active)

        return HttpResponse(status=204, headers={'HX-Trigger': 'saved'})

    def handle_no_permission(self):
        return HttpResponse(status=403, headers={'HX-Trigger': 'permission_denied'})


class SchoolTermUpdateView(mixins.PermissionRequiredMixin,
                           generic.UpdateView):
    model = models.SchoolTerm
    template_name = 'partials/simple_form.html'
    fields = ['coefficient', 'max']
    permission_required = 'exams.manage_all_grades'

    def form_valid(self, form):
        cycle = self.request.GET.get('cycle')
        if cycle:
            term = self.get_object()
            models.SchoolTerm.objects.filter(
                term__id=term.term_id, year=term.year,
                level__cycle=cycle, term__education=term.education,
                school=term.school
            ).update(**form.cleaned_data)
        else:
            form.save(True)
        return HttpResponse(status=204, headers={'HX-Trigger': 'saved'})

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form_title'] = 'Modifier coefficient'
        return context

    def handle_no_permission(self):
        return HttpResponse(status=403, headers={'HX-Trigger': 'permission_denied'})


class SubjectsListView(mixins.PermissionRequiredMixin,
                       BaseHTMXRequestsView, generic.ListView):
    model = models.Subject
    template_name = 'partials/subject/subjects_base.html'
    context_object_name = 'subjects'
    permission_required = 'exams.manage_all_grades'

    def get_queryset(self):
        user = self.request.user
        cycle = self.request.GET.get('cycle').upper()[0]
        education = main_utils.get_education(self.request)
        return models.Subject.objects.for_school(user, education, cycle)\
               .order_by('name').only('name', 'translation', 'abbreviation', 'code')

    def get_context_data(self, **kwargs):
        lang = self.request.GET.get('lang')
        cycle = self.request.GET.get(
            'cycle', main_utils.EDUCATION_FRENCH).upper()[0]
        nav_items = navs.get_subjects_navs(
            reverse('exams:subjects'), cycle)
        context = super().get_context_data(**kwargs)
        context['active_nav'] = ''
        if lang == 'ar':
            context['active_nav'] = lang
        context['nav_items'] = nav_items
        context['lang'] = lang
        context['partial_template'] = 'partials/subject/subjects_list.html'
        context['cycle'] = cycle
        context['page_title'] = 'Gestion des matières'
        return context


class SubjectCreateView(mixins.PermissionRequiredMixin,
                        generic.CreateView):
    model = models.Subject
    fields = ['education', 'name', 'abbreviation', 'translation']
    template_name = 'partials/simple_form.html'
    permission_required = 'exams.manage_all_grades'


    def form_valid(self, form):
        form_data = form.cleaned_data
        user = self.request.user
        models.Subject.objects.create(
            school=user.school, cycle=None, code=None, **form_data)
        return HttpResponse(status=204, headers={'HX-Trigger': 'saved'})

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form_title'] = 'Nouvelle matière'
        lang = self.request.GET.get('lang', main_utils.EDUCATION_FRENCH)
        context['form'].fields['education'].initial = lang
        context['form'].fields['education'].widget.attrs['readonly'] = True
        choices = ((main_utils.EDUCATION_FRENCH, 'Français',),)
        if lang.upper()[0] == main_utils.EDUCATION_ARABIC:
            choices = ((main_utils.EDUCATION_ARABIC, 'Arabe',),)
        context['form'].fields['education'].choices = choices
        return context


class LevelSubjectBaseView(mixins.PermissionRequiredMixin,
                           BaseView, generic.View):
    permission_required = 'exams.manage_all_grades'

    def get_queryset(self):
        user = self.request.user
        year = self.get_year()
        level = self.request.GET.get('niveau')
        education = self.request.GET.get('lang').upper()[0]
        return models.LevelSubject.objects \
            .for_school(user, level, education, year)

    def handle_no_permission(self):
        return HttpResponse(
            status=403,
            headers={'HX-Trigger': 'permission_denied'})



class LevelSubjectsListView(BaseHTMXRequestsView,
                            LevelSubjectBaseView,
                            generic.ListView):

    model = models.LevelSubject
    template_name = 'partials/subject/subjects_base.html'
    context_object_name = 'level_subjects'
    permission_required = 'exams.manage_all_grades'

    def get_queryset(self, niveau=None):
        year = self.get_year()
        user = self.request.user
        education = self.request.GET.get('lang')[0].upper()
        level = self.request.GET.get('niveau') or niveau
        return models.LevelSubject.objects.for_school_all(
            user, level, education, year
        )

    def get_context_data(self, **kwargs):
        url = reverse('exams:level_subjects')
        lang = self.request.GET.get(
               'lang', main_utils.EDUCATION_FRENCH).upper()[0]
        nav_items = navs.get_level_subjects_navs(
            self.request.user, self.get_year(), url, lang)
        context = super().get_context_data(**kwargs)
        context['partial_template'] = 'partials/subject/level_subjects.html'
        context['lang'] = lang
        context['nav_items'] = nav_items
        context['active_nav'] = self.request.GET.get('niveau')
        context['page_title'] = 'Repartition des matières par niveau'
        context['title'] = 'Repartition des matières'
        context['subtitle'] = 'Organisez les matières par niveau selon vos préférences'

        if not 'niveau' in self.request.GET:
            level = school_models.GenericLevel.objects.for_school(
                user=self.request.user, year=self.get_year()
            ).first().short_name
            context['level_subjects'] = self.get_queryset(level)
            context['niveau'] = level
            context['active_nav'] = level
        return context


class LevelSubjectsUpdateView(
    generic.UpdateView, LevelSubjectBaseView):
    model = models.LevelSubject
    template_name = 'partials/simple_form.html'
    fields = ['max', 'coefficient']
    permission_required = 'exams.manage_all_grades'


    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form_title'] = 'Edition de matière par classe'
        return context

    def form_valid(self, form):
        obj = self.get_object()
        obj.coefficient = form.cleaned_data['coefficient']
        obj.max = form.cleaned_data['max']
        obj.save(update_fields=['coefficient', 'max'])
        return HttpResponse(status=204, headers={'HX-Trigger': 'saved'})


def level_subject_order_change_view(request):
    data = {
        str(key).replace('order_', ''): request.POST[key] for key in request.POST if str(key).startswith('order_')
    }
    education = str(request.GET.get('lang', main_utils.EDUCATION_FRENCH))[0]
    year = get_session_year(request)
    level_name = request.GET.get('level')
    subjects = models.LevelSubject.objects.for_school_all(
        request.user, level_name, education, year).only('id', 'order', 'subject__id')
    updated = []
    for subject in subjects:
        if str(subject.id) in data:
            subject.order = data[str(subject.id)]
            updated.append(subject)

    models.LevelSubject.objects.bulk_update(updated, fields=['order'])
    return HttpResponse(status=204, headers={'HX-Trigger': 'saved'})

class LevelSubjectsStatusChangeView(
    generic.UpdateView,
    LevelSubjectBaseView):
    model = models.LevelSubject
    template_name = 'partials/confirm.html'
    fields = []

    def form_valid(self, form):
        obj = self.get_object()
        obj.active = not obj.active
        obj.save(update_fields=['active'])
        return HttpResponse(status=204, headers={'HX-Trigger': 'saved'})


class LevelSubjectCreateView(
    generic.CreateView, LevelSubjectBaseView):
    model = models.LevelSubject
    template_name = 'partials/simple_form.html'
    fields = ['subject', 'coefficient', 'max']

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form_title'] = 'Ajouter une matière'
        education = str(self.kwargs.get('lang')).upper()[0]

        queryset = context['form'].fields['subject'].queryset
        queryset = queryset.filter(Q(education=education) & (Q(school__isnull=True) | Q(school=self.request.user.school)))
        context['form'].fields['subject'].queryset = queryset
        return context

    def form_valid(self, form):
        cd = form.cleaned_data
        user = self.request.user
        year = self.get_year()
        level_name = str(self.kwargs.get('level')).lower()
        level = school_models.GenericLevel.objects.get(
            short_name__iexact=level_name)
        education = str(self.kwargs.get('lang')).upper()[0]
        subjects_count = models.LevelSubject.objects.for_school(
            user, level_name, education, year
        ).count() + 1
        subject = models.LevelSubject.objects.create(
            school=user.school, level=level,
            active=True, order=subjects_count, year=year, **cd)
        # Create empty grade objects for students in level
        create_grades_for_new_subject.delay(
            school_id=user.school_id,
            year_id=year.id, generic_level_id=level.id,
            subject_id=subject.id
        )
        return HttpResponse(status=204, headers={'HX-Trigger': 'saved'})


class GradesNavView(mixins.PermissionRequiredMixin,
                    BaseHTMXRequestsView, generic.TemplateView):
    template_name = 'partials/grade/grade_base.html'
    permission_required = 'exams.manage_all_grades'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        lang = self.request.GET.get('lang').upper()[0]
        list_type = self.request.GET.get('type')
        if list_type == 'tout':
            partial_template ='partials/grade/all_grades/all_grades.html'
        else:
            partial_template ='partials/grade/by_subject/grade_by_subject.html'

        context['partial_template'] = partial_template
        context['generic_levels'] = school_models.GenericLevel.objects \
            .for_school(self.request.user, year=self.get_year())
        context['nav_items'] = navs.get_grades_navs(
            self.request.user,
            reverse('exams:grades'), lang,
            reverse('exams:subject_grade_import'),
            reverse('exams:all_grades_import'),
            reverse('exams:results_update'),
            reverse('exams:lock_marks'))
        context['active_nav'] = list_type
        context['type'] = list_type
        context['lang'] = self.request.GET.get('lang')

        text = ''
        education = lang
        if education == main_utils.EDUCATION_ARABIC:
            text = 'Arabes' if education == main_utils.EDUCATION_ARABIC else 'Français'

        context['title'] = f'Gestion des Moyennes {text}'
        context['subtitle'] = f'Gestion, exportations, importations et calculs des moyennes'
        return context


class GradesBySubjectView(mixins.PermissionRequiredMixin,
    BaseHTMXRequestsView, generic.ListView):
    model = school_models.Enrollment
    template_name = 'partials/grade/by_subject/grade_by_subject_table.html'
    context_object_name = 'enrollments'
    permission_required = 'school.can_manage_level_grades'

    def get_queryset(self):
        user = self.request.user
        year = self.get_year()
        education = self.request.GET.get('lang').upper()[0]
        subject = self.request.GET.get('subject')
        term = self.request.GET.get('term')
        level = self.request.GET.get('level') or 0
        level = school_models.Level.objects.for_user(
            user, year=year, education=education
        ).filter(id=level).first()
        if not level or not subject:
            return school_models.Level.objects.none()

        queryset = school_models.Enrollment.objects.for_user(
            user, user.school, year
        )
        if education == main_utils.EDUCATION_FRENCH:
            queryset = queryset.filter(level_fr=level)
        else:
            queryset = queryset.filter(level_ar=level)
        return queryset.select_related('year', 'student')\
            .annotate(
                grade_value=Sum(
                    'grade__grade',
                    filter=Q(grade__subject__id=subject) &
                           Q(grade__school_term__id=term)
                )
            ).order_by('student__last_name', 'student__first_name')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['generic_levels'] = school_models.GenericLevel.objects \
            .for_school(self.request.user, year=self.get_year())
        context['type'] = self.request.GET.get('type')
        context['lang'] = self.request.GET.get('lang')
        context['short_name'] = self.request.GET.get('short_name')
        context['level'] = self.request.GET.get('level')
        context['term'] = self.request.GET.get('term')
        context['subject'] = self.request.GET.get('subject')
        return context

@decorators.permission_required('exams.manage_all_grades')
def sublevels_view(request):
    year = get_session_year(request)
    level = request.GET.get('short_name') or 0
    generic_level = school_models.GenericLevel.objects.get(
        id=(level or 0)
    )
    lang = request.GET.get('lang')

    if lang:
        lang = lang.upper()[0]
    else:
        lang = request.GET.get('education').upper()[0]
    levels = school_models.Level.objects.for_user(
        request.user, request.user.school, year,lang)\
        .filter(generic_level=generic_level)
    subjects = models.LevelSubject.objects.for_school(
        request.user, generic_level.short_name, education=lang,
        year=year)

    terms = models.SchoolTerm.objects.for_year(
        year, request.user, generic_level, lang)\
        .filter(active=True)
    form = forms.LevelForm(levels, subjects, terms)
    context = {'form': form}
    return render(request, 'partials/level/level_input_form.html', context)

@decorators.permission_required('school.can_manage_level_grades')
def grade_edit_view(request):
    template_name = 'partials/simple_form.html'
    grade = ''
    try:
        grade = float(str(request.GET.get('grade').replace(',', '.')))
    except:
        pass

    form = forms.GradeInputForm(request.POST or None, initial={'grade': grade })

    if request.method == 'POST' and form.is_valid():
        data = form.cleaned_data
        grade_value = data['grade']

        defaults = {'grade': grade_value}
        if 'grade' in data:
            del data['grade']

        grade, created = models.Grade.objects \
            .update_or_create(defaults=defaults, **data)
        education = grade.school_term.education
        context = {
            'enrollment': request.POST.get('enrollment'),
            'term': request.POST.get('school_term'),
            'short_name': request.POST.get('short_name'),
            'level': grade.enrollment.level_fr_id if education == main_utils.EDUCATION_FRENCH else grade.enrollment.level_ar_id,
            'subject': request.POST.get('subject'),
            'education': education,
            'grade_value': float(grade_value) if grade_value else '',
        }
        return render(request, 'partials/grade/by_subject/grade_partial.html', context, status=201)

    enrollment = request.GET.get('enrollment')
    term = request.GET.get('term')
    level_id = request.GET.get('short_name')
    subject = request.GET.get('subject')
    subject_qs = models.LevelSubject.objects.filter(id=subject, active=True)
    subject_obj = subject_qs.first()

    level_qs = school_models.GenericLevel.objects.filter(id=level_id)

    if not level_id or not level_qs.exists():
        level_obj = school_models.Level.objects.get(id=request.GET.get('level'))
        level_id = level_obj.generic_level.id

    year = get_session_year(request)
    enrollment_qs = school_models.Enrollment.objects.for_user(
        request.user, year=year, pk=enrollment
    )
    term_qs = models.SchoolTerm.objects.filter(id=term)

    form.fields['subject'].queryset = subject_qs
    form.fields['subject'].initial = subject_obj
    form.fields['enrollment'].queryset = enrollment_qs
    form.fields['enrollment'].initial = enrollment_qs.first()
    form.fields['school_term'].queryset = term_qs
    form.fields['school_term'].initial = term_qs.first()
    context = {'form': form, 'form_title': 'Edition de note', 'keep_btn': True,
               'custom_target': f"_{request.GET.get('subject')}_{request.GET.get('term')}_{request.GET.get('enrollment')}"}

    return render(request, template_name, context)


class AllGradesView(mixins.PermissionRequiredMixin, BaseHTMXRequestsView, generic.ListView):
    model = school_models.Enrollment
    template_name = 'partials/grade/all_grades/all_grades_table.html'
    context_object_name = 'enrollments'
    permission_required = 'exams.manage_all_grades'

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.search_fields = [
            'student__last_name', 'student__first_name',
            'student__student_id', 'student__identifier',
        ]

    def get_queryset(self):
        user = self.request.user
        year = self.get_year()
        queryset = school_models.Enrollment.objects.for_user(
            user, year=year
        )
        level_id = self.request.GET.get('level')
        queryset = queryset.filter(
            Q(level_fr__id=level_id) | Q(level_ar__id=level_id))

        # queryset = self.filter_by_search_value(queryset=queryset)
        return queryset.select_related('student') \
               .order_by('student__last_name', 'student__first_name')\
               .only(
                   'id', 'student__id', 'student__identifier',
                   'student__gender', 'student__last_name',
                   'student__first_name', 'student__student_id',
                   'student__full_name_ar'
                )

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        context['generic_levels'] = school_models.GenericLevel.objects\
            .for_school(self.request.user, year=self.get_year())
        context['type'] = self.request.GET.get('type')
        context['lang'] = self.request.GET.get('lang')
        context['short_name'] = self.request.GET.get('short_name')

        term_id = self.request.GET.get('term')
        context['term'] = term_id
        context['level'] = self.request.GET.get('level')
        context['report2'] = school_models.PDFFile.CATEGORY_REPORT_WITH_PREVIOUS_TERMS_DATA

        generic_level_id = self.request.GET.get('short_name')
        education = main_utils.get_education(self.request)
        level = school_models.GenericLevel.objects.get(id=generic_level_id)
        user = self.request.user
        year = self.get_year()
        context['subjects'] = models.LevelSubject.objects.for_school(
            user, level.short_name, education=education, year=year
        ).select_related('subject').only(
            'id', 'subject__abbreviation',  'subject__translation',
            'subject__name', 'order', 'max'
        ).order_by('order')

        ids = []
        enrollments = list(context['enrollments'])
        for item in enrollments:
            ids.append(item.id)

        context['data'] = create_grade_if_necessary(self.request.user,
            level=level, term_id=term_id, education=education,
            queryset=self.get_queryset().filter(pk__in=ids), year=year,
            request_data=self.request.GET)

        term = models.SchoolTerm.objects \
        .for_year(year, user, level, education) \
        .only('id', 'term__id', 'year__id') \
        .get(pk=term_id)
        context['average'] = int(term.max / 2)
        context['term_max'] = term.max
        text = ''
        if user.school.education == main_utils.EDUCATION_ARABIC:
            text = 'Arabe' if education == main_utils.EDUCATION_ARABIC else 'Français'

        context['title'] = f'Gestion des notes {text}'
        context['subtitle'] = f'Gestion, exportations, importations des notes et calculs des moyennes'
        context['avg_filter'] = self.request.GET.get('filter') or self.request.GET.get('avg_filter')

        # for query in connection.queries:
        #     print(query, end="\n\n")
        return context


@decorators.permission_required('school.can_manage_level_grades')
def grade_by_subject_export(request):
    user = request.user
    year = get_session_year(request)
    workbook = openpyxl.Workbook()
    sheet = workbook.active
    sheet.title = 'importation'
    sheet['A1'] = 'matricule'
    sheet.column_dimensions['A'].width = 15
    sheet.column_dimensions['B'].width = 40
    data = []
    if request.method == 'POST':
        for key, value in request.POST.items():
            if str(key).isnumeric() and value == 'on':
                data.append(int(key))

        subject = request.POST.get('selected-subject')
        term = request.POST.get('selected-term')

        term_obj = models.SchoolTerm.objects.get(id=term)

        queryset = school_models.Enrollment.objects \
            .for_user(user, year=year) \
            .filter(pk__in=data).select_related('student')\
            .only('id', 'student__id', 'student__student_id',
                  'student__last_name',
                  'student__first_name', 'student__full_name_ar') \
            .annotate(
                grade_value=Sum(
                    'grade__grade',
                    filter=Q(grade__subject__id=subject) &
                           Q(grade__school_term__id=term)
                )
            ).order_by('student__last_name', 'student__first_name')

        level = ''
        subject_obj = ''
        education = term_obj.education
        if term_obj.education == main_utils.EDUCATION_FRENCH:
            level = queryset.first().level_fr
        else:
            level = queryset.first().level_ar
        level = str(level).upper()

        subject_obj = models.LevelSubject.objects.get(id=subject)

        sheet_data = [('matricule', 'nom', 'niveau', f'{subject_obj.subject.code}', f'{term_obj.term.code}', 'rang')]
        for enrollment in queryset:
            row = (
                    enrollment.student.student_id or \
                    enrollment.student.identifier,
                    str(enrollment),
                    str(enrollment.generic_level_fr) if education == main_utils.EDUCATION_FRENCH else str(enrollment.generic_level_ar),
                    enrollment.grade_value or 0,
                    '',
                    '',
                )
            sheet_data.append(row)

        for row_index, (id, name, level_name, grade, average, rank) in enumerate(sheet_data):
            sheet.cell(row=row_index + 1, column=1, value=id)
            sheet.cell(row=row_index + 1, column=2, value=name)
            sheet.cell(row=row_index + 1, column=3, value=level_name)
            sheet.cell(row=row_index + 1, column=4, value=grade or 0)
            sheet.cell(row=row_index + 1, column=5, value=average)
            sheet.cell(row=row_index + 1, column=6, value=rank)

        response = HttpResponse(
                content_type="application/vnd.openxmlformats-officedocument" + \
                ".spreadsheetml.sheet")
        response['Content-Disposition'] = f'attachment; filename="MOYENNES_{level}_{term_obj}_{subject_obj}.xlsx"'
        workbook.save(response)
        return response


def annotate_enrollments_with_students_infos(user, year, term):
    return school_models.Enrollment.objects.for_user(
            user, year=year
        ) \
        .select_related(
                'student', 'generic_level_fr',
                'generic_level_ar', 'year') \
        .annotate(
            is_second_cycle_fr=(
                Q(generic_level_fr__cycle=main_utils.CYCLE_SECONDARY)
            ),
            result_total=Subquery(
            models.TermResult.objects.filter(
                enrollment=OuterRef('pk'), school_term=term
                ).values('total')[:1]
            ),
            result_average=Subquery(
                models.TermResult.objects.filter(
                    enrollment=OuterRef('pk'), school_term=term
                ).values('average')[:1]
            ),
            result_rank=Subquery(
                models.TermResult.objects.filter(
                    enrollment=OuterRef('pk'), school_term=term
                ).values('rank')[:1]
            ),
            result_is_ex=Subquery(
                models.TermResult.objects.filter(
                    enrollment=OuterRef('pk'), school_term=term
                ).values('is_ex')[:1]
            )
        ).only(
            'id', 'qualite', 'status',
            'student__last_name', 'student__full_name_ar',
            'active', 'has_scolarship',
            'qualite', 'status', 'student__birth_place',
            'student__first_name', 'student__gender',
            'student__birth_day', 'student__birth_month',
            'student__birth_year', 'student__student_id',
            'student__photo', 'student__nationality',
            'student__birth_place_ar',
            'student__identifier',
            'generic_level_fr__short_name',
            'generic_level_ar__short_name',
            'generic_level_fr__cycle',
            'generic_level_ar__cycle',
            'generic_level_ar__order',
            'generic_level_fr__order',
            'level_ar__id',
            'level_fr__id',
            'year__id'
        )

def annotate_enrollment_qs_with_term_grades_data(
        queryset, user, term, level,
        report_type=school_models.PDFFile.CATEGORY_REPORT,
        term_is_last=False,
        term_is_first=False):

    year = term.year

    report_with_previous_data = school_models.PDFFile.CATEGORY_REPORT_WITH_PREVIOUS_TERMS_DATA
    is_second_cycle_fr = main_utils.is_second_cycle_fr(level)

    if is_second_cycle_fr or (not report_type == report_with_previous_data):
        queryset = queryset.prefetch_related(
            Prefetch('grade_set', queryset=models.Grade.objects \
                .filter(school_term=term, subject__active=True) \
                .select_related('school_term', 'subject__subject') \
                .order_by('subject__order')\
                .only(
                    'enrollment__id',
                    'grade', 'subject__order',
                    'subject__coefficient',
                    'subject__subject__id',
                    'subject__subject__abbreviation',
                    'subject__subject__translation',
                    'subject__subject__category',
                    'subject__subject__name',
                    'subject__active',
                    'school_term__id',
                    'subject__max'),
            to_attr='filtered_grades')
        )
    else:
        queryset = queryset.prefetch_related(
            Prefetch('grade_set', queryset=models.Grade.objects \
                .filter(
                    school_term__term__order__lte=term.term.order,
                    subject__active=True,
                    school_term__education=term.education) \
                .select_related('school_term', 'subject__subject', 'school_term__term') \
                .order_by('subject__order')\
                .only(
                    'enrollment__id', 'grade', 'subject__order',
                    'subject__coefficient',
                    'subject__subject__id',
                    'subject__subject__abbreviation',
                    'subject__subject__translation',
                    'subject__subject__category',
                    'subject__subject__name',
                    'subject__active',
                    'school_term__id',
                    'school_term__term__id',
                    'subject__max'),
            to_attr='filtered_grades')

        )
    queryset = queryset.order_by('student__last_name', 'student__first_name')

    # Annotate subjects average and terms results
    if term_is_last or (not is_second_cycle_fr and report_type == report_with_previous_data and not term_is_first):
        subjects = list(models.LevelSubject.objects.for_school(
            user=user, level_name=level.generic_level.short_name,
            year=year, education=term.education).only('id', 'subject__id'))
        terms = models.SchoolTerm.objects.active(
            year=year,
            user=user, level=level.generic_level,
            education=term.education
        ).only('id', 'term__id', 'year__id').order_by('term__order')

        if is_second_cycle_fr:
            queryset = queryset.annotate(
                french_avg_summary=Avg('termresult__french_average',
                    filter=Q(termresult__school_term__active=True) & \
                        Q(termresult__french_average__isnull=False)
                )
            )

        # Subjects annual average
        for subject in subjects:
            queryset = queryset.annotate(
                **{f'annual_avg_for_subject_{subject.subject_id}': Avg('grade__grade',
                    filter=Q(grade__subject__subject__id=subject.subject_id) & \
                           Q(grade__school_term__active=True)
                    )
                }
            )

        # Term results
        for i, term_item in enumerate(terms):
            queryset = queryset.annotate(
                **{
                    f'total_for_term_{i + 1}': Subquery(
                    models.TermResult.objects.filter(
                        enrollment=OuterRef('pk'),
                            school_term__active=True,
                            school_term__term__id=term_item.term_id
                        ).only('total').values('total')[:1],
                    ),
                    f'avg_for_term_{i + 1}': Subquery(
                    models.TermResult.objects.filter(
                        enrollment=OuterRef('pk'),
                            school_term__active=True,
                            school_term__term__id=term_item.term_id
                        ).only('average').values('average')[:1],
                    ),
                    f'rank_for_term_{i  + 1}': Subquery(
                    models.TermResult.objects.filter(
                        enrollment=OuterRef('pk'),
                            school_term__active=True,
                            school_term__term__id=term_item.term_id
                        ).only('rank').values('rank')[:1],
                    ),
                    f'is_ex_for_term_{i  + 1}': Subquery(
                    models.TermResult.objects.filter(
                        enrollment=OuterRef('pk'),
                            school_term__active=True,
                            school_term__term__id=term_item.term_id
                        ).only('is_ex').values('is_ex')[:1],
                    ),
                }
            )

        if term_is_last:
            education = term.education
            queryset = queryset.annotate(
                    annual_avg=Subquery(
                        models.EducationYearResult.objects.filter(enrollment__id=OuterRef('pk'),
                        education=education).only('average').values('average')[:1]),
                    annual_rank=Subquery(
                        models.EducationYearResult.objects.filter(enrollment__id=OuterRef('pk'),
                        education=education).only('rank').values('rank')[:1]),
                    annual_is_ex=Subquery(
                        models.EducationYearResult.objects.filter(enrollment__id=OuterRef('pk'),
                        education=education).only('is_ex').values('is_ex')[:1])
                )

    return queryset


@decorators.permission_required('school.can_manage_level_grades')
def grade_actions(request):
    request_data = None
    if request.method == 'POST':
        request_data = request.POST
    else:
        request_data = request.GET

    action = request_data.get('action', 'export_grade')
    year = get_session_year(request)

    if action  == 'export_grade' or action == 'export_grade_for_all_levels':
        return grade_export(request)
    elif action == 'generate_results':
        return results_pdf_view(request)
    elif action == 'generate_report':
        ids = []

        for key, values in request_data.lists():
            if 'on' in values and str(key).isnumeric():
                ids.append(key)
        term_id = request_data.get('selected-term')

        term = None

        if 'all' in request_data:
            level = request.GET.get('level')
            level_obj = school_models.Level.objects.get(
                id=level, school__id=request.user.school_id)

        if 'generic_term' in request_data:
            generic_term= request_data.get('generic_term')
            term = models.SchoolTerm.objects.for_year(
                year=year, user=request.user,
                education=level_obj.education,
                level=level_obj.generic_level).filter(term__id=generic_term).first()
        else:
            term = models.SchoolTerm.objects.get(id=term_id)

        year = get_session_year(request)
        queryset = annotate_enrollments_with_students_infos(
            user=request.user, year=year, term=term)

        if ids:
            queryset = queryset.filter(id__in=ids)
        elif 'all' in request_data:
            education = term.education
            if education == main_utils.EDUCATION_FRENCH:
                queryset = queryset.filter(level_fr__id=level)
            else:
                queryset = queryset.filter(level_ar__id=level)

        # Get level
        level = None
        if term.education == main_utils.EDUCATION_FRENCH:
            level = queryset.first().level_fr
        else:
            level = queryset.first().level_ar

        # Annotate qs with grades
        report_type = request_data.get('report_type') \
            or school_models.PDFFile.CATEGORY_REPORT_WITH_PREVIOUS_TERMS_DATA
        term_is_last = is_last_term(user=request.user, level=level, term=term)
        term_is_first = is_first_term(user=request.user, level=level, term=term)
        queryset = annotate_enrollment_qs_with_term_grades_data(
            queryset,
            user=request.user, term=term, level=level, term_is_first=term_is_first,
            term_is_last=term_is_last, report_type=report_type)

        range_text = ''
        if 'all' in request_data and 'lot' in request_data:
            lot = int(request_data['lot'])
            paginator = Paginator(queryset, 15)
            queryset = paginator.get_page(lot).object_list
            range_text = f' Lot {lot}'

        if term.education == main_utils.EDUCATION_FRENCH:
            if level.generic_level.cycle == main_utils.CYCLE_SECONDARY:
                report = reports.SecondCycleReport()
                report.add_content(queryset, term, year, annual_report=term_is_last)
            else:
                report = reports.PrimaryReportFr()
                report.add_content(request.user, queryset, term, level,
                                   report_type=report_type, is_last_term=term_is_last)
        else:
            if level.generic_level.cycle == main_utils.CYCLE_SECONDARY:
                report = reports.SecondCycleReportAr()
                report.add_content(request.user, queryset, term, level, report_type=report_type)
            else:
                report = reports.PrimaryReportAr()
                report.add_content(request.user, queryset, term, level,
                                   report_type=report_type, is_last_term=term_is_last)

        return report.get_file_response(f'Bulletins {level} {term} {range_text}')

@decorators.permission_required('exams.manage_all_grades')
def grade_export(request):
    user = request.user
    year = get_session_year(request)
    workbook = openpyxl.Workbook()
    sheet = workbook.active
    sheet.title = 'importation'
    sheet.column_dimensions['A'].width = 15
    sheet.column_dimensions['B'].width = 15
    sheet.column_dimensions['C'].width = 30
    data = []
    first_obj = None
    level = None
    if request.method == 'POST':
        for key, value in request.POST.items():
            if str(key).isnumeric() and value == 'on':
                data.append(int(key))

        level_id = request.POST.get('level')

        term = request.POST.get('selected-term') or request.POST.get('term')
        term_obj = models.SchoolTerm.objects.get(pk=term)

        if not level_id:
            first_obj = school_models.Enrollment.objects.get(id=data[0])
            if term_obj.education == main_utils.EDUCATION_FRENCH:
                level = first_obj.level_fr
            else:
                level = first_obj.level_ar
        else:
            level = school_models.Level.objects \
                .for_user(user=request.user, year=year, education=term_obj.education) \
                .get(id=level_id)

        subject = request.POST.get('selected-subject')
        queryset = school_models.Enrollment.objects \
            .for_user(user, year=year)

        order_fields = 'student__last_name', 'student__first_name',
        if term_obj.education    == main_utils.EDUCATION_ARABIC:
            order_fields = 'student__full_name_ar',

        action = request.POST.get('action') or request.GET.get('action')
        if action == 'export_grade':
            if not 'for_all_level_students' in request.GET:
                queryset = queryset.filter(pk__in=data)
            else:
                queryset = main_utils.filter_enrollment_by_level(level, queryset)
        elif action == 'export_grade_for_all_levels':
            if term_obj.education == main_utils.EDUCATION_FRENCH:
                queryset = queryset.filter(generic_level_fr=level.generic_level, active=True)
            else:
                queryset = queryset.filter(generic_level_ar=level.generic_level, active=True)

        queryset = queryset.select_related(
                'student', 'generic_level_ar', 'generic_level_fr')\
            .prefetch_related(
                Prefetch('grade_set', queryset=models.Grade.objects \
                .filter(school_term=term, subject__active=True) \
                .select_related('subject__subject') \
                .order_by('subject__order')\
                .only(
                    'enrollment__id', 'grade', 'subject__order',
                    'subject__subject__id',
                    'subject__subject__abbreviation',
                    'subject__subject__translation',
                    'subject__subject__name',
                    'subject__active',
                    'school_term__id',
                    'subject__max'),
                 to_attr='filtered_grades'), 'termresult_set'
            ) \
            .only(
                  'id', 'student__id', 'student__student_id',
                  'student__last_name', 'student__identifier',
                  'student__first_name', 'student__full_name_ar',
                  'generic_level_ar__short_name',
                  'generic_level_ar__name',
                  'generic_level_fr__short_name',
                  'generic_level_fr__name',) \
            .annotate(
                grade_exists=Count('grade', filter=Q(grade__school_term=term)),
                result_total=Subquery(
                    models.TermResult.objects.filter(
                        enrollment=OuterRef('pk'), school_term=term
                    ).values('total')[:1]
                ),
                result_average=Subquery(
                    models.TermResult.objects.filter(
                        enrollment=OuterRef('pk'), school_term=term
                    ).values('average')[:1]
                ),
                result_rank=Subquery(
                    models.TermResult.objects.filter(
                        enrollment=OuterRef('pk'), school_term=term
                    ).values('rank')[:1]
                ),
            )\
            .order_by(*order_fields)


        level_name = ''
        level_obj = None
        if queryset.exists():
            level_obj =  level
            level_name = str(level_obj.generic_level)
        subjects = list(models.LevelSubject.objects.for_school(
            user, level_name, education=term_obj.education, year=year
        ).select_related('subject').only('order', 'subject__code').order_by('order'))
        header = None

        if term_obj.education ==  main_utils.EDUCATION_FRENCH:
            header = ('matricule', 'nom', 'prenoms', 'niveau')
        else:
            header = ('matricule', 'nom_prenoms', 'niveau')
        for subject in subjects:
            header += subject.subject.code,

        term_code = f'{term_obj.term.code}'
        header += 'total', term_code, 'rang',

        sheet_data = [header]
        for enrollment in queryset:
            data = enrollment.student.last_name or '', enrollment.student.first_name or '',
            if term_obj.education == main_utils.EDUCATION_ARABIC:
                data = enrollment.student.full_name_ar or str(enrollment),
            row = (
                    enrollment.student.student_id or \
                    enrollment.student.identifier,
                    *data,
                    level_name,
                )
            if bool(enrollment.grade_exists):
                for grade in enrollment.filtered_grades:
                    row += grade.grade,
                row += enrollment.result_total or 0, \
                        enrollment.result_average or 0, \
                        enrollment.result_rank or 0,
            sheet_data.append(row)

        for row_index, (id, name, *grades) in enumerate(sheet_data):
            sheet.cell(row=row_index + 1, column=1, value=id)
            sheet.cell(row=row_index + 1, column=2, value=name)
            for col, grade in enumerate(grades):
                grade_value = grade if grade or grade == 0 else ''
                sheet.cell(row=row_index + 1, column=3 + col, value=grade_value)

        response = HttpResponse(
                content_type="application/vnd.openxmlformats-officedocument" + \
                ".spreadsheetml.sheet")
        term_obj_name = str(term_obj)
        level_obj_name = str(level_obj)
        response['Content-Disposition'] = f'attachment; filename="MOYENNES_{term_obj_name}_{level_obj_name}.xlsx"'
        workbook.save(response)
        return response


@decorators.permission_required('school.can_manage_level_grades')
def grade_by_subject_import_view(request):
    template_name = 'partials/grade/grade_base.html'
    context = {}
    year = get_session_year(request)

    if request.method == 'POST':
        file = request.FILES['file']
        workbook = openpyxl.load_workbook(file)
        sheet = workbook.active
        cols = {
            'matricule': -1,
            'note': -1
        }
        if sheet['A1'].value == 'matricule':
            cols['matricule'] = 0
        if sheet['B1'].value == 'note':
            cols['note'] = 1
        elif sheet['C1'].value == 'note':
            cols['note'] = 2

        if -1 in cols.values():
            sweetify.error(request, "Entête incorrecte",
                text='Entête incorrecte. Veuillez vérifier les entêtes svp.',
                persistent=True)
        else:
            generic_level_id = request.POST.get('short_name')
            level_id = request.POST.get('level')
            term_id = request.POST.get('term')
            subject_id = request.POST.get('subject')
            generic_level = school_models.GenericLevel.objects.get(id=generic_level_id)
            level = school_models.Level.objects.get(id=level_id)
            education = request.GET.get('lang')[0]
            term = models.SchoolTerm.objects.for_year(
                year, request.user, generic_level, education=education)\
                .get(id=term_id)
            subject = models.LevelSubject.objects.for_school(
                request.user, generic_level.short_name, education, year=year)\
                .get(id=subject_id)

            import_grades_for_subject(
                sheet, cols, request.user, year=year,
                subject=subject, term=term, level=level)
            sweetify.success(request, "Importation terminée",
                text="L'importation s'est terminée avec succès",
                persistent=True)

            lang = request.GET.get('lang')
            url = f"{reverse('exams:subject_grade_import')}?lang={lang}"
            return HttpResponseRedirect(url)


    if not bool(request.htmx):
        context['template_name'] = template_name
        template_name = 'full_template.html'
    else:
        context['template_name'] = template_name

    lang = request.GET.get('lang')
    context['lang'] = lang
    context['partial_template'] = 'partials/grade/import/import_by_subject.html'
    context['active_nav'] = 'importation_matiere'
    context['by_subject'] = True
    context['generic_levels'] = school_models.GenericLevel.objects.for_school(
        request.user, year=year
    )
    context['nav_items'] = navs.get_grades_navs(
            request.user,
            reverse('exams:grades'), lang,
            reverse('exams:subject_grade_import'),
            reverse('exams:all_grades_import'),
            reverse('exams:results_update'),
            reverse('exams:lock_marks'))
    return render(request, template_name, context)


def import_grades_for_subject(sheet, cols, user, year, subject, term, level):
    grades_dict = {}
    objs_to_create = []
    objs_to_update = []

    for row in sheet.iter_rows(min_row=2, values_only=True):
        student_id = row[cols['matricule']]
        grade = row[cols['note']]
        grades_dict[student_id] = grade
    enrollment_qs = school_models.Enrollment.objects.for_user(
                    user, year=year
                ).select_related('student').filter(
                    Q(student__student_id__in=grades_dict.keys()) | \
                    Q(student__identifier__in=grades_dict.keys())) \
                .prefetch_related('grade_set')

    if enrollment_qs.exists():
        for enrollment in enrollment_qs:
            student = enrollment.student
            student_id = student.student_id or student.identifier

            if student_id in grades_dict:
                grade = grades_dict[student_id]
                data_valid = (
                    type(grade) == int \
                    or type(grade) == float \
                    or grade == '')
                if not data_valid:
                    continue

                qs = enrollment.grade_set.filter(
                    enrollment=enrollment,
                    school_term=term, subject=subject
                )
                if qs.exists():
                    obj = qs.first()
                    obj.grade = grades_dict[student_id]
                    objs_to_update.append(obj)
                else:
                    obj = models.Grade(
                        grade=grade, updated_by=user,
                        enrollment=enrollment,
                        school_term=term, subject=subject
                    )
                    objs_to_create.append(obj)
        models.Grade.objects.bulk_create(objs_to_create)
        models.Grade.objects.bulk_update(
            objs_to_update, fields=['grade'])
        update_level_term_result(user, level, term)

@decorators.permission_required('exams.manage_all_grades')
def all_grades_import_view(request):
    template_name = 'partials/grade/grade_base.html'
    year = get_session_year(request)
    context = {}

    if request.method == 'POST':
        file = request.FILES['file']
        workbook = openpyxl.load_workbook(file)
        sheet = workbook.active
        cols = {'matricule': 0}
        lang = request.GET.get('lang')
        level_id = request.POST.get('level')
        generic_level_id = request.POST.get('short_name')
        term_id = request.POST.get('term')
        education = str(request.GET.get('lang'))[0].upper()
        generic_level = school_models.GenericLevel.objects.get(id=generic_level_id)
        level = school_models.Level.objects.for_user(
            user=request.user, year=year, education=education
        ).get(id=level_id)
        term = models.SchoolTerm.objects.for_year(
            year, request.user, generic_level, education=education)\
            .get(id=term_id)

        level_sujects = list(models.LevelSubject.objects.for_school(
            user=request.user, level_name=generic_level.short_name,
            education=lang, year=year
        ))
        for subject in level_sujects:
            cols[subject.subject.code] = -1
        data = sheet.iter_cols(min_col=2, values_only=True)
        for index, col in enumerate(data):
            header = col[0]
            if header in cols:
                cols[header] = index + 1

        # Check headers
        if -1 in cols.values():
            print(data)
            sweetify.error(request, "Entête incorrecte",
                text='Entête incorrecte. Veuillez vérifier les entêtes svp.',
                persistent=True)
        else:
            import_grades_for_level(
                request, sheet, cols, user=request.user, year=year, term=term,
                level=level, subjects=level_sujects)

            update_level_term_result(request.user, level, term)

            try:
                generate_reports.delay(
                    user_id=request.user.id,
                    year_id=year.id,
                    education=level.education,
                    term_id=term.term_id,
                    level_id=level.id
                    )
            except:
                pass

            try:
                generate_reports.delay(
                    user_id=request.user.id,
                    year_id=year.id,
                    education=level.education,
                    term_id=term.term_id,
                    level_id=level.id,
                    file_type=school_models.PDFFile.CATEGORY_REPORT_WITH_PREVIOUS_TERMS_DATA)
            except:
                pass

            url = f"{reverse('exams:all_grades_import')}?lang={lang}&success_msg"
            return HttpResponseClientRedirect(url)


    if not bool(request.htmx):
        context['template_name'] = template_name
        template_name = 'full_template.html'
    else:
        context['template_name'] = template_name

    lang = request.GET.get('lang')
    context['lang'] = lang
    context['partial_template'] = 'partials/grade/import/import_by_subject.html'
    context['active_nav'] = 'importation'
    context['generic_levels'] = school_models.GenericLevel.objects.for_school(
        request.user, year=year
    )

    if 'success_msg' in request.GET:
        context['success_msg'] = f"L'importation s'est terminée avec succès"

    context['nav_items'] = navs.get_grades_navs(
            request.user,
            reverse('exams:grades'), lang,
            reverse('exams:subject_grade_import'),
            reverse('exams:all_grades_import'),
            reverse('exams:results_update'),
            reverse('exams:lock_marks'))

    text = ''
    if request.user.school.education == main_utils.EDUCATION_ARABIC:
        text = 'Arabe' if str(lang)[0].upper() == main_utils.EDUCATION_ARABIC else 'Français'
        context['title'] = f'Gestion des notes {text}'
        context['subtitle'] = f'Gestion, exportations, importations des notes et calculs des moyennes'
    return render(request, template_name, context)


@decorators.permission_required('exams.manage_all_grades')
def import_grades_for_level(request, sheet, cols, user, year, term,
    level, subjects):
    grades_dict = {}
    subjects_dict = {}
    subjects_obj = {subject.subject.code: subject for subject in subjects}
    students_ids = []
    objs_to_create = []
    objs_to_update = []

    for key in cols:
        if key != 'matricule' and key != 'nom':
            subjects_dict[key] = cols[key]

    for row in sheet.iter_rows(min_row=2, values_only=True):
        student_id = row[cols['matricule']]
        students_ids.append(student_id)

        grade = None
        grades = {}
        for item in subjects_dict.keys():
            grade = row[cols[item]]
            grades[item] = grade
        grades_dict[student_id] = grades

    enrollment_qs = school_models.Enrollment.objects.for_user_minimum(
                    user, year=year
                ).filter(
                    Q(student__student_id__in=students_ids) | \
                    Q(student__identifier__in=students_ids)) \
                .prefetch_related('grade_set')

    for subject in subjects:
        subject_id = f'subject{subject.id}'
        enrollment_qs = enrollment_qs.annotate(
            **{subject_id: Count('grade',
                                 filter=Q(grade__school_term=term) & \
                                        Q(grade__subject=subject))}
        )

    if enrollment_qs.exists():
        for enrollment in enrollment_qs:
            student = enrollment.student
            student_id = student.student_id or student.identifier

            if student_id in grades_dict:
                grades = grades_dict[student_id]
                for subject in grades:
                    grade = grades[subject]
                    data_valid = (type(grade) == int or type(grade) == float or grade == '' or not grade)
                    if not data_valid:
                        continue

                    subject = subjects_obj[subject]
                    result_exists = bool(enrollment.__dict__.get(f'subject{subject.id}'))

                    qs = enrollment.grade_set.filter(
                        school_term=term,
                        subject=subject
                    )
                    if result_exists:
                        obj = qs.first()

                        if obj:
                            obj.grade = grade
                            objs_to_update.append(obj)
                    else:
                        obj = models.Grade(
                            grade=grade, updated_by=user,
                            enrollment=enrollment,
                            school_term=term, subject=subject
                        )
                        objs_to_create.append(obj)
        models.Grade.objects.bulk_create(objs_to_create)
        models.Grade.objects.bulk_update(
            objs_to_update, fields=['grade'])


class ResultsNavView(
    mixins.PermissionRequiredMixin, BaseHTMXRequestsView,
    generic.TemplateView):
    template_name = 'partials/result/result_base.html'
    permission_required = 'exams.manage_all_grades'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        lang = self.request.GET.get('lang').upper()[0]
        list_type = self.request.GET.get('type')
        if list_type == 'periode':
            partial_template ='partials/result/result_by_period.html'
        else:
            partial_template ='partials/result/result_for_year.html'

        context['partial_template'] = partial_template

        if list_type == 'periode':
            context['nav_items'] = navs.get_report_navs(
                reverse('exams:period_results'),
                lang)
        else:
            context['nav_items'] = navs.get_results_navs(
                reverse('exams:results_by_cycle'),
                lang)
        context['active_nav'] = list_type
        context['type'] = list_type
        context['lang'] = self.request.GET.get('lang')
        context['generic_levels'] = school_models.GenericLevel.objects\
            .for_school(self.request.user, self.get_year())
        return context


@decorators.permission_required('exams.manage_all_grades')
def period_results(request):
    term_id = request.GET.get('term')
    level_id = request.GET.get('level')
    education = str(request.GET.get('lang')).upper()[0]
    year = get_session_year(request)

    level = school_models.Level.objects.for_user(
        user=request.user, year=year, education=education
    ).get(id=level_id)
    term = models.SchoolTerm.objects.for_year(
        year=year, user=request.user,
        level=level.generic_level,
        education=education
    ).get(id=term_id)
    level_students = school_models.Enrollment.objects.for_user(
        user=request.user, year=year
    )

    if education == main_utils.EDUCATION_FRENCH:
        level_students = level_students.filter(level_fr=level)
    else:
        level_students = level_students.filter(level_ar=level)

    results = models.TermResult.objects.filter(
            enrollment__in=level_students,
            school_term=term
        ).select_related('enrollment__student')

    ordering = request.GET.get('ordering')
    if ordering == '0':
        results = results.order_by(
            'enrollment__student__last_name',
            'enrollment__student__first_name')
    else:
        results = results.order_by(
            'rank',
            'enrollment__student__last_name',
            'enrollment__student__first_name')

    return render(
        request,
        'partials/result/result_by_period_table.html',
        {'queryset': results, 'term': term_id}
    )


class LevelActionListView(mixins.LoginRequiredMixin,
                     BaseHTMXRequestsView, generic.ListView):
    model = school_models.Level
    template_name = 'partials/navs_base.html'

    def get_queryset(self):
        user = self.request.user
        education = self.request.GET.get('education', 'fr')
        education_key = education[0].upper()
        doc_type = self.request.GET.get('doc_type', 'register')
        file_types = {
            # 'register': school_models.PDFFile.CATEGORY_REGISTER,
            # 'daily_register': school_models.PDFFile.CATEGORY_REGISTER,
            # 'marking-sheet': school_models.PDFFile.CATEGORY_MARKING_SHEET,
            'fiche_table': school_models.PDFFile.CATEGORY_FICHE_TABLE,
        }
        file_type = file_types.get(doc_type)
        queryset = school_models.Level.objects.for_user(
            user=user, education=education_key, year=self.get_year(),
            with_files=bool(file_type), file_type=file_type)
        related_field_name = 'enrollment'
        if education_key == main_utils.EDUCATION_ARABIC:
            related_field_name += '_ar'
        queryset = queryset.annotate(
            students=Count(related_field_name, distinct=True),
            remaining=Case(
                When(max__gt=Count(related_field_name, distinct=True),
                    then=F('max') - Count(related_field_name, distinct=True)),
                    default=Value(0), output_field=IntegerField()
                )
            ).order_by('generic_level', 'number')

        if doc_type == 'fiche_table':
            queryset = queryset.annotate(range_of_14=ExpressionWrapper(Ceil(Coalesce(F('students'), 0) / 14), output_field=IntegerField()))

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        education = self.request.GET.get('education')
        user = self.request.user
        url = reverse('exams:level_action')
        level_id = self.request.GET.get('level')
        doc_type = self.request.GET.get('doc_type', 'register')
        template_name = 'partials/level/level_action.html'

        title=None
        if doc_type == 'register':
            context['title'] = "Listes d'appel"
            context['subtitle'] = "Listes d'appel par classe"
        if doc_type == 'daily_register':
            context['title'] = "Listes d'appel - Modèle journalier"
            context['subtitle'] = "Listes d'appel par classe"
        elif doc_type == 'marking-sheet':
            context['title'] = "Fiches de Notation"
            context['subtitle'] = "Fiches de Notation"
            title = 'Fiches de notation'
            if education == 'ar':
                context['has_template2'] = True
                context['action_name2'] = 'Modèle 2'

        elif doc_type == 'fiche_table':
            context['title'] = "Fiches de table"
            context['subtitle'] = "Fiches de table par classe"
            context['update_action'] = 'exams:generate_fiche_table'
            template_name = 'partials/level/fiche_table.html'

        if doc_type == 'marking-sheet' or doc_type == 'fiche_table':
            context['nav_items'] = navs.get_levels_actions_navs(
                user=user, url=url, doc_type=doc_type,
                education=education, title=title)
        else:
            context['nav_items'] = navs.get_levels_actions_navs(
                user=user, url=url, doc_type=doc_type, title=title)

        context['partial_template'] = template_name
        education = self.request.GET.get('education', 'fr')
        context['active_nav'] = education
        context['action_url'] = f"{reverse('exams:level_documents')}"
        context['doc_type'] = doc_type
        context['action_name'] = 'Imprimer'
        context['icon'] = 'file-text'
        return context

    def handle_no_permission(self):
        return HttpResponse(status=403, headers={'HX-Trigger': 'permission_denied'})


@decorators.login_required()
def level_document_pdf(request):
    year = get_session_year(request)
    level_id = request.GET.get('level')
    doc_type = request.GET.get('doc_type')
    level = school_models.Level.objects.for_user(
        user=request.user, year=year, with_education=False)\
        .get(id=level_id)
    queryset = school_models.Enrollment.objects.for_user_minimum(
        user=request.user, year=year
    ).select_related('student') \
    .filter(Q(level_ar=level) | Q(level_fr=level))
    if level.education == main_utils.EDUCATION_FRENCH:
        queryset = queryset.order_by(
            'student__last_name', 'student__first_name')
    else:
        queryset = queryset.order_by('student__full_name_ar')
    if doc_type == 'register':
        doc = reports.RegisterPDF(orientation='L')
        doc.add_page()
        doc.add_header(request.user.school)
        doc.add_content(queryset, level,True)
        return doc.get_file_response("Liste d'appel " + str(level) + " " + \
                                     str(level.get_education_display()) + \
                                        f' [{request.user.id}]')
    elif doc_type == 'daily_register':
        doc = reports.DailyRegisterPDF(orientation='L')
        doc.add_page()
        doc.add_header(request.user.school)
        doc.add_content(queryset, level,True)
        return doc.get_file_response("Liste d'appel " + str(level) + " " + \
                                     str(level.get_education_display()) + \
                                        f' [{request.user.id}]')
    elif doc_type == 'marking-sheet':
        return reports.generate_marking_sheet(request.user, level, queryset, template=1 if not 'template2' in request.GET else 2)
    elif doc_type == 'fiche_table':
        PER_PAGE = 14
        lot = int(request.GET.get('lot'))
        filename = "Fiches de table " + str(level) + " " + \
                    str(level.get_education_display()) + \
                    f" Lot {lot}" + \
                    f' [{request.user.school_id}]'

        paginator = Paginator(queryset, PER_PAGE)
        page = paginator.get_page(lot)
        doc = reports.FicheTable()
        start_number = 1
        if lot > 1:
            start_number = PER_PAGE * (lot - 1 ) + 1
        doc.add_content(queryset=page.object_list, level=level, start_number=start_number)
        return doc.get_file_response(filename)

def level_documents_view(request):
    pass


@decorators.permission_required('exams.manage_all_grades')
def compute_period_results_view(request, level_id=None, term_id=None):
    user = request.user
    year = get_session_year(request)

    if not level_id:
        level_id = request.POST.get('level')
        term_id = request.POST.get('term')
    level = school_models.Level.objects.for_user(
        user=user, year=year, with_education=False).get(id=level_id)
    term = models.SchoolTerm.objects.for_year(
        year=year, user=user, level=level.generic_level,
        education=level.education).get(id=term_id)

    update_level_term_result(user, level, term)

    # Generate the two report models
    try:
        generate_reports.delay(
            user_id=user.id,
            year_id=year.id,
            education=term.education,
            term_id=term.term_id,
            level_id=level.id
            )
    except:
        pass

    try:
        generate_reports.delay(
            user_id=request.user.id,
            year_id=year.id,
            education=level.education,
            term_id=term.term_id,
            level_id=level.id,
            file_type=school_models.PDFFile.CATEGORY_REPORT_WITH_PREVIOUS_TERMS_DATA)
    except:
        pass

    return HttpResponse(status=204, headers={'HX-Trigger': 'saved'})


@decorators.permission_required('exams.manage_all_grades')
def student_report_view(request):
    enrollment_id = request.GET.get('eleve')
    term_id = request.GET.get('periode')
    level_id = request.GET.get('classe', None)
    term = models.SchoolTerm.objects.get(id=term_id)
    year = term.year
    report_type = request.GET.get('report_type') or \
        school_models.PDFFile.CATEGORY_REPORT

    queryset = annotate_enrollments_with_students_infos(
        request.user, term=term, year=year)

    level = None

    if enrollment_id:
        queryset = queryset.filter(pk=enrollment_id)
    elif level_id:
        level = school_models.Level.objects.get(
            year=year, id=level_id
        )

        if level.education == main_utils.EDUCATION_FRENCH:
            queryset = queryset.filter(level_fr=level)
        else:
            queryset = queryset.filter(level_ar=level)

    # If level is not defined use first student's level
    if term.education == main_utils.EDUCATION_FRENCH and not level:
        level = queryset.first().level_fr
    elif not level:
        level = queryset.first().level_ar

    term_is_first = is_first_term(user=request.user, level=level, term=term)
    term_is_last = is_last_term(user=request.user, level=level, term=term)
    queryset = annotate_enrollment_qs_with_term_grades_data(queryset=queryset,
                    user=request.user, term=term, level=level,
                    report_type=report_type, term_is_first=term_is_first,
                    term_is_last=term_is_last)
    # queryset = queryset.prefetch_related(
    #     Prefetch('grade_set', queryset=models.Grade.objects.filter(school_term=term, subject__active=True) \
    #         .select_related('school_term', 'subject__subject') \
    #         .order_by('subject__order')\
    #         .only(
    #             'enrollment__id', 'grade', 'subject__order',
    #             'subject__coefficient',
    #             'subject__subject__id',
    #             'subject__subject__abbreviation',
    #             'subject__subject__translation',
    #             'subject__subject__category',
    #             'subject__subject__name',
    #             'school_term__id'),
    #     to_attr='filtered_grades')
    # ).filter(active=True) \
    # .annotate(
    #     result_total=Subquery(
    #         models.TermResult.objects.filter(
    #             enrollment=OuterRef('pk'), school_term=term
    #         ).values('total')[:1]
    #     ),
    #     result_average=Subquery(
    #         models.TermResult.objects.filter(
    #             enrollment=OuterRef('pk'), school_term=term
    #         ).values('average')[:1]
    #     ),
    #     result_rank=Subquery(
    #         models.TermResult.objects.filter(
    #             enrollment=OuterRef('pk'), school_term=term
    #         ).values('rank')[:1]
    #     ),
    #     result_is_ex=Subquery(
    #         models.TermResult.objects.filter(
    #             enrollment=OuterRef('pk'), school_term=term
    #         ).values('is_ex')[:1]
    #     )
    # )

    filename = 'Bulletins '

    if queryset.count() > 1:
        filename += f'{term} {level} ({request.user.school.id})'
    else:
        filename = 'Bulletin eleve '

    if level.generic_level.cycle == main_utils.CYCLE_SECONDARY:
        if term.education == main_utils.EDUCATION_FRENCH:
            report = reports.SecondCycleReport()
            report.add_content(queryset, term, year, annual_report=term_is_last)
        elif term.education == main_utils.EDUCATION_ARABIC:
            report = reports.SecondCycleReportAr()
            report.add_content(request.user, queryset, term, level, report_type=report_type, is_last_term=term_is_last)
        return report.get_file_response(filename)
    elif level.generic_level.cycle == main_utils.CYCLE_PRIMARY:
        report = None
        if term.education == main_utils.EDUCATION_FRENCH:
            report = reports.PrimaryReportFr()
        else:
            report = reports.PrimaryReportAr()
        report.add_content(request.user, queryset, term, level, report_type=report_type, is_last_term=term_is_last)
        return report.get_file_response(filename)

    return HttpResponse(status=200)


@decorators.permission_required('exams.manage_all_grades')
def results_pdf_view(request):
    ids = []
    selected_only = True
    education = ''
    request_data = None
    level = None

    if request.method == 'POST':
        request_data = request.POST
    elif request.method == 'GET':
        request_data = request.GET

    if 'all' in request_data:
        selected_only = False
        level = school_models.Level.objects.get(pk=request.GET.get('level'))
        education=level.education

    for key, values in request_data.lists():
        if 'on' in values and str(key).isnumeric():
            ids.append(key)

    year = get_session_year(request)
    order = request_data.get('ordering')

    queryset = school_models.Enrollment.objects.for_user_minimum(
        request.user, year=year
    ).filter(active=True)

    if selected_only:
        queryset = queryset.filter(id__in=ids)
    else:
        if education == main_utils.EDUCATION_FRENCH:
            queryset = queryset.filter(level_fr=level)
        else:
            queryset = queryset.filter(level_ar=level)

    # queryset = queryset.annotate(result=Q())

    term_id = request_data['selected-term']

    term = None

    if 'generic_term' in request_data:
        generic_term= request_data.get('generic_term')
        term = models.SchoolTerm.objects.for_year(
            year=year, user=request.user,
            education=level.education,
            level=level.generic_level).filter(term__id=generic_term).first()
    else:
        term = models.SchoolTerm.objects.get(id=term_id)

    queryset = queryset.annotate(fix=Max('termresult__rank', filter=Q(termresult__school_term=term)))\
        .prefetch_related(
            Prefetch('grade_set', queryset=models.Grade.objects \
                     .filter(school_term=term, subject__active=True) \
                    .select_related('school_term', 'subject__subject') \
                    .order_by('subject__order')\
                    .only(
                        'enrollment__id', 'grade', 'subject__order',
                        'subject__subject__id',
                        'subject__subject__abbreviation',
                        'subject__subject__translation',
                        'subject__subject__name',
                        'subject__active',
                        'school_term__id'),
                to_attr='filtered_grades')
        )
    if order == 'alpha' and term.education == main_utils.EDUCATION_FRENCH:
        queryset = queryset.order_by('student__last_name', 'student__first_name')
    elif order == 'alpha' and term.education == main_utils.EDUCATION_ARABIC:
        queryset = queryset.order_by('student__full_name_ar')
    elif term.education == main_utils.EDUCATION_FRENCH:
        queryset = queryset.order_by('fix')
    elif term.education == main_utils.EDUCATION_ARABIC:
        queryset = queryset.order_by('fix')


    if not level:
        if term.education == main_utils.EDUCATION_FRENCH:
            level = queryset.first().level_fr
        else:
            level = queryset.first().level_ar

    subjects = models.LevelSubject.objects.for_school(
            request.user, level.generic_level.short_name,
            level.education, level.year
        )
    orientation = 'P'
    if term.education == main_utils.EDUCATION_FRENCH and subjects.count() > 10:
        orientation = 'L'
    elif term.education == main_utils.EDUCATION_ARABIC and subjects.count() > 7:
        orientation = 'L'

    result_pdf = None
    if term.education == main_utils.EDUCATION_FRENCH:
        result_pdf = reports.TermResultsFr(orientation=orientation)
    else:
        if 'summarized' in request_data:
            result_pdf = reports.TermResultsSummarizedAr(orientation='P')
        else:
            result_pdf = reports.TermResultsAr(orientation=orientation)

    result_pdf.add_page()
    result_pdf.add_content(request.user, queryset, term, level)

    # for query in connection.queries:
    #     print('-------', query, end='\n')
    # print(len(connection.queries), 'were executed')
    return result_pdf.get_file_response(f'Resultats {term} {level} ({request.user.id})')


@decorators.login_required()
def level_resuls_update(request):
    template_name = 'partials/grade/grade_base.html'
    lang = main_utils.get_education(request)
    year = get_session_year(request)

    if request.method == 'POST':
        term_id = request.POST.get('term')
        task_id = update_levels_results_task.delay(
            user_id=request.user.id, year_id=year.id,
            term_id=term_id, education=lang
        ).task_id
        context = {'task_id': task_id}
        return render(request, 'partials/progress.html', context)

    context = {}
    context['partial_template'] = 'partials/grade/all_grades/update.html'
    context['active_nav'] = 'calculs'
    context['lang'] = request.GET.get('lang')
    context['nav_items'] = navs.get_grades_navs(
        request.user,
        reverse('exams:grades'), lang,
        reverse('exams:subject_grade_import'),
        reverse('exams:all_grades_import'),
        reverse('exams:results_update'),
        reverse('exams:lock_marks'))

    terms_qs = get_distinct_terms_qs(request=request, education=lang)

    # Instantiate form
    form = forms.TermsForm(request.user, terms_qs)
    context['form'] = form
    # task = go_to_sleep.delay(1)
    # context['task_id'] = task.task_id
    return render(request, template_name, context)


@decorators.login_required()
def get_distinct_terms_qs(request, cycle=None, education=None):
    year = get_session_year(request)
    user = request.user

    if not education:
        education = main_utils.get_education(request)

    # Get term and cycle data
    if not cycle:
        cycle = request.user.school.cycle

    terms_qs = models.SchoolTerm.objects.filter(
            school=user.school, year=year, education=education)\
            .select_related('term', 'year')

    if cycle == main_utils.CYCLE_SECONDARY:
        terms_qs = terms_qs.filter(level__cycle=cycle)
    else:
        terms_qs = terms_qs.filter(level__cycle=main_utils.CYCLE_PRIMARY)

    terms = {}
    for term in terms_qs:
        occurence = terms.get(str(term.term.id), 0) + 1
        terms[str(term.term.id)] = occurence

        if occurence > 1:
            terms_qs = terms_qs.exclude(id=term.id)
    return terms_qs


@decorators.login_required()
def distinct_cycle_terms_view(request):
    cycle = request.GET.get('cycle')
    education = request.GET.get('lang')
    terms_qs = get_distinct_terms_qs(request, cycle=cycle, education=education)

    form = forms.TermsForm(request.user, terms_qs)
    context = {'form': form}
    return render(request, 'partials/term/cycle_terms.html', context)


@decorators.permission_required('school.can_manage_level_grades')
def grade_batch_edit_view(request):
    partial_template = 'partials/grade/all_grades/batch_edit.html'

    year = get_session_year(request)
    level = school_models.Level.objects \
        .for_user(user=request.user, year=year, with_education=False) \
        .only(
            'year__id', 'generic_level__id', 'generic_level__short_name',
            'school__id', 'number', 'generic_level__order') \
        .get(id=request.GET.get('level'))
    term = models.SchoolTerm.objects.get(
        id=request.GET.get('term'),
        school__id=request.user.school_id)

    student_id = request.GET.get('student_id')
    if 'continue' in request.GET:
        students_qs = None
        if level.education == main_utils.EDUCATION_FRENCH:
            students_qs= level.enrollment_set.order_by('student__last_name', 'student__first_name')
        else:
            students_qs = level.enrollment_ar.order_by('student__full_name_ar')
        # Filter the students qs that have no termresult for the given term
        if level.generic_level.cycle == main_utils.CYCLE_PRIMARY:
            students_qs = students_qs.filter(Q(termresult__school_term=term) & (Q(termresult__total__isnull=True) | Q(termresult__total__lt=0)))
        else:
            students_qs = students_qs.filter(Q(grade__school_term=term) & (Q(grade__grade__isnull=True) | Q(grade__grade__lt=0)))

        student_id = students_qs.first().student.identifier

    form = forms.GradeBatchEditForm(request,
           level=level, term=term,
           student_id=student_id or None,
           data=request.POST or None)

    if request.method == 'POST':
        if form.is_valid():
            form.save_grades()

            next_student = form.next_student
            if not next_student:
                sweetify.success(request, 'Notation terminée',
                                 text="La notation est terminée avec succès. Les moyennes ont été recalculées automatiquement.",
                                 persistent=True)
                if request.user.role != main_utils.ROLE_TEACHER:
                    update_level_term_result(request.user, level, term)
                    return HttpResponseClientRefresh()
                else:
                    url = f"{reverse('school:teacher_subjects_select')}?classe={level.id}"
                    return HttpResponseRedirect(url)
            # for query in connection.queries:
            #     print(query, end='\n\n\n')
            sweetify.toast(request, 'Enregistré', timer=1000)
            return HttpResponseRedirect(
                    f"{reverse('exams:grade_batch_edit')}?level={level.id}&term={term.id}&student_id={next_student}&previous={student_id}")
    context = {
        'form': form,
        'term': term,
        'level': level,
        'students_count': level.get_students_count(),
        'student_id': student_id or '',
        'previous_student': form.previous_student or request.GET.get('previous_student') or request.POST.get('previous_student'),
        'students_marked': level.enrollment_set.filter(
            termresult__school_term=term,
            termresult__total__gt = 0).count(),
    }
    return render(request, partial_template, context)


@decorators.permission_required('school.can_manage_level_grades')
def customized_marking_sheet(request):
    user = request.user
    year = get_session_year(request)
    if 'level' in request.GET or 'level' in request.POST:
        level_id = request.GET.get('level') or request.POST.get('level')
        level = school_models.Level.objects \
            .filter(school=user.school_id, year=year, pk=level_id) \
            .first()
        queryset = school_models.Enrollment.objects \
            .for_user_minimum(user, year=year).filter(active=True)
        queryset = main_utils.filter_enrollment_by_level(level, queryset)
        if level.education == main_utils.EDUCATION_FRENCH:
            queryset = queryset.order_by('student__last_name', 'student__first_name')
        else:
            queryset = queryset.order_by('student__full_name_ar')

        if request.method == 'POST':
            checked_items = main_utils.get_checked_items_list(request)
            queryset = queryset.filter(id__in=checked_items)
            return reports.generate_marking_sheet(request.user, level, queryset)

        context = {
            'enrollments': queryset
        }
        return render(request, 'partials/marking/marking_sheet_table.html', context)


    partial_template = 'partials/marking/marking_sheet.html'
    template_name = 'partials/marking/marking_base.html'

    if not bool(request.htmx):
        template_name = 'full_template.html'
    context = {
        'partial_template': partial_template,
        'template_name': 'partials/marking/marking_base.html',
        'nav_items': navs.get_custom_marking_sheet_navs(
            user, reverse('exams:custom_marking_sheet'),
            request.GET.get('lang')),
        'active_nav': request.GET.get('lang'),
        'generic_levels': school_models.GenericLevel.objects.for_school(
            request.user, year=year),
        'lang': request.GET.get('lang'),
        'page_title': 'Fiches de notation personnalisées',
    }
    return render(request, template_name, context)


def results_by_cycle_view(request):
    term = request.GET.get('term')
    education = main_utils.get_education(request)
    cycle = str(request.GET.get('type')).upper()[0]
    year = get_session_year(request)
    lang = request.GET.get('lang')

    related_field_name = 'enrollment'
    if education == main_utils.EDUCATION_ARABIC:
        related_field_name += '_ar'
    active_ft = {related_field_name + '__active': True}
    boys_ft = {related_field_name + '__student__gender': main_utils.GENDER_MALE}
    girls_ft = {related_field_name + '__student__gender': main_utils.GENDER_FEMALE}

    if term or 'term' in request.GET:
        queryset = school_models.Level.objects.for_user(
            user=request.user, year=year, education=education)\
            .filter(generic_level__cycle=cycle)\
            .annotate(
                boys_count=Count(
                    related_field_name,
                    filter=Q(**active_ft, **boys_ft),
                    distinct=True
                ),
                girls_count=Count(
                    related_field_name,
                    filter=Q(**active_ft, **girls_ft),
                    distinct=True
                ),
                students=Count(
                    related_field_name,
                    filter=Q(**active_ft),
                    distinct=True),
            )

    if (term or 'term' in request.GET) and term != 'annual':
        avg_gt_0_ft = {related_field_name + '__termresult__average__gt': 0}
        term_ft = {related_field_name + '__termresult__school_term__term__id': term}
        term_stats_ft = {'levelstatistics__term__term__id': term}
        queryset = queryset.annotate(
                marked=Count(
                    related_field_name,
                    filter=Q(**active_ft, **avg_gt_0_ft, **term_ft),
                    distinct=True
                ),
                boys_present=Max(
                    'levelstatistics__boys_present',
                    filter=Q(**term_stats_ft),
                ),
                boys_admitted = Max(
                    'levelstatistics__boys_admitted',
                    filter=Q(**term_stats_ft),
                ),
                boys_perc=Max(
                    'levelstatistics__boys_perc',
                    filter=Q(**term_stats_ft),
                ),
                girls_present=Max(
                    'levelstatistics__girls_present',
                    filter=Q(**term_stats_ft),
                ),
                girls_admitted = Max(
                    'levelstatistics__girls_admitted',
                    filter=Q(**term_stats_ft),
                ),
                girls_perc=Max(
                    'levelstatistics__girls_perc',
                    filter=Q(**term_stats_ft),
                ),
                students_present= ExpressionWrapper(
                    Coalesce(F('boys_present'), 0, output_field=IntegerField()) \
                    + Coalesce(F('girls_present'), 0, output_field=IntegerField()),
                                        output_field=IntegerField()),
                students_admitted = ExpressionWrapper(
                    Coalesce(F('boys_admitted'), 0, output_field=IntegerField()) \
                    + Coalesce(F('girls_admitted'), 0, output_field=IntegerField()),
                    output_field=IntegerField()),
                students_perc=ExpressionWrapper(
                    F('boys_admitted') /  (Coalesce(F('boys_present'), 1, output_field=IntegerField()) or 1) * 100,
                    output_field=FloatField()
                ),
                term_id=Max(
                    related_field_name + '__termresult__school_term__id',
                    filter=Q(**term_ft),

                ),
                file_id=Subquery(
                    school_models.PDFFile.objects.filter(
                        level=OuterRef('pk'),
                        term__id=term, category=school_models.PDFFile.CATEGORY_REPORT).values('id')[:1]
                ),
                is_clean=Subquery(
                    school_models.PDFFile.objects.filter(
                        level=OuterRef('pk'),
                        term__id=term, category=school_models.PDFFile.CATEGORY_REPORT).values('is_clean')[:1]
                ),
                file_id2=Subquery(
                    school_models.PDFFile.objects.filter(
                        level=OuterRef('pk'),
                        term__id=term, category=school_models.PDFFile.CATEGORY_REPORT_WITH_PREVIOUS_TERMS_DATA).values('id')[:1]
                ),
                is_clean2=Subquery(
                    school_models.PDFFile.objects.filter(
                        level=OuterRef('pk'),
                        term__id=term, category=school_models.PDFFile.CATEGORY_REPORT_WITH_PREVIOUS_TERMS_DATA).values('is_clean')[:1]
                ),
                range_of_15=ExpressionWrapper(Ceil(Coalesce(F('students'), 0, output_field=IntegerField()) / 15), output_field=IntegerField())
            ).order_by('generic_level__order', 'number')

        term_obj = models.Term.objects.get(id=term)
        context = {
            'queryset': queryset, 'term': term, 'lang':
            lang, 'is_first_term': '1' in term_obj.code
        }
        return render(request, 'partials/result/result_by_cycle_table.html', context=context)

    elif term == 'annual':
        queryset = queryset.annotate(
                boys_admitted=Subquery(
                    queryset=models.LevelStatistics.objects.filter(
                        level__id=OuterRef('pk'), term__isnull=True
                    ).values('boys_admitted')[:1]
                ),
                girls_admitted=Subquery(
                    queryset=models.LevelStatistics.objects.filter(
                        level__id=OuterRef('pk'), term__isnull=True
                    ).values('girls_admitted')[:1]
                ),
                students_admitted=Coalesce(F('boys_admitted'), 0) + Coalesce(F('girls_admitted'), 0),
                students_admitted_perc=Coalesce(F('students_admitted'), 0) / Coalesce(F('students'), 1)
            )

        aggregated = queryset.aggregate(
            boys_count_=Sum('boys_count'),
            girls_count_=Sum('girls_count'),
            students_count_=Sum('students'),
            students_admitted_=Sum('students_admitted'),
            boys_admitted_=Sum('boys_admitted'),
            girls_admitted_=Sum('girls_admitted'),
            students_admitted_perc=Coalesce(F('students_admitted_'), 0) / Coalesce(F('students_count_'), 1),
        )
        context = {'queryset': queryset, 'term': term, 'lang': lang, 'aggregated': aggregated}
        return render(request, 'partials/result/result_annual_table.html', context=context)

    partial_template = 'partials/result/result_by_cycle.html'
    template_name = 'partials/result/result_base.html'

    nav_items = navs.get_results_navs(
            reverse('exams:results_by_cycle'),
            lang, option=request.GET.get('option'))

    terms =  models.Term.objects \
        .filter(education=education) \
        .filter(Q(cycle=cycle) | Q(cycle=main_utils.CYCLE_BOTH)).distinct()

    if not bool(request.htmx):
        template_name = 'full_template.html'

    # Todo: implement result by cycle
    context = {
        'partial_template': partial_template,
        'template_name': 'partials/result/result_base.html',
        'nav_items': nav_items,
        'lang': lang,
        'active_nav': request.GET.get('type') or request.GET.get('cycle'),
        'terms': terms,
        'term': term,
        'type': request.GET.get('type'),
        'page_title': 'Résultats et Bulletins',
    }
    return render(request, template_name, context=context)


class CheriflaStudentsList(BaseHTMXRequestsView, generic.ListView):
    template_name = 'partials/cherifla/students_base.html'
    model = school_models.Enrollment
    context_object_name = 'enrollments'

    def get_queryset(self):
        lookup = {
            'cepe': 'CM2',
            'bepc': '3EME',
            'bac': 'TLE'
        }
        exam = self.request.GET.get('exam', 'cepe')
        exam = exam.lower()

        short_name = lookup[exam]
        queryset = school_models.Enrollment.objects.for_user(
            self.request.user, year=self.get_year()
            ).select_related(
            'student', 'level_fr', 'level_ar', 'generic_level_fr',
            'generic_level_ar') \
            .only(
                'student__last_name', 'student__full_name_ar',
                'active', 'student__id',
                'student__first_name', 'student__gender',
                'student__birth_day', 'student__birth_month',
                'student__birth_year', 'student__student_id',
                'student__birth_place',
                'student__photo',
                'generic_level_ar__short_name',
                'generic_level_fr__id',
                'level_fr__id', 'level_ar__id', 'cherifla'
            ).filter(generic_level_ar__short_name=short_name, active=True)
        return queryset.order_by('student__last_name', 'student__first_name')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['nav_items'] = navs.get_cherifla_navs(reverse('exams:cherifla_students'))
        context['active_nav'] = self.request.GET.get('exam')
        context['title'] = f"Candidats au {self.request.GET.get('exam').upper()}"
        context['subtitle'] = f"Liste des candidats"
        context['partial_template'] = 'partials/cherifla/students.html'
        aggregated = self.get_queryset().aggregate(
            students=Count('id'),
            cherifla=Count('id', filter=Q(cherifla=True))
        )
        context['enrollments_count'] = aggregated['students']
        context['enrollments_opened'] = aggregated['cherifla']
        context['exam'] = self.request.GET.get('exam')
        return context

@csrf_exempt
def cherifla_create_student(request):
    url = "https://www.deccherifla.ci/api/"
    qs = school_models.Enrollment.objects.for_user_minimum(
        request.user, year=get_session_year(request)
    )

    student_id = request.GET.get('student_id')
    if 'student_id' in request.GET:
        qs = qs.filter(pk=student_id)

    data = {}
    exams = {
        'CM2': 'cepe',
        '3EME': 'bepc',
        'TLE': 'bac',
    }
    for enrollment in qs:
        exam =  exams[enrollment.generic_level_ar.short_name]
        photo_url = ''
        if enrollment.student.photo:
            photo_url = enrollment.student.photo.url
        elif main_utils.is_second_cycle_fr(enrollment.level_fr) and enrollment.student.student_id:
            photo_url = enrollment.student.government_photo()
            # Check if government photo url contains data in response
        else:
            photo_url = 'https://www.ecolepro.net/static/img/avatar.jpg'

        data[str(enrollment.id)] = [
            enrollment.student.last_name,
            enrollment.student.first_name,
            enrollment.student.full_name_ar or '-',
            enrollment.student.gender,
            enrollment.student.birth_day,
            enrollment.student.birth_month,
            enrollment.student.birth_year,
            enrollment.student.birth_place,
            enrollment.student.birth_place_ar or '-',
            photo_url,
            enrollment.student.father or '-',
            enrollment.student.mother or '-',
            exam
        ]
        data['school_id'] = request.user.school.cherifla_id
    # data = {'id': 'hello'}
    rq = requests.post(url, data, verify=True)
    if rq.status_code == 200:
        obj = school_models.Enrollment.objects.get(pk=student_id)
        obj.cherifla = True
        obj.save()
        return HttpResponse(status=204, headers={'HX-Trigger': 'saved'})
    return HttpResponse(status=403, headers={'HX-Trigger': 'permission_denied'})


# def convert_to_pdf(request, template_name, context, filename):
#     template = get_template(template_name)

#     # Render the template with some context data
#     html = template.render(context)

#     # Set up file paths
#     file_path = os.path.join('media', 'pdf', filename)
#     file_dir = os.path.dirname(file_path)

#     # Ensure the directory exists
#     os.makedirs(file_dir, exist_ok=True)

#     # Generate PDF
#     with open(file_path, 'wb') as pdf_file:
#         pisa.CreatePDF(html, dest=pdf_file)

#     # Return the PDF as a FileResponse
#     with open(file_path, 'rb') as pdf:
#         response = HttpResponse(pdf.read(), content_type='application/pdf')
#         response['Content-Disposition'] = f'attachment; filename={filename}'
#         return response


def generate_fiche_table_view(request):
    education = str(request.GET.get('education'))[0].upper()
    year = get_session_year(request)
    task_id = generate_fiche_table.delay(
        user_id=request.user.id,
        year_id=year.id,
        education=education
        ).task_id
    context = {'task_id': task_id}
    return render(request, 'partials/progress.html', context)

def generate_reports_view(request):
    education = str(request.GET.get('lang'))[0].upper()
    term = request.GET.get('term')

    year = get_session_year(request)
    task_id = generate_reports.delay(
        user_id=request.user.id,
        year_id=year.id,
        education=education,
        term_id=term
        ).task_id

    try:
        generate_reports.delay(
            user_id=request.user.id,
            year_id=year.id,
            education=education,
            term_id=term,
            file_type=school_models.PDFFile.CATEGORY_REPORT_WITH_PREVIOUS_TERMS_DATA)
    except:
        pass

    # Check if request wants modal progress (for new UI)
    if request.GET.get('modal') == 'true':
        context = {
            'task_id': task_id,
            'term': term,
            'education': education
        }
        return render(request, 'partials/progress_modal.html', context)
    else:
        # Use old progress template for backward compatibility
        context = {'task_id': task_id}
        return render(request, 'partials/progress.html', context)


def generate_all_reports_for_level_view(request):
    education = str(request.GET.get('lang'))[0].upper()
    term = request.GET.get('term')
    report_type = request.GET.get('report_type')
    level_id = request.GET.get('level')

    year = get_session_year(request)
    task_id = None

    try:
        if report_type == school_models.PDFFile.CATEGORY_REPORT:
            task_id = generate_reports.delay(
                user_id=request.user.id,
                year_id=year.id,
                education=education,
                level_id=level_id,
                term_id=term
                ).task_id
        else:
            task_id = generate_reports.delay(
                user_id=request.user.id,
                level_id=level_id,
                year_id=year.id,
                education=education,
                term_id=term,
                file_type=school_models.PDFFile.CATEGORY_REPORT_WITH_PREVIOUS_TERMS_DATA).task_id
    except:
        print("!!!!! Une erreur s'est produite. Les bulletins n'ont pu être générés.")
        task_id = None

    # Check if request wants modal progress (for new UI)
    if request.GET.get('modal') == 'true':
        context = {
            'task_id': task_id,
            'level_id': level_id,
            'term': term,
            'report_type': report_type
        }
        return render(request, 'partials/progress_modal.html', context)
    else:
        # Use old progress template for backward compatibility
        context = {'task_id': task_id}
        return render(request, 'partials/progress.html', context)


def custom_404(request, exception):
    return render(
        request, 'error-pages/error-page.html',
        status=404, context={'status_code': 404})

def custom_500(request):
    return render(
        request, 'error-pages/error-page.html',
        status=500, context={'status_code': 500})


def file_download_view(request, id):
    file = school_models.PDFFile.objects.get(pk=id)
    file_path = file.path
    if os.path.exists(file_path):
        return FileResponse(
            open(file_path, 'rb'), as_attachment=True,
            content_type='application/pdf')
    return HttpResponseRedirect(reverse('school:home'))


@decorators.login_required()
def get_generated_file_link(request):
    """
    Returns the download link for a generated file after task completion
    """
    level_id = request.GET.get('level_id')
    term_id = request.GET.get('term_id')
    report_type = request.GET.get('report_type', school_models.PDFFile.CATEGORY_REPORT)

    if not level_id or not term_id:
        return JsonResponse({'error': 'Missing parameters'}, status=400)

    try:
        # Get the generated file
        file_obj = school_models.PDFFile.objects.filter(
            level_id=level_id,
            term_id=term_id,
            category=report_type,
            is_clean=True
        ).first()

        if file_obj and os.path.exists(file_obj.path):
            download_url = reverse('exams:file_download', kwargs={'id': file_obj.id})
            return JsonResponse({
                'success': True,
                'download_url': download_url,
                'filename': os.path.basename(file_obj.path)
            })
        else:
            return JsonResponse({'success': False, 'message': 'File not ready yet'})

    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


def annual_results_pdf(request):
    level_id = request.GET.get('level')
    level = school_models.Level.objects.get(id=level_id)
    user = request.user

    education = level.education
    terms = models.SchoolTerm.objects.filter(
        school__id=level.school_id, education=education,
        year__id=level.year_id, active=True,
        level__id=level.generic_level_id)\
        .select_related('term').only('term__abbreviation')
    queryset = school_models.Enrollment.objects.for_user_minimum(user=user, year=level.year)
    queryset = main_utils.filter_enrollment_by_level(level, queryset)

    for i, term in enumerate(terms):
        queryset = queryset.annotate(
            **{
                f'avg_{i}': \
                Subquery(queryset=models.TermResult.objects \
                        .filter(school_term=term, enrollment__id=OuterRef('pk')).only('average') \
                        .values('average')[:1]
                ),
                f'is_ex_{i}': \
                Subquery(queryset=models.TermResult.objects \
                        .filter(school_term=term, enrollment__id=OuterRef('pk')).only('is_ex') \
                        .values('is_ex')[:1]
                ),
                f'rank_{i}': \
                Subquery(queryset=models.TermResult.objects \
                        .filter(school_term=term, enrollment__id=OuterRef('pk')).only('rank') \
                        .values('rank')[:1]
                ),
            }
        )
    queryset = queryset.annotate(
        **{
            f'mga': \
            Subquery(queryset=models.EducationYearResult.objects \
                    .filter(education=education, enrollment__id=OuterRef('pk')).only('average') \
                    .values('average')[:1]
            ),
            f'mga_rank': \
            Subquery(queryset=models.EducationYearResult.objects \
                    .filter(education=education, enrollment__id=OuterRef('pk')) \
                    .only('rank') \
                    .values('rank')[:1]
            ),
            f'mga_decision': \
            Subquery(queryset=models.EducationYearResult.objects \
                    .filter(education=education, enrollment__id=OuterRef('pk')) \
                    .only('decision') \
                    .values('decision')[:1]
            ),
            f'mga_is_ex': \
            Subquery(queryset=models.EducationYearResult.objects \
                    .filter(education=education, enrollment__id=OuterRef('pk')) \
                    .only('is_ex') \
                    .values('is_ex')[:1]
            ),
            f'class_average': \
            Subquery(queryset=models.EducationYearResult.objects \
                    .filter(education=education, enrollment__id=OuterRef('pk')) \
                    .only('class_average') \
                    .values('class_average')[:1]
            ),
            f'admission_average': \
            Subquery(queryset=models.EducationYearResult.objects \
                    .filter(education=education, enrollment__id=OuterRef('pk')) \
                    .only('admission_average') \
                    .values('admission_average')[:1]
            ),
        }
        )

    order = request.GET.get('ordre')
    if order and order == 'merite':
        queryset = queryset.order_by('-mga', 'student__last_name', 'student__first_name')
    else:
        queryset = queryset.order_by('student__last_name', 'student__first_name')

    doc = None
    if education == main_utils.EDUCATION_FRENCH:
        doc = reports.AnnualResultsFr(orientation='L')
        doc.add_content(level, list(terms), user=user, queryset=queryset)
    else:
        doc = reports.AnnualResultsAr(orientation='L')
        doc.add_content(level, list(terms), queryset=queryset)

    return doc.get_file_response(f"RESULTATS DE FIN D'ANNEE {level} {level.year} [{level.get_education_display()}]")


class DFATemplateView(BaseHTMXRequestsView, generic.TemplateView):
    template_name = 'partials/result/dfa_list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['generic_levels'] = school_models.GenericLevel.objects.all()
        context['education'] = self.request.GET.get('education')
        return context

class DFAListView(BaseHTMXRequestsView, generic.ListView):
    model = school_models.Enrollment
    template_name = 'partials/result/dfa_table.html'

    def get_terms(self):
        user = self.request.user
        year = get_session_year(self.request)
        level = school_models.Level.objects.get(pk=self.request.GET.get('level'))
        education = self.request.GET.get('education').upper()[0]

        return models.SchoolTerm.objects.for_year(
            year=year, user=user,
            education=education,
            level=level.generic_level) \
            .order_by('term__order')

    def get_queryset(self):
        user = self.request.user
        year = get_session_year(self.request)
        level = school_models.Level.objects.get(pk=self.request.GET.get('level'))
        queryset = school_models.Enrollment.objects \
            .for_user_minimum(user=user, year=year) \
            .prefetch_related(
                'termresult_set',
                'educationyearresult_set'
            )
        queryset = main_utils.filter_enrollment_by_level(level, queryset)

        education = self.request.GET.get('education').upper()[0]
        terms = models.SchoolTerm.objects.for_year(
            year=year, user=user,
            education=education,
            level=level.generic_level) \
            .order_by('term__order')

        for i, term in enumerate(terms):
            queryset = queryset.annotate(
                **{
                    f'term_{i + 1}': Subquery(models.TermResult.objects.filter(
                            enrollment=OuterRef('pk'),
                            school_term__term=term.term
                        ).only('average').values('average')[:1]
                    )
                }
            )

        queryset = queryset.annotate(
            mga=Subquery(models.EducationYearResult.objects.filter(
                            enrollment=OuterRef('pk'),
                            education=education
                        ).only('average').values('average')[:1]
                    ),
            dfa=Subquery(models.EducationYearResult.objects.filter(
                            enrollment=OuterRef('pk'),
                            education=education
                        ).only('decision').values('decision')[:1]
                    )
        )
        return queryset.order_by('student__last_name', 'student__first_name')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        terms = self.get_terms()
        terms_count = terms.count()

        data = {}
        queryset = self.get_queryset()
        for enrollment in queryset:
            averages = []
            for i in range(1, terms_count + 1):
                average = enrollment.__dict__.get(f'term_{i}')
                averages.append(average if average or average == 0 else '-')
            data[enrollment] = averages

        context['data'] = data
        context['level'] = self.request.GET.get('level')
        headers = []
        label = 'Trim '
        level = school_models.Level.objects.get(pk=self.request.GET.get('level'))
        if main_utils.is_primary_fr(level):
            label = 'Compo '
        for i in range(terms_count):
            headers.append(f'{label} {i + 1}')
        context['headers'] = headers
        return context


def combined_results_ar(request):
    year = get_session_year(request)
    level_id = request.GET.get('level')
    level = school_models.Level.objects.get(
        pk=level_id, school=request.user.school)

    queryset = school_models.Enrollment.objects.for_user_minimum(
        user=request.user, year=year
    )
    last_term = get_last_term(level, request.user)
    queryset = main_utils.filter_enrollment_by_level(level, queryset)
    queryset = annotate_enrollment_qs_with_term_grades_data(
        queryset, request.user, last_term, level,
        school_models.PDFFile.CATEGORY_REPORT_WITH_PREVIOUS_TERMS_DATA,
        True, False).order_by('student__full_name_ar')
    subjects = models.LevelSubject.objects.for_school(
        user=request.user, level_name=level.generic_level.short_name,
        education=level.education, year=year)
    terms = models.SchoolTerm.objects.for_year(
        year=year, user=request.user, level=level.generic_level,
        education=level.education)
    report = reports.CombinedResultsArabic()
    report.add_content(queryset, level, subjects, terms)

    return report.get_file_response(f'RESULTAT GENERAL PAR MATIERE - {str(level).upper()}')


class MarksLockView(BaseHTMXRequestsView, generic.TemplateView):
    template_name = 'partials/term/terms_base.html'

    def post(self, request, *args, **kwargs):
        context = self.get_context_data(**kwargs)
        data = request.POST
        year = get_session_year(request)
        lang = request.GET.get('lang')
        checked_items = main_utils.get_checked_items_list(request, 'term')
        models.SchoolTerm.objects.filter(
            term__id__in=checked_items,
            education=lang,
            year=year,
            school=request.user.get_school()) \
            .update(allow_marking=True)
        models.SchoolTerm.objects \
            .filter(education=lang,
                year=year,
                school=request.user.get_school()) \
            .exclude(
                term__id__in=checked_items) \
            .update(allow_marking=False)
        return HttpResponse(status=204, headers={'HX-Trigger': 'saved'})

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        lang = self.request.GET.get('lang').upper()[0]
        context['nav_items'] = navs.get_grades_navs(
            self.request.user,
            reverse('exams:grades'), lang,
            reverse('exams:subject_grade_import'),
            reverse('exams:all_grades_import'),
            reverse('exams:results_update'),
            reverse('exams:lock_marks'))
        context['active_nav'] = 'verrouillage_notes'
        context['lang'] = lang
        context['partial_template'] = 'partials/term/allow_marking.html'
        context['title'] = '(Dé)vérouillage des notes pour les enseignants ' + 'Arabe' if lang == main_utils.EDUCATION_ARABIC else ''
        context['subtitle'] = "Cette option vous permet d'autoriser ou d'empêcher" \
                              " la modification des notes par les enseignants."
        school = self.request.user.get_school()
        year = get_session_year(self.request)
        if school.cycle == main_utils.CYCLE_PRIMARY or school.cycle == main_utils.CYCLE_BOTH:
            context['terms_primary'] = models.Term.objects.filter(
                    Q(cycle=main_utils.CYCLE_PRIMARY) | Q(cycle=main_utils.CYCLE_BOTH)
                ).filter(education=lang) \
                .annotate(
                    allow_marking=Subquery(
                        models.SchoolTerm.objects.filter(
                            term__id=OuterRef('pk'),
                            school=school,
                            year=year,
                            education=lang
                    ) \
                        .only('allow_marking').values('allow_marking')[:1]
                    )
                )
        if school.cycle == main_utils.CYCLE_SECONDARY or school.cycle == main_utils.CYCLE_BOTH:
            context['terms_secondary'] = models.Term.objects.filter(
                Q(cycle=main_utils.CYCLE_SECONDARY) | Q(cycle=main_utils.CYCLE_BOTH)
            ).filter(education=lang) \
                .annotate(
                    allow_marking=Subquery(
                    models.SchoolTerm.objects.filter(
                        term__id=OuterRef('pk'),
                        school=school, year=year
                    ) \
                        .only('allow_marking').values('allow_marking')[:1]
                    )
                )

        return context


def all_grades_import_view2(request):
    lang = request.GET.get('lang')

    context = {'active_nav': 'importation', 'lang': lang}
    context['nav_items'] = navs.get_grades_navs(
        request.user,
        reverse('exams:grades'), lang,
        reverse('exams:subject_grade_import'),
        reverse('exams:all_grades_import'),
        reverse('exams:results_update'),
        reverse('exams:lock_marks'))

    template_name = 'partials/grade/grade_base.html'
    context['partial_template'] = 'partials/grade/import/import_by_subject.html'
    if not bool(request.htmx):
        context['template_name'] = template_name
        template_name = 'full_template.html'
    else:
        context['template_name'] = template_name

    if request.method == 'POST':
        uploaded_file = request.FILES['file']
        year = get_session_year(request)

        # Read file content
        file_content = uploaded_file.read()

        # Start background task
        education = request.GET.get('lang')

        task = all_grades_import_task.delay(
            user_id=request.user.id,
            year_id=year.id,
            file_content=file_content,
            file_name=uploaded_file.name,
            education=education
        )

        # Return progress template with task_id
        context = {
            'task_id': task.task_id,
            'view_name': 'all_grades_import_view2',
            'success_message': 'Importation terminée avec succès'
        }
        return render(request, 'partials/progress.html', context)

    return render(request, template_name, context)


class CertificateListView(mixins.LoginRequiredMixin,
                          BaseHTMXRequestsView, generic.ListView):
    model = school_models.Enrollment
    template_name = 'partials/navs_base.html'
    context_object_name = 'enrollments'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['nav_items'] = navs.get_certificates_navs(reverse('exams:certificates'))
        context['active_nav'] = self.request.GET.get('exam')
        context['partial_template'] = 'partials/certificate/students.html'
        context['page_title'] = f"Candidats au {self.request.GET.get('exam').upper()}"
        context['title'] = f"Candidats au {self.request.GET.get('exam').upper()}"
        context['icon'] = "users"
        context['subtitle'] = f"Listes des élèves"
        context['students_count'] = self.get_queryset().count()
        context['admitted'] = self.get_queryset().filter(admitted=True).count()
        return context

    def generate_certificate_numbers(self, queryset, exam):
        queryset = queryset.order_by('created_at')
        year = queryset.first().year.name
        objs = []
        for i, enrollment in enumerate(queryset):
            enrollment.certificate_num = exam.upper() + str(year) + '/' + str(i + 1).zfill(3)
            objs.append(enrollment)
        school_models.Enrollment.objects.bulk_update(objs, fields=['certificate_num'])

    def get_queryset(self):
        year = self.get_year()
        lookup = {
            'cepe': 'CM2',
            'bepc': '3EME',
            'bac': 'TLE'
        }
        exam = self.request.GET.get('exam', 'cepe')
        exam = exam.lower()
        short_name = lookup[exam]
        min_avg = models.SchoolTerm.objects.filter(
            year=year, school=self.request.user.school,
            level__short_name=short_name,
            education=main_utils.EDUCATION_ARABIC).first().max / 2
        qs = school_models.Enrollment.objects.for_user_minimum(
            year=year, user=self.request.user) \
            .filter(generic_level_ar__short_name=short_name) \
            .annotate(average=Subquery(models.EducationYearResult.objects.filter(
                enrollment=OuterRef('pk'), education=main_utils.EDUCATION_ARABIC) \
                    .only('average').values('average')[:1])
                ).annotate(admitted=Q(average__gte=min_avg ))
        if qs.filter(certificate_num__isnull=True).exists():
            self.generate_certificate_numbers(qs, exam)
        return qs


def certificate_pdf(request):
    student_id = request.GET.get('student_id')
    year = get_session_year(request)
    lookup = {
            'cepe': 'CM2',
            'bepc': '3EME',
            'bac': 'TLE'
        }
    exam = request.GET.get('exam', 'cepe')
    exam = exam.lower()
    short_name = lookup[exam]
    max_avg = models.SchoolTerm.objects.filter(
        year=year, school=request.user.school,
        level__short_name=short_name,
        education=main_utils.EDUCATION_ARABIC).first().max
    enrollment = school_models.Enrollment.objects.for_user(
        year=year, user=request.user) \
            .filter(pk=student_id) \
            .annotate(average=Subquery(models.EducationYearResult.objects.filter(
                enrollment=OuterRef('pk'), education=main_utils.EDUCATION_ARABIC) \
                    .only('average').values('average')[:1])
                ).filter(average__gte=(max_avg / 2))\
            .first()
    certificate = reports.Certificate(orientation='L')
    certificate.generate(enrollment=enrollment, school=enrollment.school,
                         level=enrollment.generic_level_ar, max_avg=max_avg)
    return certificate.get_file_response('diplome.pdf')